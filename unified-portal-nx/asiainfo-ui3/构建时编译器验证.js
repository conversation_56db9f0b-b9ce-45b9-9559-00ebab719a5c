/**
 * 构建时编译器验证
 * 
 * 验证webpack构建过程中实际使用的是哪个模板编译器
 */

console.log('=== 构建时编译器验证 ===');

// 检查webpack配置
console.log('\n1. 检查webpack配置:');
try {
  const fs = require('fs');
  const vueConfigContent = fs.readFileSync('./vue.config.js', 'utf8');
  
  console.log('vue.config.js中的别名配置:');
  const aliasMatch = vueConfigContent.match(/'vue-template-compiler':\s*'([^']+)'/);
  if (aliasMatch) {
    console.log('✅ 找到别名配置:', aliasMatch[1]);
  } else {
    console.log('❌ 未找到别名配置');
  }
  
} catch (error) {
  console.log('配置检查失败:', error.message);
}

// 检查实际的模块解析
console.log('\n2. 检查模块解析:');
try {
  // 在webpack环境中，这个解析可能不同
  const Module = require('module');
  const originalResolveFilename = Module._resolveFilename;
  
  let resolvedPaths = [];
  
  Module._resolveFilename = function(request, parent, isMain) {
    if (request === 'vue-template-compiler') {
      const resolved = originalResolveFilename.call(this, request, parent, isMain);
      resolvedPaths.push({
        request: request,
        resolved: resolved,
        parent: parent ? parent.filename : 'unknown'
      });
    }
    return originalResolveFilename.call(this, request, parent, isMain);
  };
  
  // 触发模块解析
  try {
    require('vue-template-compiler');
  } catch (e) {}
  
  // 恢复原始函数
  Module._resolveFilename = originalResolveFilename;
  
  console.log('模块解析路径:');
  resolvedPaths.forEach(path => {
    console.log(`- ${path.resolved}`);
    if (path.resolved.includes('vue-template-compiler-patched')) {
      console.log('✅ 解析到修复版本');
    } else {
      console.log('❌ 解析到原版本');
    }
  });
  
} catch (error) {
  console.log('模块解析检查失败:', error.message);
}

// 检查package.json中的依赖优先级
console.log('\n3. 检查依赖优先级:');
try {
  const fs = require('fs');
  const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
  
  console.log('dependencies中的相关包:');
  if (packageJson.dependencies['vue-template-compiler-patched']) {
    console.log('✅ vue-template-compiler-patched:', packageJson.dependencies['vue-template-compiler-patched']);
  }
  
  console.log('devDependencies中的相关包:');
  if (packageJson.devDependencies['vue-template-compiler']) {
    console.log('⚠️  vue-template-compiler:', packageJson.devDependencies['vue-template-compiler']);
  }
  
  // 检查node_modules中实际安装的版本
  console.log('\n实际安装的版本:');
  try {
    const originalPkg = JSON.parse(fs.readFileSync('./node_modules/vue-template-compiler/package.json', 'utf8'));
    console.log('vue-template-compiler实际版本:', originalPkg.version);
  } catch (e) {
    console.log('vue-template-compiler未安装');
  }
  
  try {
    const patchedPkg = JSON.parse(fs.readFileSync('./node_modules/vue-template-compiler-patched/package.json', 'utf8'));
    console.log('vue-template-compiler-patched实际版本:', patchedPkg.version);
  } catch (e) {
    console.log('vue-template-compiler-patched未安装');
  }
  
} catch (error) {
  console.log('依赖检查失败:', error.message);
}

// 模拟webpack构建环境
console.log('\n4. 模拟webpack构建环境:');
try {
  // 创建一个简单的webpack配置来测试别名
  const webpack = require('webpack');
  const path = require('path');
  
  const config = {
    mode: 'development',
    entry: './src/main.js',
    resolve: {
      alias: {
        'vue-template-compiler': 'vue-template-compiler-patched'
      }
    }
  };
  
  console.log('webpack别名配置:', config.resolve.alias);
  console.log('✅ webpack配置中包含正确的别名');
  
} catch (error) {
  console.log('webpack配置测试失败:', error.message);
}

// 检查构建产物
console.log('\n5. 检查构建产物:');
try {
  const fs = require('fs');
  const path = require('path');
  
  const distPath = './dist';
  if (fs.existsSync(distPath)) {
    console.log('✅ 构建产物存在');
    
    // 检查是否有构建日志或信息
    const staticJsPath = path.join(distPath, 'static', 'js');
    if (fs.existsSync(staticJsPath)) {
      const jsFiles = fs.readdirSync(staticJsPath);
      const chunkLibsFile = jsFiles.find(file => file.startsWith('chunk-libs'));
      
      if (chunkLibsFile) {
        const filePath = path.join(staticJsPath, chunkLibsFile);
        const stats = fs.statSync(filePath);
        console.log(`chunk-libs文件: ${chunkLibsFile}, 大小: ${(stats.size/1024/1024).toFixed(2)}MB`);
        
        // 检查文件内容是否包含Vue相关代码
        const content = fs.readFileSync(filePath, 'utf8');
        if (content.includes('Vue')) {
          console.log('✅ 构建产物包含Vue代码');
        }
      }
    }
  } else {
    console.log('❌ 构建产物不存在，请先运行构建');
  }
  
} catch (error) {
  console.log('构建产物检查失败:', error.message);
}

console.log('\n=== 验证结论 ===');
console.log('1. CVE-2024-9506漏洞确实存在于Vue 2.7.16中');
console.log('2. 我们的修复方案（webpack别名）在构建时应该生效');
console.log('3. 但需要确保构建过程真正使用了修复版本');
console.log('4. 建议进行一次完整的重新构建来确保修复生效');

console.log('\n推荐的验证步骤:');
console.log('1. 删除node_modules和dist目录');
console.log('2. 重新安装依赖: npm install');
console.log('3. 重新构建: npm run build:prod');
console.log('4. 检查构建过程是否使用了修复版本的编译器');
