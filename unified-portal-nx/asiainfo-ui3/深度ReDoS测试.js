/**
 * 深度ReDoS漏洞测试
 * 
 * 使用更大的攻击向量来测试真正的ReDoS漏洞
 */

console.log('=== 深度ReDoS漏洞测试 ===');

// 测试不同规模的攻击向量
const testCases = [
  { name: '小规模', size: 1000 },
  { name: '中规模', size: 10000 },
  { name: '大规模', size: 100000 },
  { name: '极大规模', size: 500000 }
];

async function testCompiler(compilerName, compiler, template, timeout = 30000) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const timeoutId = setTimeout(() => {
      resolve({
        success: false,
        duration: timeout,
        error: 'TIMEOUT',
        status: '超时'
      });
    }, timeout);
    
    try {
      const result = compiler.compile(template);
      clearTimeout(timeoutId);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      resolve({
        success: true,
        duration: duration,
        hasErrors: result.errors && result.errors.length > 0,
        status: duration > 5000 ? '性能问题' : '正常'
      });
      
    } catch (error) {
      clearTimeout(timeoutId);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      resolve({
        success: false,
        duration: duration,
        error: error.message,
        status: '异常'
      });
    }
  });
}

async function runTests() {
  try {
    const originalCompiler = require('./node_modules/vue-template-compiler');
    const patchedCompiler = require('./node_modules/vue-template-compiler-patched');
    
    console.log('\n原版本编译器版本:', originalCompiler.version || '未知');
    console.log('修复版本编译器版本:', patchedCompiler.version || '未知');
    
    for (const testCase of testCases) {
      console.log(`\n=== ${testCase.name}测试 (${testCase.size}个重复字符) ===`);
      
      const template = `<div><script>${'<'.repeat(testCase.size)}</textarea></div>`;
      
      // 测试原版本
      console.log(`测试原版本编译器...`);
      const originalResult = await testCompiler('原版本', originalCompiler, template);
      console.log(`原版本结果: ${originalResult.status}, 耗时: ${originalResult.duration}ms`);
      
      // 测试修复版本
      console.log(`测试修复版本编译器...`);
      const patchedResult = await testCompiler('修复版本', patchedCompiler, template);
      console.log(`修复版本结果: ${patchedResult.status}, 耗时: ${patchedResult.duration}ms`);
      
      // 分析结果
      if (originalResult.duration > 10000 || patchedResult.duration > 10000) {
        console.log('⚠️  检测到潜在的ReDoS问题');
      }
      
      if (originalResult.status === '超时') {
        console.log('❌ 原版本存在严重ReDoS漏洞！');
      }
      
      if (patchedResult.status === '超时') {
        console.log('❌ 修复版本仍存在ReDoS漏洞！');
      }
      
      // 如果测试规模太大导致超时，停止后续测试
      if (originalResult.status === '超时' || patchedResult.status === '超时') {
        console.log('由于超时，停止后续更大规模的测试');
        break;
      }
    }
    
  } catch (error) {
    console.log('测试失败:', error.message);
  }
}

// 测试特定的Snyk PoC
async function testSnykPoC() {
  console.log('\n=== Snyk PoC 精确复现测试 ===');
  
  try {
    const Vue = require('vue');
    
    // 使用Snyk报告中的确切PoC
    const snykTemplate = `
<div> 
   Hello, world!
   <script>${'<'.repeat(1000000)}</textarea>
</div>`;
    
    console.log('开始Snyk PoC精确复现测试...');
    console.log('警告: 这可能会导致长时间等待或系统卡顿');
    
    const startTime = Date.now();
    
    // 设置较长的超时时间
    const timeout = setTimeout(() => {
      console.log('❌ Snyk PoC测试超时！确认存在严重ReDoS漏洞');
      console.log('Vue 2.7.16确实存在CVE-2024-9506漏洞');
      process.exit(1);
    }, 60000); // 60秒超时
    
    try {
      const vm = new Vue({
        template: snykTemplate
      });
      
      clearTimeout(timeout);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`Snyk PoC测试完成，耗时: ${duration}ms`);
      
      if (duration > 30000) {
        console.log('❌ 检测到严重ReDoS漏洞！');
      } else if (duration > 5000) {
        console.log('⚠️  检测到性能问题');
      } else {
        console.log('✅ 未检测到明显问题');
      }
      
    } catch (error) {
      clearTimeout(timeout);
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log(`Snyk PoC测试异常 (${duration}ms):`, error.message);
    }
    
  } catch (error) {
    console.log('Snyk PoC测试失败:', error.message);
  }
}

// 运行所有测试
async function main() {
  await runTests();
  
  console.log('\n是否继续进行Snyk PoC精确复现测试？');
  console.log('警告: 这个测试可能会导致系统卡顿很长时间');
  console.log('如果要继续，请手动运行: node -e "require(\'./深度ReDoS测试.js\').testSnykPoC()"');
}

// 导出函数供外部调用
module.exports = { testSnykPoC };

main().catch(console.error);
