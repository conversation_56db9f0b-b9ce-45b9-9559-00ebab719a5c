'use strict'
const path = require('path')
const http = require('http')
const defaultSettings = require('./src/settings.js')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'SRD-BD 基础开发平台' // 标题
const port = process.env.port || process.env.npm_config_port || 80 // 端口
const webpack = require('webpack')

// const CompressionWebpackPlugin = require('compression-webpack-plugin')
// const productionGzipExtensions = ['js', 'css', 'png', 'svg', 'woff', 'ttf', 'eot', 'otf']

// vue.config.js 配置说明
// 官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  publicPath: process.env.NODE_ENV === 'production' ? './' : './',
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: 'dist',

  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: 'static',
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: process.env.NODE_ENV === 'development',
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  // webpack-dev-server 相关配置
  devServer: {
    host: '0.0.0.0',
    port: port,
    // 项目启动成功后，不自动打开浏览器
    open: false,
    proxy: {
      // 满意度
      '/workflowBoard': {
        // target: 'http://***********:9085',
        // target: 'http://***********:10101', // 联调网址
        // target: 'http://basenx-workflow-api.asiainfo.work:80',
        // target: 'http://nx-portal.asiainfo.work',
        target: 'http://localhost:8085',
        changeOrigin: true,
        pathRewrite: {
          '^/workflowBoard': '/workflowBoard'
        }
      },
      // 满意度
      '/proxy-satisfy': {
        // target: 'http://***********:9085',
        target: 'http://***********:9085', // 联调网址
        changeOrigin: true,
        pathRewrite: {
          '^/proxy-satisfy': ''
        }
      },
      // 权限
      [process.env.VUE_APP_BASE_API]: {
        // target: 'http://nx-portal.asiainfo.work',
        // target:'http://wangxuan.nat300.top',
        // target: 'http://***********:8085',
        target: 'http://127.0.0.1:8085',

        agent: new http.Agent({ keepAlive: true }),
        changeOrigin: true,
        pathRewrite: {
          // ['^' + process.env.VUE_APP_BASE_API]: '/'
          ['^' + process.env.VUE_APP_BASE_API]: '/nxportal'
          // ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
    },

    disableHostCheck: true
  },
  configureWebpack: {
    name: name,
    devtool: 'inline-source-map',
    resolve: {
      alias: {
        '@': resolve('src'),
        'bj_src': resolve('./src/views/bj_proatal_web'),
        // 使用修复了CVE-2024-9506的vue-template-compiler版本
        'vue-template-compiler': 'vue-template-compiler-patched'
      }
    },
    plugins: [
      new webpack.ProvidePlugin({
        $: 'jquery',
        jQuery: 'jquery',
        'windows.jQuery': 'jquery'
      })

      // new CompressionWebpackPlugin({
      //   filename: '[path].gz[query]',
      //   algorithm: 'gzip',
      //   test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
      //   threshold: 10240,
      //   minRatio: 0.8,
      //   deleteOriginalAssets: true
      // })
    ]
  },
  chainWebpack(config) {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/asse0ts/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config
      .when(process.env.NODE_ENV !== 'development',
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
              // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .optimization.splitChunks({
              chunks: 'all',
              cacheGroups: {
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  chunks: 'initial' // only package third parties that are initially dependent
                },
                elementUI: {
                  name: 'chunk-elementUI', // split elementUI into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
                },
                commons: {
                  name: 'chunk-commons',
                  test: resolve('src/components'), // can customize your rules
                  minChunks: 3, //  minimum common number
                  priority: 5,
                  reuseExistingChunk: true
                }
              }
            })
          config.optimization.runtimeChunk('single'),
          {
            from: path.resolve(__dirname, './public/robots.txt'), // 防爬虫文件
            to: './' // 到根目录下
          }
        }
      )
  },
  transpileDependencies: [
    'fuse.js',
    'highlight.js',

    /[/\\]node_modules[/\\](.+?)?element-ui(.*)[/\\]src/,
    /[/\\]node_modules[/\\](.+?)?element-ui(.*)[/\\]package/,
    /[/\\]node_modules[/\\](.+?)?f-render(.*)/,
    /[/\\]node_modules[/\\](.+?)?quill-image-drop-module(.*)/,
    /[/\\]node_modules[/\\](.+?)?vue-ele-form(.*)/,
    /[/\\]node_modules[/\\](.+?)?vue-ele-form-bmap(.*)/,
    /[/\\]node_modules[/\\](.+?)?vue-baidu-map(.*)/,
    /[/\\]node_modules[/\\](.+?)?vue-ele-upload-image(.*)/,
    /[/\\]node_modules[/\\](.+?)?vue-echarts(.*)/,
    /[/\\]node_modules[/\\](.+?)?resize-detector(.*)/,
    /[/\\]node_modules[/\\](.+?)?crypto-js(.*)/
  ]
}
