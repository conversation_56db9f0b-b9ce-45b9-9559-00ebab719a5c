<template>
  <div class="dynamic-field-form">
    <div
      v-for="(field, index) in fields"
      :key="index"
      class="field-row"
    >
      <!-- 字段名称 -->
      <div class="field-name">
        <el-input
          v-model="field.name"
          placeholder="字段名称"
          :disabled="readonly"
          @input="onFieldChange"
        />
      </div>

      <!-- 字段类型 -->
      <div class="field-type">
        <el-select
          v-model="field.type"
          placeholder="类型"
          :disabled="readonly"
          @change="onFieldChange"
        >
          <el-option
            v-for="type in fieldTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
      </div>

      <!-- 字段值 -->
      <div class="field-value">
        <!-- 文本类型 -->
        <el-input
          v-if="field.type === 'string'"
          v-model="field.value"
          placeholder="字段值"
          :disabled="readonly"
          @input="onFieldChange"
        />

        <!-- 数字类型 -->
        <el-input-number
          v-else-if="field.type === 'number'"
          v-model="field.value"
          placeholder="字段值"
          :disabled="readonly"
          style="width: 100%"
          @change="onFieldChange"
        />

        <!-- 布尔类型 -->
        <el-switch
          v-else-if="field.type === 'boolean'"
          v-model="field.value"
          :disabled="readonly"
          @change="onFieldChange"
        />

        <!-- 日期类型 -->
        <el-date-picker
          v-else-if="field.type === 'date'"
          v-model="field.value"
          type="datetime"
          placeholder="选择日期时间"
          :disabled="readonly"
          style="width: 100%"
          @change="onFieldChange"
        />

        <!-- 默认文本类型 -->
        <el-input
          v-else
          v-model="field.value"
          placeholder="字段值"
          :disabled="readonly"
          @input="onFieldChange"
        />
      </div>

      <!-- 操作按钮 -->
      <div v-if="!readonly" class="field-actions">
        <el-button
          size="mini"
          type="danger"
          icon="el-icon-delete"
          @click="removeField(index)"
        />
      </div>
    </div>

    <!-- 添加字段按钮 -->
    <div v-if="!readonly" class="add-field-btn">
      <el-button
        size="mini"
        type="primary"
        icon="el-icon-plus"
        @click="addField"
      >
        添加字段
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DynamicFieldForm',
  props: {
    // 字段数据
    value: {
      type: Object,
      default: () => ({})
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },
    // 最小字段数量
    minFields: {
      type: Number,
      default: 1
    },
    // 最大字段数量
    maxFields: {
      type: Number,
      default: 20
    }
  },
  data() {
    return {
      // 字段类型选项
      fieldTypes: [
        { label: '文本', value: 'string' },
        { label: '数字', value: 'number' },
        { label: '布尔', value: 'boolean' },
        { label: '日期', value: 'date' }
      ],
      // 字段列表
      fields: []
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.initFields(newVal)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    /**
     * 初始化字段
     */
    initFields(fieldData) {
      this.fields = []
      
      if (fieldData && typeof fieldData === 'object') {
        Object.keys(fieldData).forEach(key => {
          const value = fieldData[key]
          this.fields.push({
            name: key,
            type: this.getFieldType(value),
            value: value
          })
        })
      }
      
      // 确保至少有一个字段
      if (this.fields.length === 0 && !this.readonly) {
        this.addField()
      }
    },
    
    /**
     * 获取字段类型
     */
    getFieldType(value) {
      if (typeof value === 'number') {
        return 'number'
      } else if (typeof value === 'boolean') {
        return 'boolean'
      } else if (value instanceof Date) {
        return 'date'
      } else {
        return 'string'
      }
    },
    
    /**
     * 添加字段
     */
    addField() {
      if (this.fields.length >= this.maxFields) {
        this.$message.warning(`最多只能添加${this.maxFields}个字段`)
        return
      }
      
      this.fields.push({
        name: '',
        type: 'string',
        value: ''
      })
      
      this.onFieldChange()
    },
    
    /**
     * 删除字段
     */
    removeField(index) {
      if (this.fields.length <= this.minFields) {
        this.$message.warning(`至少需要保留${this.minFields}个字段`)
        return
      }
      
      this.fields.splice(index, 1)
      this.onFieldChange()
    },
    
    /**
     * 字段变化处理
     */
    onFieldChange() {
      const fieldData = {}
      
      this.fields.forEach(field => {
        if (field.name && field.value !== null && field.value !== undefined && field.value !== '') {
          fieldData[field.name] = this.convertFieldValue(field.value, field.type)
        }
      })
      
      this.$emit('input', fieldData)
      this.$emit('change', fieldData)
    },
    
    /**
     * 转换字段值
     */
    convertFieldValue(value, type) {
      if (value === null || value === undefined || value === '') {
        return null
      }
      
      try {
        switch (type) {
          case 'number':
            return Number(value)
          case 'boolean':
            return Boolean(value)
          case 'date':
            return value instanceof Date ? value : new Date(value)
          default:
            return String(value)
        }
      } catch (error) {
        console.warn('字段值转换失败:', error)
        return value
      }
    },
    
    /**
     * 验证字段数据
     */
    validate() {
      const errors = []
      
      // 检查重复字段名
      const fieldNames = this.fields.map(field => field.name).filter(name => name)
      const duplicateNames = fieldNames.filter((name, index) => fieldNames.indexOf(name) !== index)
      
      if (duplicateNames.length > 0) {
        errors.push(`字段名称重复: ${duplicateNames.join(', ')}`)
      }
      
      // 检查空字段名
      const emptyNameFields = this.fields.filter(field => !field.name && field.value !== '')
      if (emptyNameFields.length > 0) {
        errors.push('存在未命名的字段')
      }
      
      return {
        valid: errors.length === 0,
        errors
      }
    },
    
    /**
     * 清空所有字段
     */
    clear() {
      this.fields = []
      if (!this.readonly) {
        this.addField()
      }
      this.onFieldChange()
    },
    
    /**
     * 设置字段数据
     */
    setFieldData(fieldData) {
      this.initFields(fieldData)
    }
  }
}
</script>

<style scoped>
.dynamic-field-form {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background: #fafafa;
}

.field-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.field-row:last-child {
  margin-bottom: 0;
}

.field-name {
  flex: 0 0 200px;
}

.field-type {
  flex: 0 0 100px;
}

.field-value {
  flex: 1;
}

.field-actions {
  flex: 0 0 auto;
}

.add-field-btn {
  text-align: center;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .field-row {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }
  
  .field-name,
  .field-type,
  .field-value {
    flex: none;
  }
  
  .field-actions {
    align-self: center;
  }
}
</style>