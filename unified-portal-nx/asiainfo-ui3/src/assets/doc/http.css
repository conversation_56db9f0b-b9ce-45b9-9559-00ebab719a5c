.doc-content .doc-update-time{
    position: absolute;
    right: 10px;
    top:10px;
    line-height: 50px;
}
.doc .doc-item-section-title{
    font-size: 14px;
    font-weight: bold;
    margin:15px 0 5px 0;
}
.doc .doc-item-section{
    line-height: 25px;
}
.doc .doc-attach{
    float: left;
    width: 140px;
    height: 100px;
    overflow: hidden;
    text-align: center;
    margin-right: 10px;
    background-color: #f0f0f0;
    position: relative;
}
.doc .doc-attach i{
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2;
    font-size: 16px;
}
.doc .doc-attach img{
    max-width: 140px;
    max-height: 100px;
}
.doc .doc-attach.file{
    line-height: 30px;
    text-align: center;
    cursor: pointer;
}
.doc .doc-attach.file a{
    text-decoration: underline;
    color: #00c4ff;
}
.doc .http-environment{
    width: 100%;
}
.doc .doc-http-attach{
    position: relative;
    width: 100%;
    text-align: center;
    line-height: 100px;
    color: #482121;
    border: 1px dashed #ccc;
}
.doc .doc-http-attach input[type='file']{
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    opacity: 0;
}
.api-details-xml{
    font-family: 'Consolas';
}
.api-result-tabs{
    background: #F3F3F3;
    border: 1px solid #E5E5E5;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
}
.api-result-tabs .tab{
    color: #666;
    padding: 10px;
}
.api-result-tabs .tab.active{
    border-bottom: 2px solid orangered;
    color: #000;
}
.api-result-tabs .tab:hover{
    color: #000;
}
.api-result-headers-list{
    line-height: 25px;
    padding: 20px;
}
.api-result-box{
    position: relative;
}
.api-result-box .iconfont{
    position: absolute;
    right: 10px;
    top:0;
}
.api-result-box .icon-openwindow{
    right: 30px;
}
#api-result{
    background: #fff;
    border: 1px solid #E5E5E5;
    max-height: 600px;
    overflow-y: auto;
}
#api-result pre{
    border: none;
}
