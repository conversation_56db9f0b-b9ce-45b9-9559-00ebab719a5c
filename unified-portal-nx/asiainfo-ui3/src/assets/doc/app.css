@import "//at.alicdn.com/t/font_279250_vntpumsehs.css";
@import "../toastr/toastr.min.css";
@import "../pace/pace.min.css";
@import "button.css";

.doc .uk-tab>*>a{
    font-weight:400;
    font-size: 16px;
}

.iconfont {
    margin: 0 4px;
    font-size: inherit;
}

body {
    font: 14px/1.5 "Helvetica Neue", Helvetica, Arial, sans-serif;
    color: #333;
    background-color: #fff;
    min-width: 1255px;
}

[v-cloak] {
    display: none;
}

[class*=uk-modal-close-] {
    left: auto !important;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #fff;
}

::-webkit-scrollbar {
    width: 6px;
    background-color: #fff;
}

::-webkit-scrollbar:horizontal {
    height: 6px;
}

::-webkit-scrollbar-thumb {
    background-color: #8A8A8A;
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
}

article, aside, footer, header, main, nav, section, code {
    display: block;
}

pre, xmp, plaintext, listing {
    font: 13px/1.5 'Microsoft Yahei', tahoma, sans-serif;
}

code {
    background: #E7E7E7;
    padding: 5px;
    box-sizing: border-box;
}

li {
    list-style: none;
}

div, p {
    box-sizing: border-box;
}

img {
    border: none;
}

body ul {
    padding: 0;
    margin: 0;
}

.cb:after {
    clear: both;
    content: '';
    display: block;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.ta-c {
    text-align: center;
}

a {
    /*color: #333;*/
    text-decoration: none;
    cursor: pointer;
}

a:hover {
    color: #0f6ecd;
}

a.active {
    color: #1e87f0 !important;
}

.mc {
    width: 1200px;
    margin: 0 auto;
}

.hide {
    display: none;
}

input, textarea {
    outline: none;
}

.text {
    width: 100%;
    padding: 10px 12px;
    font-size: 14px;
    border: 1px solid #C3C3C3;
    box-sizing: border-box;
    font-family: consolas, 'Microsoft Yahei', tahoma, sans-serif
}

.text:focus {
    border-color: #1e87f0;
}

.select {
    width: 100%;
    border: 1px solid #C3C3C3;
    padding: 2px 8px;
    box-sizing: border-box;
    outline: none;
    /*-webkit-appearance: none;*/
    -webkit-border-radius: 0;
}

.line {
    width: 100%;
    height: 1px;
    background: #ddd;
}

.iconfont {
    cursor: pointer;
}

.spinner {
    margin: 100px auto;
    width: 80px;
    height: 80px;
    text-align: center;
    font-size: 10px;
    position: relative;
}

.spinner .rect1, .spinner .rect2, .spinner .rect3, .spinner .rect4, .spinner .rect5 {
    background-color: #1abc9c;
    height: 100%;
    width: 6px;
    display: inline-block;
    -webkit-animation: sk-stretchdelay 1.2s infinite ease-in-out;
    -ms-animation: sk-stretchdelay 1.2s infinite ease-in-out;
    -moz-animation: sk-stretchdelay 1.2s infinite ease-in-out;
    -o-animation: sk-stretchdelay 1.2s infinite ease-in-out;
    animation: sk-stretchdelay 1.2s infinite ease-in-out;
}

.spinner .rect2 {
    -webkit-animation-delay: -1.1s;
    -ms-animation-delay: -1.1s;
    -moz-animation-delay: -1.1s;
    -o-animation-delay: -1.1s;
    animation-delay: -1.1s;
}

.spinner .rect3 {
    -webkit-animation-delay: -1.0s;
    -ms-animation-delay: -1.0s;
    -moz-animation-delay: -1.0s;
    -o-animation-delay: -1.0s;
    animation-delay: -1.0s;
}

.spinner .rect4 {
    -webkit-animation-delay: -0.9s;
    -ms-animation-delay: -0.9s;
    -moz-animation-delay: -0.9s;
    -o-animation-delay: -0.9s;
    animation-delay: -0.9s;
}

.spinner .rect5 {
    -webkit-animation-delay: -0.8s;
    -moz-animation-delay: -0.8s;
    -ms-animation-delay: -0.8s;
    -o-animation-delay: -0.8s;
    animation-delay: -0.8s;
}

@-webkit-keyframes sk-stretchdelay {
    0%, 40%, 100% {
        -webkit-transform: scaleY(0.4);
        -moz-transform: scaleY(0.4);
        -ms-transform: scaleY(0.4);
        -o-transform: scaleY(0.4);
        transform: scaleY(0.4);
    }
    20% {
        -webkit-transform: scaleY(1);
        -moz-transform: scaleY(1);
        -ms-transform: scaleY(1);
        -o-transform: scaleY(1);
        transform: scaleY(1);
    }
}

@-ms-keyframes sk-stretchdelay {
    0%, 40%, 100% {
        -webkit-transform: scaleY(0.4);
        -moz-transform: scaleY(0.4);
        -ms-transform: scaleY(0.4);
        -o-transform: scaleY(0.4);
        transform: scaleY(0.4);
    }
    20% {
        -webkit-transform: scaleY(1);
        -moz-transform: scaleY(1);
        -ms-transform: scaleY(1);
        -o-transform: scaleY(1);
        transform: scaleY(1);
    }
}

@-moz-keyframes sk-stretchdelay {
    0%, 40%, 100% {
        -webkit-transform: scaleY(0.4);
        -moz-transform: scaleY(0.4);
        -ms-transform: scaleY(0.4);
        -o-transform: scaleY(0.4);
        transform: scaleY(0.4);
    }
    20% {
        -webkit-transform: scaleY(1);
        -moz-transform: scaleY(1);
        -ms-transform: scaleY(1);
        -o-transform: scaleY(1);
        transform: scaleY(1);
    }
}

@keyframes sk-stretchdelay {
    0%, 40%, 100% {
        -webkit-transform: scaleY(0.4);
        -moz-transform: scaleY(0.4);
        -ms-transform: scaleY(0.4);
        -o-transform: scaleY(0.4);
        transform: scaleY(0.4);
    }
    20% {
        -webkit-transform: scaleY(1);
        -moz-transform: scaleY(1);
        -ms-transform: scaleY(1);
        -o-transform: scaleY(1);
        transform: scaleY(1);
    }
}

.spinner .double-bounce1, .spinner .double-bounce2 {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #0f6ecd;
    opacity: 0.7;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-animation: sk-bounce 2.0s infinite ease-in-out;
    -o-animation: sk-bounce 2.0s infinite ease-in-out;
    -ms-animation: sk-bounce 2.0s infinite ease-in-out;
    -moz-animation: sk-bounce 2.0s infinite ease-in-out;
    animation: sk-bounce 2.0s infinite ease-in-out;
}

.spinner .double-bounce2 {
    -webkit-animation-delay: -1.0s;
    -ms-animation-delay: -1.0s;
    -moz-animation-delay: -1.0s;
    -o-animation-delay: -1.0s;
    animation-delay: -1.0s;
}

@-webkit-keyframes sk-bounce {
    0%, 100% {
        -webkit-transform: scale(0.0);
        transform: scale(0.0);
        opacity: 0.7;
    }
    50% {
        -webkit-transform: scale(1.0);
        transform: scale(1.0);
        opacity: 1
    }
}

@-ms-keyframes sk-bounce {
    0%, 100% {
        -ms-transform: scale(0.0);
    }
    50% {
        -ms-transform: scale(1.0);
        opacity: 1
    }
}

@keyframes sk-bounce {
    0%, 100% {
        transform: scale(0);
    }
    50% {
        transform: scale(1);
    }
}

.form .item {
    border-bottom: 1px solid #efefef;
    margin: 0;
    padding: 10px 0;
}

.form .item:after {
    clear: both;
    content: '';
    display: block;
}

.form .item:last-child {
    border-bottom: none;
}

.form .tip {
    margin: 5px 0;
    color: #ffa3a3;
    display: none;
}

.form .hint {
    color: #919191;
    margin-top: 5px;
}

.form .error {
    color: #ffa3a3;
}

.form .error .text {
    border-color: #ffa3a3;
}

.form .invalid {
    border-color: #ffa3a3;
}

.form .invalid ~ .tip {
    color: #ffa3a3;
    display: inherit;
}

.form .title {
    font-size: 18px;
    padding-left: 15px;
}

.form .label {
    text-align: right;
    display: inline-block;
    padding-top: 10px;
}

.form .label-content {
    padding-top: 10px;
}

.form .full-text {
    padding-top: 6px;
}

.div-table {
    width: 100%;
}

.div-table-header li {
    background-color: #fff;
    padding: 8px 10px;
    border-top: 1px solid #ccc !important;
    font-weight: 700;
}

.div-table .sortable-placeholder {
    width: 100%;
    height: 32px;
    background-color: #ddecf4;
}

.div-table .icon-drag-copy {
    cursor: move;
}

.div-table-line li {
    padding: 10px 0 10px 10px;
    box-sizing: border-box;
    border: 1px solid #e4e4e4;
    border-left: none;
    border-top: none;
    outline: none;
    position: relative;
    height: 43px;
}

.div-table-line li .value {
    overflow: hidden;
    height: 20px;
    text-overflow: ellipsis;
}
.div-table-line .full-height{
    padding: 0;
}
.div-table-line li .w-block {
    height: 30px;
    overflow-y: hidden;
}
.div-table-line li .w-block:hover {
    overflow-y: inherit;
    height: auto;
}
.div-table-line .full-height .w-block{
    padding: 10px;
}
.div-table-line .full-height .w-block:hover{
    background-color: #e1e1e1;
}

.div-table-line li .w-item {
    word-wrap: break-word;
}

.div-table-line li .hover {
    display: none;
    word-break: break-all;
    z-index: 10;
}
.div-table-line li .hover.description {
    left: 0;
    background: #f0f0f0;
    padding-left: 10px;
}

.div-table-line li:first-child {
    border-left: 1px solid #ccc;
}

.div-table-line .sub {
    padding: 0;
    border: none;
    height: auto;
}

.div-table-line .name {
    color: red;
}

.div-table.editing .input {
    overflow: hidden;
    padding: 0;
}

.div-table.editing .input:hover {
    overflow: inherit;
}

.div-table.editing .text {
    border: none;
    background: none;
    user-modify: read-write-plaintext-only;
    user-modify: read-write-plaintext-only;
    -webkit-user-modify: read-write-plaintext-only;
    -moz-user-modify: read-write-plaintext-only;
    padding: 13px 10px;
    line-height: 20px;
}

.div-table.editing .input .rich-text {
    outline: none;
    overflow-y: inherit;
}

.div-table.editing .input .rich-text:focus, .div-table.editing .input:hover .rich-text {
    position: absolute;
    z-index: 1;
    background-color:#e4e4e4;
    height:129px;
}

.div-table-line .name {
    padding-left: 10px;
    overflow: visible;
    position: relative;
}

.div-table-line .name .iconfont {
    color: #666;
    font-size: 14px;
    position: absolute;
    left: -16px;
    top: 8px;
    width: 16px;
    height: 16px;
    background-position: center;
    background-repeat: no-repeat;
}

.div-table-line .name .icon-my {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAHCAQAAACFbCRbAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAACYktHRAD/h4/MvwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAl2cEFnAAAABAAAAAcAwQJp2AAAACVJREFUCNctxjEBACAMA7BQsXgCs+UYV+K0qsIthGmY/uyVgQw83VQKohVhLVcAAAAldEVYdGRhdGU6Y3JlYXRlADIwMTQtMDktMjNUMTQ6MzM6NDgrMDg6MDBYHUu3AAAAJXRFWHRkYXRlOm1vZGlmeQAyMDE0LTA5LTIzVDE0OjMzOjQ4KzA4OjAwKUDzCwAAAABJRU5ErkJggg==");
}

.div-table-line .name .icon-my.open {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAECAQAAADoz+32AAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAACYktHRAD/h4/MvwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAl2cEFnAAAABwAAAAQAbLtItQAAACdJREFUCNdNiMENAEAIg6jDdienrQ9zOfkQUIdDWT+sgjcsIITQWQ/KugrMocu3oAAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAxNC0wOS0yM1QxNDozMzo0OCswODowMFgdS7cAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMTQtMDktMjNUMTQ6MzM6NDgrMDg6MDApQPMLAAAAAElFTkSuQmCC");
}

.div-table-line .div-table-line .name {
    padding-left: 25px;
}

.div-table-line .div-table-line .div-table-line .name {
    padding-left: 40px;
}

.div-table-line .div-table-line .div-table-line .div-table-line .name {
    padding-left: 55px;
}

.div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .name {
    padding-left: 70px;
}

.div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .name {
    padding-left: 85px;
}

.div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .name {
    padding-left: 100px;
}

.div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .name {
    padding-left: 115px;
}

.div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line.name {
    padding-left: 130px;
}

.div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .name {
    padding-left: 145px;
}

.div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line .div-table-line.name {
    padding-left: 160px;
}

.col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9 {
    float: left;
    padding-left: 15px;
}
.col-sm-12 {
    width: 100%
}

.col-sm-11 {
    width: 91.66666667%
}

.col-sm-10 {
    width: 83.33333333%
}

.col-sm-9 {
    width: 75%
}

.col-sm-8 {
    width: 66.66666667%
}

.col-sm-7 {
    width: 58.33333333%
}

.col-sm-6 {
    width: 50%
}

.col-sm-5 {
    width: 41.66666667%
}

.col-sm-4 {
    width: 33.33333333%
}

.col-sm-3 {
    width: 25%
}

.col-sm-2 {
    width: 16.66666667%
}

.col-sm-1 {
    width: 8.33333333%
}

ul[uk-tab] {
    margin-top: 10px;
    margin-bottom: 10px;
}

.uk-modal-dialog {
    margin-top: 80px !important;
}

.uk-button {
    padding: 0 15px !important;
    line-height: 28px !important;
    border-radius: 4px;
}

/*************** 登录、注册、忘记密码、找回密码 start*******************/

.login-form {
    width: 400px !important;
    margin-top: 120px !important;
}

.login-form.succeed {
    width: 600px;
}

.login-form .logo {
    margin-bottom: 30px;
}

.login-form .text {
    padding-top: 10px;
    padding-bottom: 10px;
}

.login-form [type='submit'] {
    width: 100%;
    padding: 10px 0;
    background: #0f6ecd;
    color: #fff;
}

.login-form [type='submit']:hover {
    background-color: #0061c1;
}

.login-third {
    margin: 10px 0;
}

.login-third img {
    width: 28px;
    margin-right: 2px;
}

.login-register-body .forget-succeed {
    border: 1px solid #c0ebd0;
    background-color: #f4fbf6;
    color: #47b26f;
    line-height: 30px;
    padding: 20px;
}

.lf-gotomail {
    padding: 10px 25px;
}

.lf-gotomail:hover {
    color: #fff;
}

.lr-friendly-tip {
    margin-top: 30px;
    color: #9e9e9e;
}

/*************** 登录、注册、忘记密码、找回密码 end*******************/

/****** component *******/
.x-ul {
}

.x-ul .x-li {
    padding: 0 12px;
    white-space: nowrap;
}

.x-ul.horiz:after {
    clear: both;
    content: ' ';
    display: block;
}

.x-ul.horiz > li {
    float: left;
    position: relative;
}

.x-ul .x-sub-ul {
    display: none;
    position: absolute;
    line-height: 30px;
    z-index: 10;
    background-color: #fff;
    box-shadow: 2px 2px 4px #ccc;
}

.x-ul .x-sub-ul .x-li:hover {
    background-color: #f0f0f0;
}

.x-ul li:hover .x-sub-ul {
    display: block;
}

/****/
.user-account-logo {
    width: 40px;
    height: 40px;
    border-radius: 100%;
}

.normal {
    font-size: 14px;
}

.normal h1 {
    font-size: 30px;
}

.normal h2 {
    font-size: 26px;
}

.normal h3 {
    font-size: 21px;
}

.normal h4 {
    font-size: 16px;
}

.normal li {
    list-style: inherit !important;
}

.normal p {
    margin: 0 10px;
}

.normal blockquote, .normal pre {
    background: none repeat scroll 0 0 rgba(102, 128, 153, .2);
    border-color: #D6DBDF;
    font-style: initial;
    font-size: initial;
    line-height: 25px;
    padding: 10px;
    border-left: 5px solid #D6DBDF;
}

.normal code {
    background-color: #D6DBDF;
    border: none;
    display: initial;
}

.normal pre code {
    background: none;
}