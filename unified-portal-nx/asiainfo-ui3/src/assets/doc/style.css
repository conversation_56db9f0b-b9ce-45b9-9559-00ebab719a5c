@import "button.css";

header{
}
footer{
    background: #fff;
    padding: 15px 0;
}
.col-sm-1,.col-sm-10,.col-sm-11,.col-sm-12,.col-sm-2,.col-sm-3,.col-sm-4,.col-sm-5,.col-sm-6,.col-sm-7,.col-sm-8,.col-sm-9 { float:left;padding-left: 15px } .col-sm-12 { width: 100% } .col-sm-11 { width: 91.66666667% } .col-sm-10 { width: 83.33333333% } .col-sm-9 { width: 75% } .col-sm-8 { width: 66.66666667% } .col-sm-7 { width: 58.33333333% } .col-sm-6 { width: 50% } .col-sm-5 { width: 41.66666667% } .col-sm-4 { width: 33.33333333% } .col-sm-3 { width: 25% } .col-sm-2 { width: 16.66666667% } .col-sm-1 { width: 8.33333333% }


.nav {
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.nav>li {
    position: relative;
    display: block
}

.nav>li>a {
    position: relative;
    display: block;
    padding: 10px 15px
}

.nav>li>a:focus,.nav>li>a:hover {
    text-decoration: none;
    background-color: #eee
}

.nav>li.disabled>a {
    color: #777
}

.nav>li.disabled>a:focus,.nav>li.disabled>a:hover {
    color: #777;
    text-decoration: none;
    cursor: not-allowed;
    background-color: transparent
}

.nav .open>a,.nav .open>a:focus,.nav .open>a:hover {
    background-color: #eee;
    border-color: #0f6ecd
}

.nav .nav-divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e5e5e5
}

.nav>li>a>img {
    max-width: none
}
.tabs{
    border-bottom: 1px solid #ccc;
    margin: 5px 0;
}
.tabs:after{
    clear: both;
    content: ' ';
    display: block;
}
.tabs .tab{
    float: left;
    padding: 5px 10px;
}
.tabs .tab:hover{
    color: #db4437;
}
.tabs .tab.active{
    border-bottom: 1px solid #db4437;
}
.tab-content{
    /*display: none;*/
}
.tab-content.active{
    display: block;
}
.table{
    width: 100%;
}
.table thead th{
    text-align: left;
    padding: 5px 10px;
    background: #e2e2e2;
}
.table tbody td{padding: 5px 10px;}
.table tbody tr:hover{
    background: #f7f7f7;
}

body.modal-open{
    overflow: hidden;
}

.component-loading{
    display: none;
}
body.loading .db-main{
    display: none;
}
body.loading .component-loading{
    display: block;
}



/*****************/
.api-env{
    cursor: pointer;
}
.api-env-details{
    position: absolute;
    z-index: 11;
}
.api-env-details ul.api-env-items{
    background: #fff;
    box-shadow: 0 1px 5px 2px #ccc;
}
.api-env-details .api-env-items li{
    padding: 6px 40px 7px 10px;
    cursor: pointer;
    white-space:nowrap;
}
.api-env-details li.line{
    padding-top: 0;
    padding-bottom: 0;
}
.api-env-details .api-env-items li:hover{
    background-color: #ccc;
}
.api-env-details .api-env-items li.active{
     background-color: #f0f0f0;
 }
.api-env-content{
    position: absolute;
    width: 300px;
    left: -300px;
    top:0;
    background: #fff;
    z-index: 11;
}

.api-menus{
    position: absolute;
    z-index: 15;
    width: 250px;
    background-color: #fff;
    border:1px solid #e5e5e5;
    box-shadow: 0 2px 10px rgba(0,0,0,.2);
    right: 15px;
    top: 100px;
    display: none;
}

.api-menus ul{
    padding: 15px;
}
.api-menus li{
    float: left;
    width: 33%;
    text-align: center;
}
.api-menus li a{
    padding: 10px 10px 1px 10px;
    font-size: 12px;
    display: block;
}
.api-menus li i{
    font-size: 30px;
    color: #716f6f;
}
.api-menus a:hover i{
    color: #0f6ecd;
}
.api-sanjiaoxings{
    position: absolute;
    right: 20px;
    top: -22px;
}
.api-menus .icon-sanjiaoxing{
    font-size: 25px;
    color: #fff;
    text-shadow: 0 -1px 1px #ccc;
}
/* 操作记录窗口 */
.api-history{
    position: absolute;
    z-index: 15;
    width: 300px;
    background-color: #f0f0f0;
    border:1px solid #e5e5e5;
    box-shadow: 0 2px 5px rgba(0,0,0,.2);
    right: 15px;
    top: 100px;
}
/* 操作记录窗口关闭按钮 */
.api-history .icon-close{
    padding: 14px 20px ;
    position: absolute;
    top:0;
    right: 0;  
}
.api-fn-actions{
    padding-left: 3px;
    margin-bottom: 10px;
}
.api-fn-actions>div{
    width: 49%;
    height: 30px;
    padding: 0 10px;
    background-color: #e0e0e0;
    margin-right: 2%;
    line-height: 30px;
    cursor: pointer;
}
.api-fn-actions>div:hover{
    background: #ddecf4;
    color: #0f6ecd;
}
.api-fn-actions>div:last-child{
    margin-right: 0;
}
.api-chose-list{
    padding-left: 20px!important;
}
.api-chose-list div{
    display: inline-block;
}
.api-chose-list div.div-select{
    border: 1px solid #e0e0e0;
    border-radius: 2px;
}
#ace-editor-box{
    min-height: 250px;
}

.api-chose-list div.div-select:hover{
    background-color: #e6e6e6;
    border-color: #adadad;
}
.api-chose-list select{
    padding: 3px;
    background-color: transparent;
    border: none;
    outline: none;
    height: 25px;
    line-height: 25px;
    min-width: 90px;
}
.api-modules-tab{
    position: fixed;
    z-index: 2;
    top:0;
    left: 45%;
    background:#fff;
}
.api-modules-tab a{
    border-bottom:2px solid #fff;
    line-height: 50px;
    height: 50px;
    display: inline-block;
    padding: 0 10px;
    color: #000;
}
.api-modules-tab a:hover{
    color: #db4437;
}
.api-modules-tab a.active{
    border-color: #db4437;
    color: #db4437;
}
.api-modules{
    background: #0f6ecd;
    padding-left: 10px;
}
.api-modules-container{
    max-width: 1430px;
}
.api-module{
    color: #f3f3f3;
    border-bottom:2px solid transparent;
    padding: 0 8px;
    line-height: 50px;
    height: 50px;
    position: relative;
}
.api-module i{
    margin:0;
}
.api-new-module{
    width: 200px;
    color: #757575;
    display: none;
}
.api-module-plus i{
    font-size: 20px;
}
.api-edit i{
    margin-right:4px;
    width: 14px;
    height:14px;
}
.api-module.active{
    border-bottom-color: #fff;
}
.api-module.active span{
    color: #fff;
}
.api-module span{
    cursor: pointer;
    display: inline-block;
    min-width: 20px;
    color: #fff;
}
.db-api-error{
    padding:20px;
}
.api-view-env{
    background-color: #DFDFDF;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
}
.api-view-env-items{
    position: absolute;
}
.api-view-env-items li{
    cursor: pointer;
}
.api-share{

}
.api-share .api-share-item{
    position: relative;
    padding: 10px 0;
}
.api-share .api-share-title{
    color: #0f6ecd;
    text-decoration: underline;
}
.api-share .api-share-pwd-box{
    position: absolute;
    width: 150px;
    right: 0;
    top: 0;
    display: none;
}
.api-share .api-share-cm-label{
    margin-right: 5px;
}
.apis{
}
.apis li{
    position: relative;
}
.apis li.sortable-placeholder{
    background-color: #ddecf4;
    display: block;
    width: 100%;
    height: 34px;
}
.apis li .handle{
    position: absolute;
    top: 5px;
    right: 0;
}
.api-container{
    background: #fff;
    padding: 20px 0;
}
.apis-left{
    border-right: 2px solid #eee;
    width: 250px;
    padding: 0 5px;
}
.apis-nav{
}
.apis-module-name{
    font-size:18px;
    margin: 0 0 0 20px;
}
.apis-module-host{
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
}
.apis-nav .api-name{
    /*padding: 5px 0 5px 20px;*/
    padding: 0 0 0 20px;
    line-height: 34px;
    cursor: pointer;
    border-left:3px solid #fff;
}
.apis-nav .api-name.api-folder-new{
    background: #e0e0e0;
    margin-top: 20px;
}
.apis-nav .api-name:hover{
    background: #ddecf4;
    color: #0f6ecd;
}
.apis-nav .api-name.active{
    border-left:3px solid #0f6ecd;
    background: #ddecf4;
    color: #0f6ecd;
}
.apis-nav .api-name.api-folder{
    background: url("http://img.alicdn.com/tps/i4/TB1sLIsGXXXXXb6XFXX1aiKJFXX-4-7.png") 8px 12px no-repeat;
}
.apis-nav .api-name.api-folder.open{
    background-image: url("http://img.alicdn.com/tps/i3/TB1vn3AGXXXXXbFXXXXWXbjJFXX-7-4.png");
    background-position-y:15px;
}
.apis-nav .api-name i{
    margin-left: 8px;
}
.apis-nav .api-folder span{
    display: inline-block;
    min-width: 20px;
    min-height: 20px;
}
.apis-nav-sub{
}
.apis-nav-sub .api-name{
    padding-left: 35px;
}
.apis-nav-sub .api-name span.deprecated{
    text-decoration: line-through;
}
.apis-nav-sub .api-name span{
    /*padding: 10px 0;*/
    min-width:20px;
    display: inline-block;
}
.api-module-item .api-item-actions{
    right: 11px;
}
.api-item-actions{
    position: absolute;
    padding:10px 0;
    box-shadow: 0 5px 5px #ccc;
    background-color: #fff;
    z-index: 2;
    right: 0;
    top: 35px;
    color: #4e4e4e;
    min-width: 70px;
}
.api-item-actions div{
    line-height: 20px;
    padding: 5px 10px;
    cursor: pointer;
}
.api-item-actions div i{
    font-size: 12px;
}
.api-item-actions div:hover{
    background-color: #f0f0f0;
}

.api-editing .api-module-item i.icon-bianji,.api-editing .api-module-item i.icon-close{
    display: inline-block;
}
.api-editing .api-save,.api-editing .api-cancel{
    display: list-item;
}
.api-editing .api-edit{
    display: none;
}
.api-editing #api-view-box{
    display: none;
}
.api-editing #api-edit-box{
    display: block;
}
.api-project-hint{
    position: absolute;
    right: 0;
    top:0;
}
.api-module-search{
    position: relative;
}
.api-module-search:hover .text{
    border-color: #fff;
}
.api-module-search .text{
    font-size: 12px;
    color: #fff;
    background-color: transparent;
    border-color: transparent;
    width: 150px;
}
.api-module-search i{
    position: absolute;
    right: 5px;
    top: 0;
}
.api-content .xx:after{
    content: ' ';
    display: block;
    clear: both;
}
.api-content  .apis-module-name{
    font-weight:normal;
    font-size: 24px;
    margin-left:0;
}
.editormd-html-preview, .editormd-preview-container{
    padding: 5px!important;
}
.api-content .api-update-time{
    color: #666;
    white-space:nowrap;
    top:5px;
    right: 0px;
    position: absolute;
    z-index: 1;
}
.api-details{
    position: relative;
    padding-top: 1px;
}
.api-details-title{
    font-size: 14px;
    font-weight: bold;
    margin:10px 0 8px 0;
}
.api-details-title.second{
    font-size: 13px;
    color: #999;
}
.api-content .table{
}
.api-content .table td{
    border-bottom: 1px solid #e0e0e0;
    border-left: 1px solid #e0e0e0;
    padding: 4px 10px;
    outline: none;
}
.api-content .table td:focus{
    border:1px solid #0089cd;
}
.api-content .table td.field-name{
    color:#d45454;
}
.api-content .table td:last-child{
    border-right: 1px solid #e0e0e0;
}
.api-example{
    width: 100%;
    height:150px;
    padding: 5px;
    box-sizing: border-box;
}
.api-base-info p{
    line-height: 25px;
}
#api-edit-details{
    position: relative;
    padding-bottom: 50px;
}

#api-edit-box{
    width: 100%;
}
#api-edit-box .apis-nav .api-name i{
    margin-left: 0;
}
.api-add-sub{
    display: none
}


.api-nodata{
    color: #9D9D9D;
    padding: 100px 0;
}
.api-nodata .iconfont{font-size: 100px}
.api-nodata .tip{font-size: 20px}
.api-nodata .new-api{
    margin-top: 50px;
}
.api-nodata .new-api a{
    background: orangered;
    color: #fff;
    padding: 8px 75px;
}
.api-nodata .new-api a:hover{
    background-color: #DF3D01;
}
.api-error-tip{
    margin-top: 100px; color: #9F9F9F;
}
.api-error-tip i{
    font-size: 80px;
}
.api-error-tip p{
    font-size: 18px;
}
.api-quick-create{
    width: 330px;
}
.api-quick-create a i{
    font-size: 36px;
}
.api-quick-create a{
    font-size: 14px;
}

.api-doc-desc .edui-container{
    z-index: 2!important;
}
.api-plugin-tip{
    margin: 40px 0;
    color: #888;
}
.api-plugin-tip i{
    font-size: 48px;
}
.api-plugin-tip p{
    margin: 5px 0;
}
.api-name .api-actions{
    margin-right: 5px;
}
.api-name .api-actions i{
    font-size: 12px;
    color: #888;
    margin-left: 0;
    margin-right: 1px;
}
.api-name .api-actions i:hover{
    color: #000;
}


/***************history******************/
/* iframe整个页面 */
.iframe-page{
    width: 300px;
    height: 100%;
    padding: 5px;
}
/* h2 */
.iframe-page h2{
    padding: 10px;
    position: fixed;
    top:0;
    width: 100%;
    height: 50px;
    background-color: #ffffff;
}
/* logs整体 */
.iframe-page .records{
    margin-top: 40px;
    margin-bottom: 50px;
}
/* 单条log */
.iframe-page .each-record{
    overflow:hidden;
    background-color: #fff;
    margin: 5px;
    padding: 6px;
    font-size: 14px;
    border-top: 1px solid #eaeaea;
}
/* 去除全局col属性中的padding-left:15px */
.iframe-page .col-sm-2{
    padding: 0;
}
/* 用户头像 */
.iframe-page .db-user-logo{
    width: 40px;
    height: 40px;
    border-radius: 100%;
    margin: 6px 8px auto 5px;
}
/* 用户昵称 */
.iframe-page .user-name{
    line-height: 2
}
/* log时间 */
.iframe-page .record-time{
    line-height: 2;
    text-align: right;
}
/* log详情 */
.iframe-page .record-detail{
    font-size: 13px;
    line-height: 1.4;
}
/* 加载动画 */
.iframe-page .loading{ 
	width: 80px; 
	height: 40px; 
	margin: 0 auto; 
	margin-top:30px; 
} 
/* 加载动画中的每一条 */
.iframe-page .loading span{ 
	display: inline-block; 
	width: 6px; 
	height: 100%; 
	border-radius: 4px; 
	background: lightgreen; 
	-webkit-animation: load 1s ease infinite;
} 
/* load效果 */
@-webkit-keyframes load{ 
	0%,100%	{ 
		height: 20px; 
		background: lightgreen; 
	} 
	50%{ 
		height: 35px; 
		margin: -8px 0; 
		background: lightblue; 
	} 
} 
/* 延迟形成波动效果 */
.iframe-page .loading span:nth-child(2){ 
	-webkit-animation-delay:0.2s; 
} 
.iframe-page .loading span:nth-child(3){ 
	-webkit-animation-delay:0.4s; 
} 
.iframe-page .loading span:nth-child(4){ 
	-webkit-animation-delay:0.6s; 
} 
.iframe-page .loading span:nth-child(5){ 
	-webkit-animation-delay:0.8s; 
}
/* 底部固定 */
.iframe-page footer{
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 45px;
    padding: 13px;
    border-top: 1px solid #eaeaea;
}
/* 刷新按钮 */
.iframe-page .icon-iconfontshuaxin{
    font-size: 20px;
    padding-left: 5px;
    padding-top: 2px;
}
/* 每页显示数量选择框 */
.iframe-page footer div{
    float: right;
    padding-top: 5px;
}
/* “没有记录啦” */
.iframe-page .bottom{
	text-align: center;
	font-size: 15px;
}

/***************DASHBOARD******************/
.dashboard .bg{
    background-color: #fff;
    /*background-color: #2e2d2b;*/
    box-shadow: 1px 0 13px 0 #A3A3A3
}
.dashboard .db-item:hover{
    background-color: #f1f1f1;
}

.db-left{
}
.db-left .logo{
    margin-top: 50px;
}
.db-left-content{
    position: fixed;
    width: 250px;
    top:0;
    left:0;
    height: 100%;
    overflow-y: auto;
    z-index: 101;
    background-color: #fff;
}
.dlc2{
    display: none;;
    left: 60px;
}
.dashboard.max .dlc1{
    display: none;
}
.dashboard.max .dlc2{
    display: block;
}
.dlc2 .db-left-content{
    left: 60px;
}
.dbl-projects{
    display: block;
}
body.profile .dbl-projects{
    display: none;
}
.dbl-userinfo{
    display: none;
}
body.profile .dbl-userinfo{
    display: block;
}
.db-left .dbl-userinfo .db-item{
    padding-left: 55px;
}
.dashboard.max .db-main{
    padding: 0;
}
.db-left-layer{
    background-color: rgba(138, 138, 138, 0.5);
    position: fixed;
    top:0;
    left:60px;
    height: 100%;
    width: 100%;
    overflow-y: auto;
    z-index: 100;
}
.db-left-bar{
    background-color: #2e2d2b;
    color: #fff;
    width: 60px;
    height: 100%;
    position: fixed;
    z-index: 99;
    left: 0;
    top: 0;
    overflow-y: auto;
}
.db-left-bar .logo img{
    width: 50px;
}
.db-left .db-left-bar .db-item{
    padding-left: 0;
}
.db-left .db-left-bar .db-item a{
    color: #a2a2a2;
}
.db-left .db-left-bar .db-item a:hover,.db-left .db-left-bar .db-item a.active{
    color: #fff;
}
.dashboard .db-left-bar .db-item:hover{
    background-color: inherit;
}
.db-left .db-left-bar .db-item i{
    font-size: 30px;
    font-weight: bold;
    margin-right: inherit;
}

.db-left-search{
    margin: 40px 0 5px 25px;
}
.db-left-search [type='text']{
    border: none;
    line-height: 30px;
    height: 30px;
    margin-left: 3px;
}
.db-left-search i{
    font-size: 20px;
    color: #757575;
}
.db-search-box{
    margin: 30px auto;
    width: 80%;
    display: block;
}
.db-search-box input{
    border-color: #E5E5E5;
}
.db-left .db-item{
    display: -webkit-flex;
    display: flex;
    padding-left: 26px;
    margin-bottom: 10px;}

.db-left .db-item a{ 
    -webkit-flex: 3;
    flex: 3;   
    height: 40px;
    width: 50%;
    line-height: 40px;
}
.db-left .db-item .shoucang{
    -webkit-flex: 1;
    flex: 1;
    display: none;
    text-align: center;
}
.db-left .db-item.active{
    background-color: #fff;
}
.db-left .db-item:hover .shoucang{
    display: block;
    color: #0f6ecd;
}
.db-left .db-item i{
    margin-right: 10px;
}
.bd-project-title{
    padding-left: 26px;
    font-size: 16px;
    margin: 30px 0 20px 0;
}
.db-right{
    margin-left: 250px
 }
.dashboard.max .db-right{
    margin-left: 60px;
}

.db-right .db-nav{
    display: block;
    border-bottom: 1px solid #e1e1e1;
}
.db-nav .db-back{
    border-right:1px solid #e1e1e1;
    margin:0 10px 0 5px;
}
.db-nav .db-item{
    position: relative;
}
.db-nav a{
    color: #666;
    display: block;
    height: 50px;
    line-height: 50px;
    padding: 0 15px;
}
.db-nav .db-item:hover{
    background-color: #F1F1F1;
}
.db-nav .page-name{
    font-weight: bold;
    font-size: 14px;
}
.db-right .db-nav a:hover{
    color: #000;
}
.db-nav .db-user-logo{
    width: 30px;
    height: 30px;
    border-radius: 100%;
    margin-right: 8px;
    margin-top: 10px;
}
.db-nav .db-msg{
    position: relative;
    padding-right: 20px;
}
.db-nav .db-msg .db-subscript{
    position: absolute;
    right: 10px;
    top: 5px;
    font-size: 12px;
    border-radius: 100%;
    background: #d43b49;
    color: #fff;
    display: block;
    height: 20px;
    width: 20px;
    padding: 0;
    text-align: center;
}

.db-item-sub{
    background: #fff;
    border: 1px solid #eaeaea;
    position: absolute;
    right: 0;
    z-index: 12;
    top: 50px;
}
.db-item-sub a{
    width: 150px;
    height: 40px;
    line-height: 40px;
}
.db-item-sub i{
    color: #0f6ecd;
    margin-right: 5px;
}
.db-item-sub .db-item{
    border-bottom: 1px solid #eaeaea;
}
.db-item-sub .db-item:last-child{
    border: none;
}
.db-item:hover .db-item-sub{
    /*display: block;*/
}
.db-nav-sub-profile{
    margin: 10px 0;
    line-height: 25px;
    width: 180px;
    overflow: hidden;
}
.db-nav-profile-name{
    font-size: 16px;
}
.db-item-sub .db-user-logo{
    height: 50px;
    width: 50px;
    margin: 10px 20px 10px 20px;
}
.db-item .db-profile-info{
    background: #fff!important;
    color: #666;
    width: 280px;
}
.db-item.profile .db-item{
    background: #f9f9f9;
}
.db-item.profile .db-item a{
    padding-left: 30px;
}
.db-nav-msg-box{
    padding: 0 10px;
    line-height: 40px;
    background: #e2e2e2;
    width: 300px;
}
.db-msg .db-item-sub{
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
}
.db-msg .db-item:not(.item-title){
    padding: 5px;
}
.db-msg .db-item:hover{
    background: none;
}
.db-msg .db-item a:hover{
    color: #0759ff;
}
.db-msg .db-item a{
    font-size: 12px;
    color: #06c;
    height: initial;
    line-height: 16px;
    width: 100%;
    padding: 5px 8px 0 8px;
    box-sizing: border-box;
}
.db-view-form{
    width: 600px;
}