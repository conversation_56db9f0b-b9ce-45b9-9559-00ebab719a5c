body{
    min-height: 100%;
}
.project-line,.line{
    width: 100%;
    height: 1px;
    background-color: #f0f0f0;
}
.home-header{
    background-color: #fff;
    height: 50px;
    position: relative;
}
.home-header img{
    height: 40px;
    margin: 5px 0;
}
.user-account-logo{
    width: 40px;
    height: 40px;
    border-radius: 100%;
}
.header-titles{
    width: 500px;
    margin: 0 auto;
}
.header-titles a{
    line-height: 50px;
    padding: 0 5px;
    color: #000;
}
.user-account span{
    margin-left: 5px;
}

.home-projects{
    width: 800px;
    margin-top: 80px;
}
.home-projects>div{
    background-color: #fff;
    padding: 0 20px;
    margin-top: 10px;
    height: 40px;
    line-height: 40px;
    box-shadow: 0px 2px 10px #ccc;
}
.home-projects .home-p-title{
    background-color: transparent;
    box-shadow: none;
}
.home-projects .p-actions .iconfont {
    font-size: 16px;
}
.home-projects .p-actions{
    text-align: right;
}
.home-projects .p-actions .uk-dropdown{
    min-width:130px!important;
    padding: 10px 20px;
    line-height: 20px;
}
.home-projects .p-actions>span{
    cursor: pointer;
}
.home-projects .uk-dropdown li span{
    margin-right: 10px;
}
.home-body .new-project-btn{
    margin: 0 auto;
    width: 200px;
    padding-top:50px;
    padding-bottom:50px;
}
.home-body .new-project-btn:hover .np-btns{
    display: block;
}
.home-body .new-project-btn-holder{
    text-align: center;
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 100%;
    color: #fff;
    background-color: #d23f31;
    font-size: 40px;
    display: inline-block;
    cursor: pointer;
}
.home-body .np-btns{
    position: absolute;
    margin-top: -110px;
    margin-left: -26px;
    background: rgba(255, 255, 255, 0.68);
    width: 100%;
    left: 0;
    display: none;
}
.home-body .new-project-btns{
    width: 200px;
    margin: 0 auto;
}
.home-body .new-project-btns .np-btns-item{
    text-align: right;
    width: 140px;
    margin: 10px 0;
    cursor: pointer;
}
.home-body .new-project-btns .np-text{
    background-color: #fff;
    padding: 3px 5px;
    box-shadow: 0px 1px 1px #999999;
    border-radius: 2px;
    color: #777;
}
.home-body .new-project-btns .nb-btn{
    display: inline-block;
    border-radius:100%;
    background-color: dodgerblue;
    color: #fff;
    padding: 8px;
}
.home-body .no-project{
    margin-top: 100px;
    color: #000000;
}
.home-body .no-project i{
    font-size: 60px;
}

/******** home end **************/


/******** project-info start **************/


.project-info{
    width: 1000px;
    margin-top: 50px;
}

.project-info .project-left{
    width: 200px;
    height: 500px;
    border-right: 1px solid #f0f0f0;
    position: absolute;
}
.project-info .project-info-content{
    margin-left: 220px;
    background-color: #fff;
    margin-top: 30px;
}
.project-menus{
    padding: 0;
}
.project-menus a{
    width: 100%;
    display: block;
    height: 40px;
    line-height: 40px;
    padding-left: 30px;
    color: #797979;
}
.project-menus a:hover{
    background-color:#f0f0f0;
}
.project-menus a.active{
    background-color: #f0f0f0;
}



/********** NEW *********/
.db-view-new{
}
.db-add-succeed{
    border: 1px solid #c0ebd0;
    color: #47b26f;
    background-color: #f4fbf6;
    padding: 20px;
    margin-right: 80px;
}
.dvn-import-members{
    margin-top: 80px;
    width: 800px;
}
.dbv-chose-users{
    margin: 0;
    padding: 0;
}
.dbv-chose-users li{
    margin: 0 30px 30px 0;
    float: left;
    text-align: center;
    cursor: pointer;
}
.dbv-chose-users li>p{
    width: 53px;
    overflow: hidden;
    word-wrap: break-word;
    white-space: nowrap;
    margin: 0;
}
.dbv-chose-users li.active .flag{
    display: block;
}
.dbv-user-icon{
    position: relative;
}
.dbv-user-icon .flag{
    width: 15px;
    height: 15px;
    border-radius: 100%;
    background: #ff5a00;
    position: absolute;
    right: 0;
    bottom: 16px;
    border: 2px solid #fff;
    display: none;
}
.dbv-chose-users img.active{
    border-color: red;
}
.dbv-chose-users .img{
    width: 50px;
    height:50px;
    border-radius: 100%;
    margin-bottom: 3px;
    border: 1px solid #fff;
}
.dbv-chose-users .word{
    background-color: #0f6ecd;
    color: #fff;
    line-height: 50px;
    overflow: hidden;
}
.dvn-new-users{
    margin-top: 10px;
    color: #9B9B9B;
    font-size: 16px;
}
.dvn-new-users li{
    float: left;
    margin-right: 10px;
    border-radius: 100%;
    width: 50px;
    height:50px;
    line-height: 50px;
    background-color: #4DBAD3;
    color: #fff;
    text-align: center;
    margin-bottom: 8px;
}



/*****************/
.db-export{
    width: 600px;
}
.db-export li{
    width: 100px;
    height: 100px;
    text-align: center;
    cursor:pointer;
    float: left;
    font-size: 13px;
}
.db-export li img{
    width: 32px;
    height: 32px;
}

/**********************************************/
.db-members{
    position: relative;
}
.db-m-list{
    width: 1000px;
}
.db-m-list li{
    border-bottom: 1px solid #f0f0f0;
    padding: 15px 0;
    line-height: 50px;
}
.db-m-list li:last-child{
    border-bottom: none;
}
.db-m-list li .user-logo{
    border-radius: 100%;
    display: block;
    width: 50px;
    height: 50px;
}


/*************************/
.db-relation .db-rel-desc{
    line-height: 24px;
}
.db-relation ul{
    margin-top: 30px;
}
.db-relation li{
    float: left;
    width: 220px;
    margin-right: 10px;
    border:1px solid #E7E7E7;
    padding: 30px 0;
    background-color: #fff;
}
.db-relation li i{
    font-size: 60px;
    cursor: inherit;
}
.db-relation li p{
    margin: 10px;
}
.db-relation li input{
    border-radius: 50px;
}
.db-relation li img{
    width: 64px;
    height: 64px;
}


/*************************/
.db-profile .user-logo{
    width: 100px;
    height: 100px;
    border-radius: 100%;
    position: relative;
    overflow: hidden;
    padding-left: 0;
}
.db-profile .user-logo img{
    width: 100%;
    height: 100%;
}
.db-profile .user-logo .logo-edit{
    display: none;
    position: absolute;
    left:0;
    top:0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    text-align: center;
    line-height: 100px;
    cursor: pointer;
}
.db-profile .user-logo .logo-edit i{
    font-size: 30px;
    color: #fff;
}
.db-profile .user-logo:hover .logo-edit{
    display: block;
}
#imagefile{
    opacity: 0;
    position: absolute;
    left:0;
    top:0;
    width: 100%;
    height: 100%;
}

.home-imports li{
    position: relative;
    width: 130px;
}
.home-imports li .plugin-icon{
    height: 50px;
}
.home-imports li .upload{
    position: absolute;
    width: 100%;
    height: 100%;
    top:0;
    left:0;
    z-index: 333;
    opacity:0;
    cursor: pointer;
}