/**
 * 获取用户权限信息
 */
import { setToken } from '@/utils/auth'
import { loginUser,loginHeart } from '@/api/portal/charts'
import { getUserCityPermissInfo } from '@/api/portal/charts'

/**
 * 获取用户的token信息
 */
async function getUserToken() {
  if (process.env.NODE_ENV === 'development') { // 开发环境
    /**
    * guo_xin   916   640000
    * wangzejin   917   640100
    * wangzhening   918   640104
    * wupeng 无权限数据
    */

    //     admin,1,超级管理员
    // huwen,2,郝挺
    // wangfei,3,测试

    //     matingting
    // pq_kouxiaoju
    // pq_yangping

    const { code, token, msg } = await loginUser({
      username: 'admin',
      password: 'admin123'
    })

    if (code == 200) {
      console.log('msg=>', msg)
      sessionStorage.setItem('Authorization', msg)
      setToken(msg)
    }
  } else { // 正式环境
    // 获取oa过来url中?后面的参数 正式环境
    const hashstring = window.location.hash
    if (hashstring) {
      const temparr = hashstring.split('?')
      if (temparr && temparr[1]) {
        const pa = getRequest(temparr[1])
        // pa.token = 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImU5YmE0MzQ0LWFkOTctNDlkMS04NDNmLTk1MjYwNjE1NGZjOSJ9.o_-89eutaqDEwjFhdiZwym2_QwOF3QbqDvnDgjbR5VRHUWhzh40BeggSl_GPleQGufbo868dI2v7Sx1tbvzONg'
        document.cookie = `LtpaToken=${pa.token}`
        localStorage.setItem('username', pa.name)
        localStorage.setItem('loginName', pa.loginName)
        sessionStorage.setItem('Authorization', pa.token)
        sessionStorage.setItem('mainId', pa.mainId)
        sessionStorage.setItem('loginName', pa.loginName)
        setToken(pa.token)
      }
    }
  }
}

/**
 * 获取用户地图权限信息
 */
async function getCityPermission() {
  const mapCodeName = { '640000': '宁夏', '640100': '银川市', '640200': '石嘴山市', '640300': '吴忠市', '640400': '固原市', '640500': '中卫市' }
  const { code, data } = await getUserCityPermissInfo()
  const mapPermission = {}
  if (code == 200) {
    const props = {
      cityId: 'cityId1', cityid: 'cityId1', name: 'cityName1', mapLevel: 'level1', parentCityId: 'parentId1',
      cityId2: 'cityId2', name2: 'cityName2', mapLevel2: 'level2', parentCityId2: 'parentId2'
    }

    for (const [key, field] of Object.entries(props)) {
      mapPermission[key] = key.includes('mapLevel') ? Number(data[field]) : data[field]
    }
    mapPermission.parentCityName = mapCodeName[mapPermission.parentCityId]
  }
  sessionStorage.setItem('nx-map-permission', JSON.stringify(mapPermission))
}

function getRequest(strs) {
  const theRequest = {}
  const arr = strs.split('&')
  arr.forEach(i => {
    document.cookie = i
    const arr2 = i.split('=')
    theRequest[arr2[0]] = arr2[1]
  })
  return theRequest
}
async function uploadInfo() {
  try {
    await loginHeart();
    const time = 1000 * 60;
    const timer = setInterval(() => {
      loginHeart();
    }, time);
  } catch (error) {
    console.log('>>>>>>>> error >>>>>>', error);
  }
};
export default async function getUserPermission() {
  await getUserToken()
  await getCityPermission()
  await uploadInfo();
}
