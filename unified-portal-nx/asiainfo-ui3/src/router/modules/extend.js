import Layout from '@/layout'

const extendRouter = {
  path: '/extend',
  component: Layout,
  redirect: '/extend/vertical-data',
  name: 'Extend',
  meta: {
    title: '扩展功能',
    icon: 'el-icon-s-grid'
  },
  children: [
    {
      path: 'vertical-data',
      component: () => import('@/views/bj_proatal_web/views/extend/VerticalDataManager'),
      name: 'VerticalDataManager',
      meta: {
        title: '纵表数据管理',
        icon: 'el-icon-s-data'
      }
    },
    {
      path: 'business-type-config',
      component: () => import('@/views/bj_proatal_web/views/extend/BusinessTypeConfig'),
      name: 'BusinessTypeConfig',
      meta: {
        title: '业务类型配置',
        icon: 'el-icon-s-tools'
      }
    }
  ]
}

export default extendRouter