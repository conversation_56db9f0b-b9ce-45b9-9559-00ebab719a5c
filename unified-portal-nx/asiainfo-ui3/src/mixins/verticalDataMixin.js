import {
  saveBusinessData,
  getBusinessData,
  pageBusinessData,
  deleteBusinessData,
  batchDeleteBusinessData,
  getFieldNamesByBusinessType
} from '@/api/extend/verticalData'
import {
  formatFieldValue,
  validateFieldData,
  generateFieldSummary,
  buildQueryParams,
  getBusinessTypeConfig
} from '@/utils/verticalDataUtils'

/**
 * 纵表数据管理混入
 * 提供通用的纵表数据操作方法
 */
export default {
  data() {
    return {
      // 通用数据
      verticalData: {
        loading: false,
        dataList: [],
        total: 0,
        selectedRows: [],
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          businessType: '',
          businessId: '',
          fieldName: '',
          fieldValue: ''
        }
      }
    }
  },
  
  methods: {
    /**
     * 获取纵表数据列表
     * @param {Object} params - 查询参数
     */
    async getVerticalDataList(params = {}) {
      this.verticalData.loading = true
      
      try {
        const queryParams = {
          ...this.verticalData.queryParams,
          ...buildQueryParams(params)
        }
        
        const response = await pageBusinessData(queryParams)
        
        if (response.code === 200) {
          this.verticalData.dataList = response.data.records || []
          this.verticalData.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '查询失败')
        }
      } catch (error) {
        console.error('查询纵表数据失败:', error)
        this.$message.error('查询失败')
      } finally {
        this.verticalData.loading = false
      }
    },
    
    /**
     * 保存纵表数据
     * @param {Object} data - 要保存的数据
     */
    async saveVerticalData(data) {
      try {
        const response = await saveBusinessData(data)
        
        if (response.code === 200) {
          this.$message.success('保存成功')
          return response.data
        } else {
          this.$message.error(response.msg || '保存失败')
          return null
        }
      } catch (error) {
        console.error('保存纵表数据失败:', error)
        this.$message.error('保存失败')
        return null
      }
    },
    
    /**
     * 获取单个纵表数据
     * @param {string} businessType - 业务类型
     * @param {string} businessId - 业务ID
     */
    async getVerticalDataById(businessType, businessId) {
      try {
        const response = await getBusinessData(businessType, businessId)
        
        if (response.code === 200) {
          return response.data
        } else {
          this.$message.error(response.msg || '获取数据失败')
          return null
        }
      } catch (error) {
        console.error('获取纵表数据失败:', error)
        this.$message.error('获取数据失败')
        return null
      }
    },
    
    /**
     * 删除纵表数据
     * @param {string} businessType - 业务类型
     * @param {string} businessId - 业务ID
     */
    async deleteVerticalData(businessType, businessId) {
      try {
        const response = await deleteBusinessData(businessType, businessId)
        
        if (response.code === 200) {
          this.$message.success('删除成功')
          return true
        } else {
          this.$message.error(response.msg || '删除失败')
          return false
        }
      } catch (error) {
        console.error('删除纵表数据失败:', error)
        this.$message.error('删除失败')
        return false
      }
    },
    
    /**
     * 批量删除纵表数据
     * @param {Array} dataList - 要删除的数据列表
     */
    async batchDeleteVerticalData(dataList) {
      if (!dataList || dataList.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return false
      }
      
      try {
        // 按业务类型分组
        const groupedData = {}
        dataList.forEach(item => {
          if (!groupedData[item.businessType]) {
            groupedData[item.businessType] = []
          }
          groupedData[item.businessType].push(item.businessId)
        })
        
        // 批量删除
        const promises = Object.keys(groupedData).map(businessType => {
          return batchDeleteBusinessData({
            businessType: businessType,
            businessIds: groupedData[businessType]
          })
        })
        
        await Promise.all(promises)
        this.$message.success('批量删除成功')
        return true
      } catch (error) {
        console.error('批量删除纵表数据失败:', error)
        this.$message.error('批量删除失败')
        return false
      }
    },
    
    /**
     * 获取业务类型的字段名列表
     * @param {string} businessType - 业务类型
     */
    async getVerticalDataFieldNames(businessType) {
      if (!businessType) {
        return []
      }
      
      try {
        const response = await getFieldNamesByBusinessType(businessType)
        
        if (response.code === 200) {
          return response.data || []
        } else {
          console.warn('获取字段名失败:', response.msg)
          return []
        }
      } catch (error) {
        console.error('获取字段名失败:', error)
        return []
      }
    },
    
    /**
     * 格式化字段值显示
     * @param {*} value - 字段值
     * @param {string} type - 字段类型
     */
    formatVerticalFieldValue(value, type) {
      return formatFieldValue(value, type)
    },
    
    /**
     * 生成字段数据摘要
     * @param {Object} fieldData - 字段数据
     * @param {number} maxLength - 最大长度
     */
    generateVerticalFieldSummary(fieldData, maxLength = 100) {
      return generateFieldSummary(fieldData, maxLength)
    },
    
    /**
     * 验证字段数据
     * @param {Object} fieldData - 字段数据
     * @param {string} businessType - 业务类型
     */
    validateVerticalFieldData(fieldData, businessType) {
      const config = getBusinessTypeConfig(businessType)
      return validateFieldData(fieldData, config.fields)
    },
    
    /**
     * 处理纵表数据选择变化
     * @param {Array} selection - 选中的数据
     */
    handleVerticalDataSelectionChange(selection) {
      this.verticalData.selectedRows = selection
    },
    
    /**
     * 重置纵表数据查询条件
     */
    resetVerticalDataQuery() {
      this.verticalData.queryParams = {
        pageNum: 1,
        pageSize: 10,
        businessType: '',
        businessId: '',
        fieldName: '',
        fieldValue: ''
      }
    },
    
    /**
     * 确认删除对话框
     * @param {Function} callback - 确认后的回调函数
     * @param {string} message - 确认消息
     */
    confirmDeleteVerticalData(callback, message = '确认删除该数据吗？') {
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (typeof callback === 'function') {
          callback()
        }
      }).catch(() => {
        // 用户取消删除
      })
    },
    
    /**
     * 确认批量删除对话框
     * @param {Function} callback - 确认后的回调函数
     * @param {number} count - 删除数量
     */
    confirmBatchDeleteVerticalData(callback, count) {
      const message = `确认删除选中的${count}条数据吗？`
      this.confirmDeleteVerticalData(callback, message)
    },
    
    /**
     * 格式化日期显示
     * @param {Date|string|number} date - 日期
     */
    formatVerticalDate(date) {
      if (!date) return ''
      
      const d = new Date(date)
      if (isNaN(d.getTime())) return ''
      
      return d.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    
    /**
     * 导出纵表数据为Excel
     * @param {Array} dataList - 要导出的数据
     * @param {string} filename - 文件名
     */
    exportVerticalDataToExcel(dataList, filename = '纵表数据') {
      if (!dataList || dataList.length === 0) {
        this.$message.warning('没有数据可导出')
        return
      }
      
      // 这里可以集成Excel导出库，如 xlsx
      console.log('导出数据:', dataList)
      this.$message.info('导出功能待实现')
    }
  }
}