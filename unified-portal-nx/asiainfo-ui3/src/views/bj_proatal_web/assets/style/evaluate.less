*,
*::before,
*::after {
  box-sizing: border-box;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  border-radius: 0;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #e3e7ee;
}

html {
  width: 100%;
  height: 100%;
}

body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 12px;
  background: #F5F6FA;
  font-family: 'SourceHanSansSC-Normal', "Helvetica Neue", Helvetica, Arial, "Microsoft YaHei", sans-serif !important;
}

article, aside, figcaption, figure, footer, header, hgroup, main, nav, section {
  display: block;
}

.ev-container{
  width: 85%;
  //height: 100%;
  margin: 0 auto;
  padding: 10px 0;
}

.ev-white-box{
  background: #ffffff;
  border: 1px solid #ECECEC;
  width: 100%;
  padding: 20px 36px;
}


/*
 element style reset
*/

.ev-el-table{
  &.ev-min-height-table{
    .el-table__body-wrapper{
      min-height: 200px;
    }
  }


  /deep/ .el-radio__label{
    display: none;
  }

  .ev-el-table-label{
    .el-radio__label{
      display: none;
    }
  }
  .ev-el-table-oper{
    color: #848C96;
    cursor: pointer;
    font-size: 12px;

    img{
      vertical-align: middle;
      margin-right: 3px;
    }

    & + .ev-el-table-oper{
      margin-left: 15px;
    }
  }
}

.ev-el-select-tags{
   /deep/ .el-tag.el-tag--info{
     border-color: #d9ecff;
     background-color: #E6F3FF;
     color: #575C61;
     font-weight: 500;

     .el-tag__close{
       background-color:#D8E6F7;
       color: #169FFF;
     }
   }
}

.ev-el-table{
  border: 1px solid #EAEAEA;
  border-bottom: none;

  thead{
    font-size: 12px;
    color: #575C61;

    th{
      background: #EDF7FF;
    }

  }
  tbody{
    font-size: 12px;
    color: #434951;
  }
}

.ev-el-button{
  height: 30px;
  line-height: 30px;
  padding: 0 20px;
  border-radius: 3px;
}

.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner,
.el-radio__input.is-checked .el-radio__inner{
  border-color:#4CCE8B;
  background: #4CCE8B;
}

.el-loading-mask{
  background-color: rgba(255,255,255,0.4);

  .el-loading-spinner{
    font-size: 30px;
  }
}

