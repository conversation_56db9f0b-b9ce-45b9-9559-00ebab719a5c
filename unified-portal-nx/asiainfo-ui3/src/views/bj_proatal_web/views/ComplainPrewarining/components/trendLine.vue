<template>
  <div class="hunsty" style="position:relative">
    <div :id="t" class="hunsty" :style="{opacity:allData.length?1:0}" />
    <div :style="{position:'absolute',left:0,top:0,width:'100%',height:'100%',display:allData.length?'none':'block'}">
      <Blank2 />
    </div>
  </div>
</template>

<script>
import Blank2 from '@/views/bj_proatal_web/components/common/Blank2'
export default {
  name: 'GL<PERSON><PERSON><PERSON>',
  components: {
    Blank2
  },
  props: {
    allData: {
      type: Array,
      default: () => {
        return []
      }
    },
    option: {
      type: Object,
      default: () => {
        return {}
      }
    },
    legendData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    seriesData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      t: '',
      chart: null,
      valueList: [],
      showChart: true

    }
  },
  watch: {
    allData(v, oldv) {
      // console.log('数据变了:', v)
      this.renderChart()
    },
    xData(v, oldv) {
      // console.log('x轴数据变了:', v)
    },
    legendData(v, oldv) {
      // console.log('legend数据变了:', v)
    },
    seriesData(v, oldv) {
      // console.log('seriesData数据变了:', v)
    }
  },
  created() {
    this.t = new Date().getTime() + (Math.random() * 100).toFixed(0)
  },
  mounted() {
    var _this = this
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', function() {
      _this.chart.resize()
    })
  },
  methods: {
    initChart() {
      const dom = document.getElementById(this.t)
      this.chart = this.$echarts.init(dom)
      this.renderChart()
    },
    renderChart() {
      const option = {
        grid: {
          top: '20%',
          left: '10%',
          right: '10%',
          bottom: '15%',
          containLabel: false
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255,255,255,1)',
          textStyle: {
            color: '#262626',
            align: 'left'
          },
          confine: true,
          extraCssText: 'box-shadow:0px 2px 8px 0px rgba(102, 61, 0, 0.16)'
        },
        legend: {
          data: this.legendData,
          top: '2%',
          textStyle: {
            color: '#747474'
          }
        },
        xAxis: {
          type: 'category',
          data: this.xData,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#393939'
            }
          }
        },
        yAxis: [
          {
            // max: 100,
            type: 'value',
            name: '',
            nameTextStyle: {
              color: '#393939',
              padding: [0, 0, 10, -40]
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#eeeeee'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#393939'
              }
            }
          }

        ],
        series: [
          {
            name: '变化值',
            type: 'line',
            showAllSymbol: true, // 小圆点
            symbol: 'circle',
            symbolSize: 1,
            // smooth: true, // 平滑曲线
            itemStyle: {
              color: 'rgb(246, 175, 56)',
              borderWidth: '2',
              borderColor: 'rgb(246, 175, 56)'
            },
            lineStyle: {
              color: 'rgb(246, 175, 56)' // 线颜色
            },
            data: this.seriesData
          }

        ]

      }
      this.chart.setOption(option)
      if (this.allData.length == 0 || !this.allData) {
        this.chart.showLoading({
          text: '暂无数据',
          color: 'rgba(255, 255, 255, 0)',
          fontSize: 20,
          textColor: '#8a8e91',
          maskColor: 'rgba(255, 255, 255, 1)'
        })
      } else {
        this.chart.hideLoading()
      }
    }

  }

}
</script>
<style lang="scss" scoped>
.hunsty{
    height:100%;
    width:100%;
}
</style>
