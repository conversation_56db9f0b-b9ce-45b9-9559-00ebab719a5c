<template>
  <div class="business">
    <NumberTitle num="01" text="投诉总览" />
    <div class="con">
      <div style="position:absolute;right:0px;top:-60px">
        <div style="display:inline-block">
          <WeekDatePick @weekChange="weekChange" />
        </div>
      </div>
      <div class="con">
        <div class="flex mapbox" style="height:520px" v-loading="mapLoading">
          <div style="padding:10px;">
            <div style="border-right:1px solid #e6e6e6;height:100%;">
               <Map
                class="traffic-map-height"
                title="投诉量地图"
                :rank-colors="['rgba(245,154,35,1)','rgba(245,154,35,0.7)','rgba(245,154,35,0.4)',]"
                :map-data="mapData"
                :showMapList="showMapList"
                :ningdongAndSanyingVis="false"
                :mapMaxLevel="4"
                 :tooltip="[
                  {labelField:'label',field:'num'}
                ]"
                @map-click="areaChange"
              /> 
            </div>
          </div>
          <div style="padding:10px;padding-left:0;">
            <div class="title">
              投诉量看板
            </div>
            <div class="upflex">
              <div class="upflex1">
                <div @click="errorTypeChange('1')" :class="{cardcon:true,active:errorType=='1'}" style="text-align:left">
                  <div class="lab">两类差错投诉</div>
                  <div class="num">{{
                    errorTypeList[0].num
                    }}</div>
                  <div><span class="lab">环比：</span>
                    <span class="per">
                    {{ errorTypeList[0].zb}} 
                    </span>
                    <span v-show="errorTypeList[0].zb && errorTypeList[0].zb!='-'"> %</span>
                    <span style="margin-left:5px;">
                      <i v-show="errorTypeList[0].zb && errorTypeList[0].zb !='-' && errorTypeList[0].zb!=0"
                    :class="{
                      'el-icon-caret-bottom':true,
                      up:errorTypeList[0].zb>0?true:false,
                      down:errorTypeList[0].zb>0?false:true,
                    }"
                  /></span></div>
                </div>

              </div>
              <div class="upflex2 iclass">
                <i class="el-icon-circle-plus-outline " style="font-size:35px;color:#F5B23F;" />
              </div>
              <div class="upflex3">
                 <div @click="errorTypeChange('2')" :class="{cardcon:true,active:errorType=='2'}" style="text-align:left">
                  <div class="lab">反悔办理投诉</div>
                  <div class="num">{{
                    errorTypeList[1].num
                    }}</div>
                  <div><span class="lab">环比：</span>
                    <span class="per">
                    {{ errorTypeList[1].zb}} 
                    </span>
                    <span v-show="errorTypeList[1].zb && errorTypeList[1].zb!='-'"> %</span>
                    <span style="margin-left:5px;">
                      <i v-show="errorTypeList[1].zb && errorTypeList[1].zb !='-' &&  errorTypeList[1].zb!=0"
                    :class="{
                      'el-icon-caret-bottom':true,
                      up:errorTypeList[1].zb>0?true:false,
                      down:errorTypeList[1].zb<0?true:false,
                    }"
                  /></span></div>
                </div>
              </div>
              <div class="upflex4 iclass">
                <i class="el-icon-video-pause" style="font-size:35px;color:#F5B23F;" />

              </div>
              <div class="upflex5">
                <div @click="errorTypeChange('3')" :class="{cardcon:true,active:errorType=='3'}" style="text-align:left">
                  <div class="lab">总投诉量</div>
                  <div class="num">{{
                    errorTypeList[2].num
                    }}</div>
                  <div><span class="lab">环比：</span>
                    <span class="per">
                    {{ errorTypeList[2].zb}} 
                    </span>
                    <span v-show="errorTypeList[2].zb && errorTypeList[2].zb!='-' " > %</span>
                    <span style="margin-left:5px;">
                      <i v-show="errorTypeList[2].zb && errorTypeList[2].zb !='-' && errorTypeList[2].zb!=0"
                    :class="{
                      'el-icon-caret-bottom':true,
                      up:errorTypeList[2].zb>0?true:false,
                      down:errorTypeList[2].zb<0?true:false,
                    }"
                  /></span></div>
                </div>
              </div>
            </div>
            <div>
              <div class="title">
                网格类型构成
              </div>
              <div style="height:280px;padding-left:30px;position:relative">
                <PieView v-if="pieData.length" :data="pieData" />
                <Blank2 v-else />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import NumberTitle from '@/views/bj_proatal_web/components/common/numberTitle'
import AreaPicker from '@/views/bj_proatal_web/nx-components/area-picker/AreaPicker.vue'
import WeekDatePick from './weekDatePicker.vue'
// import Map from '@/views/bj_proatal_web/nx-components/map/Map.vue'
import Map from '@/views/bj_proatal_web/nx-components/map/MapContainGrid.vue'

import PieView from './PieView.vue'
import { getTwoErrorTotal } from '@/api/complainPrewarining'
const errorList = [
        {
          errorType:1,
          num:'-',
          zb:'-'
        },
         {
          errorType:2,
          num:'-',
          zb:'-'
        },
         {
          errorType:3,
          num:'-',
          zb:'-'
        }
      ]

export default {
  inject: ['mapPermission'],
  name: 'ConFirst',
  components: {
    NumberTitle,
    AreaPicker,
    WeekDatePick,
    Map,
    PieView

  },
  inject: ['mapPermission'],
  data() {

    const day = new Date().getDay()
    const oneDayTime = 24 * 60 * 60 * 1000
    const nowTime = new Date().getTime()
    // 显示周一 先算出本周一 再减去7 天 默认上周一
    let lastMondayTime = nowTime - (day - 1) * oneDayTime - 7 * oneDayTime
    // 显示周日 先算出本周末 再减去7 天 默认上周末
    let lastSundayTime = nowTime + (7 - day) * oneDayTime - 7 * oneDayTime
    // 初始化日期时间
    lastMondayTime = this.parseTime(lastMondayTime, '{y}-{m}-{d}')
    lastSundayTime = this.parseTime(lastSundayTime, '{y}-{m}-{d}')

    return {
      mapLoading: false,
      map: { ...this.mapPermission },
      pieData: [],//{ name: 'x', value: '10', partrate: '20' }
      endTime:`${lastMondayTime}|${lastSundayTime}`,
      mapData:[],
      errorType:'3',// 1两类差错投诉 2反悔办理投诉 3总投诉量
      errorTypeList:errorList,
      // 是否展示地图排名
      showMapList:true
    }
  },
  mounted() {
    this.query();
  },
  methods: {
    errorTypeChange(errorType){
      this.errorType = errorType;
      this.query();
    },
    // 查询数据
    query(){
      let {endTime,map,errorType} = this;
      let {cityId} = map;
      this.mapLoading = true;
       getTwoErrorTotal({ cityId, endTime,errorType }).then(res => {
        let {code,data,msg} =res;
        if(code==200){
             let {areaList,errorTypeList,gridTypeList} = data;
              if(areaList&&Array.isArray(areaList)&&areaList.length){
                   this.mapData = areaList.map(i=>{
                    i.label = errorType=='3'?'总投诉量':errorType=='2'?'反悔办理投诉':'两类差错投诉'
                    i.cityId = i.id
                    i.cityName = i.name;
                    return i
 
                  })
                 
              console.log('this.mapData=>',this.mapData)
              }else{
                this.mapData = []
              }
              if(errorTypeList&&errorTypeList.length){
                let temp = JSON.parse(JSON.stringify(errorList))
                temp.forEach(j=>{
                  errorTypeList.forEach(x=>{
                    if(j.errorType==x.id){
                      j.num = x.num;
                      j.zb =x.zb;
                    }
                  })
                })
                 this.errorTypeList = temp
                 console.log('errorTypeList==>',temp);
              }else{
                  this.errorTypeList = errorList 
              }
             


              if(gridTypeList&&gridTypeList.length){
                // { name: 'x', value: '10', partrate: '20' }
                this.pieData = gridTypeList.map(x=>{
                  x.value = x.num;
                  x.partrate = x.zb;
                  return x;
                })
              }else{
                this.pieData = []
              }
             
        }else{
          this.$message.error(msg)
        }
    
       
    }).finally(()=>{
      this.mapLoading = false;
    }).catch(err=>{
      console.log('err==>',err)
      this.$message(err)
    })
    },
    areaChange(map) {
      // 暂时不能点击区
      // let clickCityId = map.id;
     
      // if(clickCityId&&clickCityId.slice(4,6)!='00' && false){//区县
      //   this.showMapList = false;
      //   this.queryDistrict(clickCityId)


      //   return;
      // }else{
      //   this.showMapList = true;
        
      // }
      // console.log('this.map==>',map);
      
      this.map = { ...map }
      this.query()
    },

    queryDistrict(clickCityId){//查询区县
      let {endTime,map,errorType} = this;
      this.mapLoading = true;
       getTwoErrorTotal({ cityId:clickCityId, endTime,errorType }).then(res => {
        let {code,data,msg} =res;
        if(code==200){
             let {areaList,errorTypeList,gridTypeList} = data;
              // if(areaList&&Array.isArray(areaList)&&areaList.length){
              //      this.mapData = areaList.map(i=>{
              //       i.label = errorType=='3'?'总投诉量':errorType=='2'?'反悔办理投诉':'两类差错投诉'
              //       i.cityId = i.id
              //       i.cityName = i.name;
              //       return i
 
              //     })
                 

              // }else{
              //   this.mapData = []
              // }
              if(errorTypeList&&errorTypeList.length){
                let temp = JSON.parse(JSON.stringify(errorList))
                temp.forEach(j=>{
                  errorTypeList.forEach(x=>{
                    if(j.errorType==x.id){
                      j.num = x.num;
                      j.zb =x.zb;
                    }
                  })
                })
                 this.errorTypeList = temp
                 console.log('errorTypeList==>',temp);
              }else{
                  this.errorTypeList = errorList 
              }
             


              if(gridTypeList&&gridTypeList.length){
                // { name: 'x', value: '10', partrate: '20' }
                this.pieData = gridTypeList.map(x=>{
                  x.value = x.num;
                  x.partrate = x.zb;
                  return x;
                })
              }else{
                this.pieData = []
              }
             
        }else{
          this.$message.error(msg)
        }
    
       
    }).finally(()=>{
      this.mapLoading = false;
    }).catch(err=>{
      console.log('err==>',err)
      this.$message(err)
    })
    },










    weekChange(week) {
      console.log('week=>',week);
      this.endTime = week;
      this.query();
    }
  }
}
</script>

<style lang="scss" scoped>
.upflex{
  display: flex;
  width:100%;
  height:100px;
  text-align: center;
  margin-bottom: 10px;
  .upflex1{
    width:28%;
   
  }
  .upflex2{
    width:8%;
    line-height: 100px;
  }
  .upflex3{
     width:28%;
     
  }
  .upflex4{
     width:8%;
      line-height: 100px;
     i{
      transform: rotate(90deg);
     }
  }
  .upflex5{
     width:28%;
   
  }
  .iclass i{
    font-size:35px;

  }

}
i{
  font-size:22px;
  &.up{
      color:red !important;
      transform: rotate(180deg) translateY(-3px);
    }
  &.down{
      color:green !important;
      transform:translateY(3px);
    }
}
.cardcon{
  padding:10px 10px 10px 20px;
  font-size: 14px;
  cursor: pointer;
   background:#f5f5f5;
  &.active{
    background:#FCECD1;
  }
  .lab{
    color:#262626;
  }
  .num{
    font-weight: bold;
    line-height: 36px;
  }
  .per{

  }
}
.title{
    color: #262626;
    font-weight: bold;
    line-height: 40px;
    padding:10px 0 10px 0;
}

.business{
     background-color: rgba(242, 242, 242, 1);
     padding:30px 40px 0px 40px;

     /deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }
}
.con{
  position: relative;
  background:#fff;
  margin-top:10px;
}
.flex{
  display:flex;
  &.mapbox{
    >div{
      width:50%;
      flex:1;
    }
  }
}
.cus-border {
      height: 93%;
      width: 1px;
      margin: 3% 0;
      background-color: #ddd;
      float: right;
    }

.pt{
    font-weight: bold;
    font-size: 16px;
    line-height: 32px;
    padding:10px 10px 0px 10px;

}

.show {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #6b6b6b;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hide {
  display: none;
}

</style>

