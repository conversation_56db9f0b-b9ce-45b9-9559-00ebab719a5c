<template>
  <div class="business">
    <NumberTitle num="03" text="投诉网格TOP" />
    <div class="con">
      <div style="position:absolute;right:0px;top:-60px">
        <AreaPicker class="areabox" size="small" :max-area-level="3" @change="areaChange" />
        <div style="display:inline-block">
          <WeekDatePick @weekChange="weekChange" />
        </div>

      </div>
      <div class="con">
        <div class="radiobox">
          <el-radio-group v-model="errorType" @change="radioChange">
            <el-radio-button label="1">两类差错</el-radio-button>
            <el-radio-button label="2">反悔办理</el-radio-button>
          </el-radio-group>
        </div>
        <div class="flex" v-loading="loading">
          <div class="flexCard" v-for="item in typeNames" :key="item">
            <div style="line-height:40px;font-weight:bold;font-size:14px;border-bottom:1px solid #e6e6e6;padding-left:20px">{{item}}网络TOP10</div>
            <Top10  v-if="top10Data[item] && top10Data[item].length" :keymap="keymap" :all-data="top10Data[item]" />
            <div  v-else style="min-height:200px;display:flex;flex-direction:column;justify-content:center">
              <Blank2 />
            </div>
            
          </div> 
        </div>

      </div>
    </div>

  </div>
</template>
<script>
import NumberTitle from '@/views/bj_proatal_web/components/common/numberTitle'
import AreaPicker from '@/views/bj_proatal_web/nx-components/area-picker/AreaPicker.vue'
import WeekDatePick from './weekDatePicker.vue'
import Top10 from './top10.vue'
import { getTwoErrorByGrid } from '@/api/complainPrewarining'


export default {
  components: {
    NumberTitle,
    AreaPicker,
    WeekDatePick,
    Top10
  },
  inject: ['mapPermission'],
  data() {
    const day = new Date().getDay()
    const oneDayTime = 24 * 60 * 60 * 1000
    const nowTime = new Date().getTime()
    // 显示周一 先算出本周一 再减去7 天 默认上周一
    let lastMondayTime = nowTime - (day - 1) * oneDayTime - 7 * oneDayTime
    // 显示周日 先算出本周末 再减去7 天 默认上周末
    let lastSundayTime = nowTime + (7 - day) * oneDayTime - 7 * oneDayTime
    // 初始化日期时间
    lastMondayTime = this.parseTime(lastMondayTime, '{y}-{m}-{d}')
    lastSundayTime = this.parseTime(lastSundayTime, '{y}-{m}-{d}')
    console.log('lastMondayTime=>', lastMondayTime)
    return {
      errorType: '1',
      endTime:`${lastMondayTime}|${lastSundayTime}`,
      cityId: this.mapPermission.cityId,
      keymap: {
        yName: '', // y轴的名称
        yData: 'filed', // y轴取的字段
        seriesData: ['score', 'sampleNum'] // 对应值取的字段
      },
      
     loading:false,
      typeNames:['乡镇','商圈','社区','综合'],
      top10Data:{
        '乡镇':[],
        '商圈':[],
        '社区':[],
        '综合':[]
      }

    }
  },
  mounted(){
    this.query()
  },
  methods: {
    query(){
       let {endTime,cityId,errorType} = this;
        this.loading=true;
        // :'2023-03-13|2023-03-19'
        getTwoErrorByGrid({endTime,cityId,errorType}).then(res=>{
          let {code,data}=res;
          if(code==200 && data){
            this.typeNames.forEach(i=>{
              data[i]&&data[i].forEach(j=>{
                j.filed = j.name;
                j.score=j.num;
                j.sampleNum = `${j.zb} %`;
              })
            });
            this.top10Data = data||{}
          }
        }).finally(()=>{
          this.loading = false;
        }).catch(err=>{
          this.$message.error(err)
        })
      
    },
    areaChange(v) {
      if(v.length){
         this.cityId = v[v.length-1];
         this.query();
      }
    },
    
    radioChange() {
      this.query();
    },
    weekChange(week) {
      console.log('week=>',week);
      this.endTime = week;
      this.query();
    }
  }
}
</script>

<style lang="scss" scoped>

.business{
     background-color: rgba(242, 242, 242, 1);
     padding:30px 40px 0px 40px;
     min-height: 100vh;
     /deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }
}
.con{
  position: relative;
  background:#fff;
  margin-top:10px;
}
.radiobox{
  padding:20px 0;
}
.flex{
  display:flex;
  padding:0 10px;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;

  div.flexCard{
    box-sizing: border-box;
    border:1px solid #e6e6e6;
    width:calc(50% - (20px / 2));
    margin-bottom: 10px;;
  }
}
.cus-border {
      height: 93%;
      width: 1px;
      margin: 3% 0;
      background-color: #ddd;
      float: right;
    }
.pt{
    font-weight: bold;
    font-size: 16px;
    line-height: 32px;
    padding:10px 10px 0px 10px;

}

.show {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #6b6b6b;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hide {
  display: none;
}
</style>

