<template>
  <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-suffix=": " :hide-required-asterisk="true">
    <el-form-item label="预警级别" prop="warningLevel">
      <el-radio-group v-model="form.warningLevel">
        <el-radio
          v-for="{complanitwarningleveltype} in warningLevel"
          :key="complanitwarningleveltype"
          :label="complanitwarningleveltype"
        />
      </el-radio-group>
    </el-form-item>
    <el-row>
      <el-col :span="10">
        <el-form-item label="通知渠道" prop="notificationChannel">
          <el-checkbox-group v-model="form.notificationChannel">
            <el-checkbox
              v-for="ch in channel"
              :key="ch.complanitwarningleveltype"
              :label="ch.complanitwarningleveltype"
              :disabled="disabledChannel.includes(ch.typeid)"
            />
          </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col :span="14">
        <el-form-item label="接受时段" prop="receiveTime">
          <el-time-picker v-model="form.receiveTime" is-range />
        </el-form-item>
      </el-col>
    </el-row>
    <el-form-item label="短信内容" prop="message">
      <el-input v-model="form.message" type="textarea" rows="5" placeholder="请输入短信内容" />
    <!-- <div class="form-tip">示例：黄色预警（1级） 您好，预警规则名称1已触发预警，指标已达到阀值（20%〕，银川市实际工单量为2000，请及时处理！</div> -->
    </el-form-item>
    <el-form-item label="接受人" prop="receivePhone">
      <el-input v-model="form.receivePhone" placeholder="请输入电话号码以 , 隔开" />
    </el-form-item>
  </el-form>
</template>

<script>
import ajaxRequest from '@/api/complain-warning'

export default {
  name: 'Step3',
  props: ['form'],
  data() {
    return {
      warningLevel: [],
      channel: [],
      disabledChannel: ['6'],
      rules: {
        warningLevel: { required: true, message: '请选择预警级别' },
        notificationChannel: { required: true, message: '请选择通知渠道' },
        receiveTime: { required: true, message: '请选择接受时段' },
        message: { required: true, message: '请输入短信内容' },
        receivePhone: { required: true, message: '请输入接受人' }
      }
    }
  },
  deactivated() {
    this.$refs.form.clearValidate()
  },
  created() {
    this.getLists()
  },
  methods: {
    getLists() {
      // 级别类型
      ajaxRequest('hvGwrnsj', { type: 'WJB' }).then((res) => {
        const { data: { data }} = res
        this.warningLevel = data
      })
      // 通知渠道
      ajaxRequest('hvGwrnsj', { type: 'TZJD' }).then((res) => {
        const { data: { data }} = res
        this.channel = data
      })
    },
    validate() {
      return new Promise((resolve) => {
        this.$refs.form.validate((valid) => {
          return valid ? resolve(true) : resolve(false)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form{
  width: 55%;
  margin: 40px auto 40px 24%;

  .el-date-editor,
  .el-select{
    width: 100%;
  }
}
.form-tip {
  color: #8c8c8c;
  font-size: 14px;
  line-height: 20px;
  padding-top: 12px;
}

/deep/ textarea {
  font-family: inherit;
}
</style>
