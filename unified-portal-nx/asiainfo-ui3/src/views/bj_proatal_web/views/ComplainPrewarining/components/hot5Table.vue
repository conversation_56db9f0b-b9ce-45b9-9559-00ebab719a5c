<template>
  <div class="repair-table">
    <el-table border :data="tableData" class="inner-table" stripe @row-click="clickRow">
      <el-table-column align="center" type="index" label="排名" />
      <el-table-column align="center" prop="targetAlias" label="热点业务名称" />
      <el-table-column align="center" prop="score" label="投诉量" />
      <el-table-column align="center" prop="partRate" label="投诉占比" />
      <el-table-column align="center" prop="momrate" label="投诉环比" />
    </el-table>
    <el-dialog
      title="地市投诉排名"
      class="table-up"
      :visible.sync="selfInspectionDialog"
      :close-on-click-modal="false"
      width="370px"
      center
      :modal="false"
    >
      <el-table :data="orgsArr" style="width: 100%">
        <el-table-column prop="cityName" align="center" label="地市" />
        <el-table-column prop="score" align="center" label="投诉量" />
        <el-table-column prop="partRate" align="center" label="投诉占比" />
        <el-table-column prop="momrate" align="center" label="投诉环比" /></el-table></el-col>
    </el-dialog>
  </div>
</template>
<script>

import { Yftf91by, fstzmS0 } from '@/api/complainPrewarining/index.js'
import tool from '@/views/bj_proatal_web/utils/utils'
export default {
  props: {
    allData: {
      type: Array,
      default: () => {
        return []
      }
    },
    orgs: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      tableData: [],
      orgsArr: [],
      selfInspectionDialog: false

    }
  },
  watch: {
    allData(v, oldv) {
      if (Array.isArray(v)) {
        v.forEach(item => {
          item.partRate = item.partRate === '0' ? (item.partRate) + ' %' : item.partRate === 0 ? (item.partRate) + ' %'
            : !item.partRate ? '-' : item.partRate + ' %'

          item.momrate = item.momrate === '0' ? item.momrate + ' %' : item.momrate === 0 ? item.momrate + ' %'
            : !item.momrate ? '-' : item.momrate + ' %'
        })
      }
      console.log('v:=>', v)
      this.tableData = v || []
    },
    orgs(v, oldv) {
      if (Array.isArray(v)) {
        v.forEach(item => {
          item.partRate = item.partRate === '0' ? (item.partRate) + ' %' : item.partRate === 0 ? (item.partRate) + ' %'
            : !item.partRate ? '-' : item.partRate + ' %'

          item.momrate = item.momrate === '0' ? item.momrate + ' %' : item.momrate === 0 ? item.momrate + ' %'
            : !item.momrate ? '-' : item.momrate + ' %'
        })
      }
      console.log('1123232')
      this.orgsArr = v || []
      this.selfInspectionDialog = true
    }
  },

  methods: {
    clickRow(row) {
      this.$emit('rowclick', row)
      // console.log('row:', row)
      // const p = {
      //   'targetId': '623010405',
      //   'statDate': '2022-06-12',
      //   'statType': '5',
      //   'cityId': '640000'
      // }
      // fstzmS0(p).then(res => {
      //   if (res.code == 200) {
      //     tool.handlerMomrateAndYoyrate(res.data)
      //     this.orgs = res.data || []
      //   }
      // }).finally(() => {
      //   this.selfInspectionDialog = true
      // })
    }
  }
}
</script>
  <style lang="less" scoped>
  .repair-table{
      padding:10px;
  }
    .inner-table{
    ::v-deep{
    .el-table__cell{
    // height:58px !important;
         }
        }

    }
.repair-table /deep/.el-dialog:not(.is-fullscreen) {
    margin-top: 33vh !important;
}
  </style>

