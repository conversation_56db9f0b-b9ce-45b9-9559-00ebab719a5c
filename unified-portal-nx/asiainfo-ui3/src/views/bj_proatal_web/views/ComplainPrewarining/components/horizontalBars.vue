<template>
  <div class="hunsty" style="position: relative">
    <div :id="t" class="hunsty" :style="{ opacity: allData.length ? 1 : 0 }" />
    <div
      :style="{
        position: 'absolute',
        left: 0,
        top: 0,
        width: '100%',
        height: '100%',
        display: allData.length ? 'none' : 'block',
      }"
    >
      <Blank2 />
    </div>
  </div>
</template>

<script>
import { getTop5Detail } from "@/api/complaint-topic/index.js";
import tool from "@/views/bj_proatal_web/utils/utils";
export default {
  name: "HorizontalBar",
  props: {
    keymap: {
      type: Object,
      default: () => {
        return {
          yName: "", // y轴的名称
          yData: "appartName", // y轴取的字段
          seriesData: ["score"], // 对应值取的字段
        };
      },
    },
    allData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    option: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isShowTip: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
  },
  data() {
    return {
      t: "",
      chart: null,
      valueList: [],
      showChart: true,
      loading: false,
      axisToolTip: false,
    };
  },
  computed: {
    yData() {
      const temp = this.allData.map((i) => i[this.keymap["yData"]]);
      temp.reverse();
      return temp;
    },
    seriesData() {
      const temp = [];
      this.keymap["seriesData"].forEach((i, idx) => {
        temp[idx] = this.allData.map((x) => x[i]).reverse();
      });

      return temp;
    },
  },
  watch: {
    allData(v, oldv) {
      this.initChart();
    },
  },
  created() {
    this.t = new Date().getTime() + (Math.random() * 100).toFixed(0);
  },
  mounted() {
    var _this = this;
    this.$nextTick(() => {
      this.initChart();
    });
    window.addEventListener("resize", function () {
      _this.chart.resize();
    });
  },
  methods: {
    initChart() {
      this.chart ? this.chart.dispose() : "";
      const dom = document.getElementById(this.t);
      this.chart = this.$echarts.init(dom);
      this.renderChart();
      this.chart.on("mousemove", (params) => {
        if (params.componentType === "yAxis") {
          this.axisToolTip = true;
          this.chart.dispatchAction({
            type: "showTip",
            seriesIndex: 0,
            dataIndex: params.dataIndex,
            position: [params.event.offsetX + 10, params.event.offsetY + 10],
          });
        }
      });
      this.chart.on("mouseover", () => {
        this.axisToolTip = false;
      });
    },
    renderChart() {
      const _this = this;
      const option = {
        grid: {
          top: "20%",
          left: "2%",
          right: "8%",
          bottom: "5%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          className: this.axisToolTip ? "horizontal-axis-tooltip" : "",
          formatter: (params) => {
            // y轴的提示
            if (this.axisToolTip) {
              const [{ name }] = params;
              return `<span class="horizontal-axis-tooltip">${name}</span>`;
            }
            // 数据的提示
            const tips = [];
            params.forEach((item) => {
              if (item.seriesName !== "__bg") {
                tips.push(
                  `${item.name} </br> ${item.marker} ${item.seriesName}${item.value}`
                );
              }
            });
            return tips.join("</br>");
          },
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.01],
          show: false,
        },
        yAxis: [
          {
            name: this.keymap.yName,
            type: "category",
            data: this.yData,
            axisTick: {
              show: false, // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false, // 不显示坐标轴线
            },
            axisLabel: {
              show: true, // 不显示坐标轴上的文字
            },
            splitLine: {
              show: false, // 不显示网格线
            },
          },
        ],
        series: [
          {
            // name: "2011",
            type: "bar",
            showBackground: true,
            backgroundStyle: {
              // 柱状图的背景颜色
              color: "#f0f0f0",
            },
            itemStyle: {
              // 柱状图实际数据的颜色
              color: "#7097F6",
            },
            barWidth: 15,
            // data: this.yAxisData
            data: this.seriesData[0],
          },
        ],
      };
      if (this.isShowTip) {
        option.tooltip = {
          trigger: "axis",
          backgroundColor: "rgba(255,255,255,1)",
          textStyle: {
            color: "#262626",
            align: "left",
          },
          transitionDuration: 0,
          triggerOn: "click",
          //  confine: true,
          extraCssText: "box-shadow:0px 2px 8px 0px rgba(102, 61, 0, 0.16)",
          formatter: function (datas, ticket, callback) {
            let param = {};
            _this.allData.forEach((els) => {
              if (els.targetalias == datas[0].axisValue) {
                param = {
                  opTime: els.opTime,
                  cityId: els.cityId,
                  targetAlias: els.targetalias,
                  targetId: els.targetId,
                  statType: els.statType,
                };
              }
            });
            if (!_this.loading) {
              _this.loading = true;
              _this.$emit("isLoading", _this.loading);
              getTop5Detail(param).then((res) => {
                let html = "";
                if (res.data.data.length) {
                  html = `
                <div
      class="legend-right"
    >
      <div class="table-all" style=" display: flex;
  flex-direction: column;">
        <div class="table-line table-headers" style="display: flex;
    align-items: center;
    margin-bottom: 7px;">
          <div class="table-cells" style="height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px; min-width: 100px; color: #8c8c8c !important;">地市</div>
          <div class="table-cells" style=" height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px; width: 50px;color: #8c8c8c !important;">投诉量</div>
          <div class="table-cells" style=" height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px; width: 50px;color: #8c8c8c !important;">投诉环比</div>
        </div>`;

                  res.data.data.forEach((el) => {
                    html += ` <div class="table-line" style="display: flex;
    align-items: center;
    margin-bottom: 7px;">
          <div class="table-cells" style=" min-width: 100px;height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px;">${el.cityname}</div>
          <div class="table-cells" style="  width: 50px;  height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px;">${el.score}</div>
          <div class="table-cells" style=" width: 50px; height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px;">${
        el.momratetype && el.momratetype == 1
          ? "-" + (100 * Number(el.momrate)).toFixed(2)
          : (100 * Number(el.momrate)).toFixed(2) + "%"
      }</div>
        </div>`;
                  });
                  html += `</div>
    </div>`;
                } else {
                  html = "暂无数据";
                }
                _this.loading = false;
                _this.$emit("isLoading", _this.loading);
                callback(ticket, html);
              });
            }

            return "数据查询中";
          },
        };
      }

      this.chart&&this.chart.setOption(option);
      if (this.allData.length == 0 || !this.allData) {
        this.chart.showLoading({
          text: "暂无数据",
          color: "rgba(255, 255, 255, 0)",
          fontSize: 20,
          textColor: "#8a8e91",
          maskColor: "rgba(255, 255, 255, 1)",
        });
      } else {
        this.chart.hideLoading();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.hunsty {
  height: 100%;
  width: 100%;
}
.table-all {
  display: flex;
  flex-direction: column;
  position: relative;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  max-height: 295px;
  overflow: auto;
  max-width: 80%;
  .table-line {
    display: flex;
    align-items: center;
    margin-bottom: 7px;
    .table-cells {
      height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px;
      &:nth-child(1) {
        width: 15px;
      }
      &:nth-child(2) {
        min-width: 120px;
      }
      &:nth-child(3) {
        width: 50px;
      }
      &:nth-child(4) {
        width: 50px;
      }
      span {
        display: inline-block;
        width: 12px;
        height: 12px;
        background: #f98781;
        border-radius: 50%;
      }
    }
  }
}
.table-headers {
  .table-cells {
    color: #8c8c8c !important;
  }
}
</style>
