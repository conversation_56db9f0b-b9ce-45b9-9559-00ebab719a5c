<template>
<div>
   <el-form ref="form" :model="form" :rules="rules" label-width="90px" label-suffix=": " >
    <el-form-item label="预警级别" prop="warningLevel">
      <el-radio-group v-model="form.warningLevel">
        <el-radio
          v-for="{complanitwarningleveltype} in warningLevel"
          :key="complanitwarningleveltype"
          :label="complanitwarningleveltype"
        />
      </el-radio-group>
    </el-form-item>
    <el-row>
      <el-col :span="10">
        <el-form-item label="通知渠道" prop="notificationChannel" >
          <!-- <el-checkbox-group v-model="form.notificationChannel">
            <el-checkbox
              v-for="ch in channel"
              :key="ch.complanitwarningleveltype"
              :label="ch.complanitwarningleveltype"
              :disabled="disabledChannel.includes(ch.typeid)"
            />
          </el-checkbox-group> -->
          <span style="color:#606266">短信</span>
        </el-form-item>
      </el-col>
      <el-col v-show="false" :span="14">
        <el-form-item label="接受时段" prop="receiveTime">
          <el-time-picker v-model="form.receiveTime" is-range />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- <el-form-item label="短信内容" prop="message">
      <el-input v-model="form.message" type="textarea" rows="5" placeholder="请输入短信内容" />

    </el-form-item> -->

    <!-- <el-form-item label="接受人" prop="receivePhone">
      <el-input v-model="form.receivePhone" placeholder="请输入电话号码以英文 , 隔开" />
    </el-form-item> -->


    
    <el-form-item label="接受人" prop="receivePhoneArray">
        <el-select v-model="form.receivePhoneArray" filterable multiple placeholder="" @change="receivePhoneArrayChange" style="width:80%" >
          <el-option
            v-for="item in receivePhoneArray"
            :key="item.label"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-button style="margin-left:20px" type="primary" @click="openDialog">通过部门选择人员</el-button>
    </el-form-item>

    
    <el-form-item label="短信内容" prop="msgContent" >

      <!-- // warningLevel: '预警级别',
      //   indexName: '预警规则名称',
      //   referenceValue: '阈值',
      //   resultValue: '实际值',
      //   calibers: '引用类型'
      //   receiveTime: '受理日期', -->
      <!-- #orderAndPhoneNum# -->

      <div class="message-edit">
        <el-button type="primary" size="mini" @click="setDefaultState">恢复默认</el-button>
        <el-button size="mini" @click="addToContent('warningLevel')">{{ msgName.warningLevel || `预警级别` }}</el-button>
        <el-button size="mini" @click="addToContent('indexName')">{{ msgName.indexName || `预警规则名称` }}</el-button>
        <el-button size="mini" @click="addToContent('referenceValue')">{{ msgName.referenceValue || `阈值` }}</el-button>
        <el-button size="mini" @click="addToContent('resultValue')">{{ msgName.resultValue || `实际值` }}</el-button>
        <el-button size="mini" @click="addToContent('receiveTime')">{{ msgName.receiveTime || `受理日期` }}</el-button>
        <el-button size="mini" @click="addToContent('calibers')">{{ msgName.calibers || `引用类型` }}</el-button>
        <el-button size="mini" @click="addToContent('orderAndPhoneNum')">{{ msgName.orderAndPhoneNum || `工单号` }}</el-button>
        <div
          id="editable"
          ref="message-edit"
          class="content"
          contenteditable
          @drop="handleDrop"
          @dragover="handleDragOver"
          @keyup="handleEditChange"
          v-html="decodeMSG(msgContent)"
        />
      </div>
    </el-form-item>

  </el-form>
   <el-dialog title="选择接受人" :visible.sync="stp3DialogVis" center width="85%" @close="clean">
    <el-form v-model="stp3DialogForm" :inline="true" label-width="120px">
        <el-row>
          <el-form-item label="选择部门:">
            <el-cascader 
                :show-all-levels="false"
                :style="{width:'400px'}"
                v-model="stp3DialogForm.deptId"
                :options="deptInfoArr"
                :props="{label:'deptName',value:'deptId',children:'childDeptAndUserInfoVOS',checkStrictly:true}"
                @change="cascaderChange">
            </el-cascader>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="选择接受人:">
              <el-select  :style="{width:'400px'}" v-model="stp3DialogForm.selectedPersons" multiple placeholder="请选择" >
                <el-option
                  v-for="item in personOpts"
                  :key="`${item.nickName}${item.phonenumber}`"
                  :label="`${item.nickName}${item.phonenumber}`"
                  :value="`${item.phonenumber}`">
                </el-option>
              </el-select>
          </el-form-item>
        </el-row>

      </el-form>
        <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="clean">清 除</el-button>
        <el-button type="primary" size="small" @click="dialogComfirm">确 定</el-button>
      </span>
  </el-dialog>
</div>
 
 
</template>

<script>
import ajaxRequest from '@/api/complain-warning'

export default {
  name: 'Step3',
  props: ['form','receivePhoneObject','receivePhoneArray','deptInfoArr'],
  data() {
    var validatePhone = (rule, value, callback) => {
      if (value) {
        const temp = value.split(',')
        let result = true
        temp.forEach(i => {
          const r = /^1[3|4|5|7|8|9]\d{9}$/.test(i)
          if (!r) {
            result = false
          }
        })
        if (!result) {
          callback(new Error('请输入正确的手机号码并以英文,隔开'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入正确的手机号码并以英文,隔开'))
      }
    }

      var validatePhoneArray = (rule, value, callback) => {
      if (value) {
        if(!value.length){
          callback(new Error('请选择接受人'))
        }else{
          callback()
        }
      } else {
        callback(new Error('请选择接受人'))
      }
    }
   

    return {
      personOpts:[],
      depOpts:[],
      stp3DialogVis:false,
      stp3DialogForm:{
        deptId:'',
        selectedPersons:[]
      },
    
      customerOptions:[],
      warningLevel: [],
      channel: [],
      disabledChannel: ['6'],
      rules: {
       
        warningLevel: { required: true, message: '请选择预警级别' },
        notificationChannel: { required: false, message: '请选择通知渠道' },
        receiveTime: { required: false, message: '请选择接受时段' },
        // message: { required: true, message: '请输入短信内容' },
        receivePhone: [
          { required: true, message: '请输入接受人' },
          { validator: validatePhone }
        ],
         receivePhoneArray: [
          { validator: validatePhoneArray }
        ]
        // msgContent: { required: true, message: '请输入短信内容' }
      },

      // warningLevel: '预警级别',
      //   indexName: '预警规则名称',
      //   referenceValue: '阈值',
      //   resultValue: '实际值',
      //   calibers: '引用类型'
      //   receiveTime: '受理日期',

      //* ******
      msgName: {
        time: 'XXXX年XX月XX日XX时',
        phone: '180********',
        url: 'http://xxxx.xx.xx',
        warningLevel: '预警级别',
        indexName: '预警规则名称',
        referenceValue: '阈值',
        resultValue: '实际值',
        receiveTime: '受理日期',
        calibers: '引用类型',
        orderAndPhoneNum:'工单号'
      },
      msgContent: ''
      //* ******
    }
  },
  
  deactivated() {
    this.$refs.form.clearValidate()
  },
  created() {
    
    // 如果没有短信设置默认短信
    if (!this.form.message) {
      this.msgContent = '#warningLevel# 您好，#indexName#已触发预警，服务请求类型条件为#calibers#；阀值为#referenceValue#，受理日期#receiveTime#，实际投诉量为#resultValue#，工单号为#orderAndPhoneNum#，请及时处理！'
    } else {
      this.msgContent = this.form.message
    }
    this.getLists()
  },
  mounted(){
    if(this.form.receivePhone){
      let tempArray = this.form.receivePhone.split(',')
      let receivePhoneArray = this.form.receivePhoneArray||[]
      receivePhoneArray.push.apply(receivePhoneArray,tempArray);
    }
  },
  methods: {
    openDialog(){
      this.stp3DialogVis = true;
    },
    cascaderChange(v){
      console.log('v>>>>',v)
      if(v&&v.length){
        let tar = v[v.length-1];//根据deptId 获取部门下面的人员
        console.log('tar=>',tar);
        this.getPersonsByDeptId(tar,this.deptInfoArr)
      }else{
        this.personOpts = []
      }
      
    },
    // 找出对应的部门下面的人员
    getPersonsByDeptId(id,arr){
      if(Array.isArray(arr)&&arr.length){
      
        arr.forEach(i=>{
         
          if(i.deptId==id){
            
            this.personOpts = i.userList||[]
          
          }else{
             
            if(i.childDeptAndUserInfoVOS&&i.childDeptAndUserInfoVOS.length){
              this.getPersonsByDeptId(id,i.childDeptAndUserInfoVOS)
            }
          }
        })
      }
    },
    dialogComfirm(){
      let tempArray = this.stp3DialogForm.selectedPersons||[];
      let receivePhoneArray = this.form.receivePhoneArray||[];
      tempArray.forEach(j=>{
        if(receivePhoneArray.indexOf(j)==-1){
          receivePhoneArray.push(j);
        }
      })
      console.log('receivePhoneArray=>',receivePhoneArray)
      console.log('tempArray=>',tempArray)
      if(tempArray&&tempArray.length){
        this.$set(this.form,'receivePhone',receivePhoneArray.join(','))
        
      }
      
      // receivePhoneArray.splice(0,receivePhoneArray.length);
      // receivePhoneArray.push.apply(receivePhoneArray,tempArray);
      this.stp3DialogForm.selectedPersons=[];
      this.stp3DialogForm.deptId=''
      this.stp3DialogVis = false;
   },
    clean(){
      this.stp3DialogForm.selectedPersons=[];
      this.stp3DialogForm.deptId=''
      this.stp3DialogVis = false;
    },

    receivePhoneArrayChange(v){
      console.log('v------',v)
      console.log('v==>',v.join(','))
      this.form.receivePhone = v.join(',')
    },
  
    // **********

    handleDrop(event) {},
    handleDragOver(event) {
      event.preventDefault()
    },
    handleEditChange(e) {},

    addToContent(type) {
      // 获取光标位置
      const selection = window.getSelection()
      const range = selection.getRangeAt(0)
      console.log(selection)
      console.log(range)
      if (range.startContainer.parentNode.className !== 'content') {
        return
      }
      range.setStart(range.endContainer, range.endOffset)
      const button = document.createElement('button')
      button.name = type
      button.disabled = true
      button.className = 'el-button el-button--default el-button--mini is-disabled'
      button.contentEditable = false
      button.draggable = true
      button.type = 'button'
      button.innerHTML = `<span>${this.msgName[type]}</span>`
      range.insertNode(button)

      this.$nextTick(() => {
        this.keepLastIndex(document.getElementById('editable'))
      })
    },

    keepLastIndex(obj) {
      var range = null
      if (window.getSelection) { // ie11 10 9 ff safari
        obj.focus() // 解决ff不获取焦点无法定位问题
        range = window.getSelection()// 创建range
        range.selectAllChildren(obj)// range 选择obj下所有子内容
        range.collapseToEnd()// 光标移至最后
      } else if (document.selection) { // ie10 9 8 7 6 5
        range = document.selection.createRange()// 创建选择对象
        // var range = document.body.createTextRange();
        range.moveToElementText(obj)// range定位到obj
        range.collapse(false)// 光标移至最后
        range.select()
      }
    },

    // 恢复默认
    setDefaultState() {
      // yjjb: '预警级别',
      // yjgzmc: '预警规则名称',
      // fz: '阈值',
      // sjz: '实际值',
      // slrq: '受理日期',
      // slhm: '受理号码',
      // gdlsh: '工单流水号',
      // yylx: '引用类型'

      //   warningLevel: '预警级别',
      //   indexName: '预警规则名称',
      //   referenceValue: '阈值',
      //   resultValue: '实际值',
      //   calibers: '引用类型'
      //   receiveTime: '受理日期',

      this.msgContent = '#warningLevel# 您好，#indexName#已触发预警，服务请求类型条件为#calibers#；阀值为#referenceValue#，受理日期#receiveTime#，实际投诉量为#resultValue#，工单号为#orderAndPhoneNum#，请及时处理！'
      this.$refs['message-edit'].innerHTML = this.decodeMSG(this.msgContent)
    },

    // 解析标识符为html
    decodeMSG(content) {
      const { msgName } = this

      //   warningLevel: '预警级别',
      //   indexName: '预警规则名称',
      //   referenceValue: '阈值',
      //   resultValue: '实际值',
      //   calibers: '引用类型'
      //   receiveTime: '受理日期',

      content = content.replace(new RegExp('#warningLevel#', 'g'), this.templateFactory('warningLevel', msgName.warningLevel))
      content = content.replace(new RegExp('#indexName#', 'g'), this.templateFactory('indexName', msgName.indexName))
      content = content.replace(new RegExp('#referenceValue#', 'g'), this.templateFactory('referenceValue', msgName.referenceValue))
      content = content.replace(new RegExp('#resultValue#', 'g'), this.templateFactory('resultValue', msgName.resultValue))
      content = content.replace(new RegExp('#receiveTime#', 'g'), this.templateFactory('receiveTime', msgName.receiveTime))
      content = content.replace(new RegExp('#calibers#', 'g'), this.templateFactory('calibers', msgName.calibers))
      content = content.replace(new RegExp('#orderAndPhoneNum#', 'g'), this.templateFactory('orderAndPhoneNum', msgName.orderAndPhoneNum))

      return content
    },
    // 替换html标签为标识符
    encodeMSG() {
      const contentDom = this.$refs['message-edit']
      let contentString = contentDom.innerHTML.trim()
      const doms = contentDom.querySelectorAll('button')
      for (let i = 0; i < doms.length; i++) {
        const item = doms[i]
        switch (item.name) {
          case 'time':
            contentString = contentString.replace(item.outerHTML, '#time#')
            break
          case 'url':
            contentString = contentString.replace(item.outerHTML, '%url%')
            break
          case 'tel':
            contentString = contentString.replace(item.outerHTML, '#tel#')
            break

            //       <!-- yjjb:'预警级别',
            // yjgzmc:'预警规则名称',
            // fz:'阈值',
            // sjz:'实际值',
            // slrq:'受理日期',
            // slhm:'受理号码',
            // gdlsh:'工单流水号',
            // yylx:'引用类型' -->

            //   warningLevel: '预警级别',
            //   indexName: '预警规则名称',
            //   referenceValue: '阈值',
            //   resultValue: '实际值',
            //   calibers: '引用类型'
            //   receiveTime: '受理日期',

          case 'warningLevel':
            contentString = contentString.replace(item.outerHTML, '#warningLevel#')
            break
          case 'indexName':
            contentString = contentString.replace(item.outerHTML, '#indexName#')
            break
          case 'referenceValue':
            contentString = contentString.replace(item.outerHTML, '#referenceValue#')
            break
          case 'resultValue':
            contentString = contentString.replace(item.outerHTML, '#resultValue#')
            break
          case 'receiveTime':
            contentString = contentString.replace(item.outerHTML, '#receiveTime#')
            break

          case 'calibers':
            contentString = contentString.replace(item.outerHTML, '#calibers#')
            break
          case 'orderAndPhoneNum':
            contentString = contentString.replace(item.outerHTML, '#orderAndPhoneNum#')
            break
          default:break
        }
      }
      const temDiv = document.createElement('div')
      temDiv.innerHTML = contentString
      return temDiv.innerText
    },
    templateFactory(name, val, dval) {
      // eslint-disable-next-line max-len
      return `<button name="${name}" disabled="disabled" class="el-button el-button--default el-button--mini is-disabled" contenteditable="false" draggable="true" type="button"><span contenteditable="false">${val || dval}</span></button>`
    },
    // **********
    getLists() {
      // 级别类型
      ajaxRequest('hvGwrnsj', { type: 'WJB' }).then((res) => {
        const { data: { data }} = res
        this.warningLevel = data
      })
      // 通知渠道
      // ajaxRequest('hvGwrnsj', { type: 'TZJD' }).then((res) => {
      //   const { data: { data }} = res
      //   this.channel = data
      // })
    },
    validate() {
      console.log(this.encodeMSG())
      this.$set(this.form, 'message', this.encodeMSG())
      if(!this.form.message) {
        this.$message.error('请输入短信')
        return
      }
      return new Promise((resolve) => {
        this.$refs.form.validate((valid) => {
          return valid ? resolve(true) : resolve(false)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form{
  width: 55%;
  margin: 40px auto 40px 24%;

  .el-date-editor,
  .el-select{
    width: 100%;
  }
}
.form-tip {
  color: #8c8c8c;
  font-size: 14px;
  line-height: 20px;
  padding-top: 12px;
}

/deep/ textarea {
  font-family: inherit;
}
.message-edit {
      text-align: left;
      .tag {
        color: orange;
      }
      /deep/ .el-dialog__body{
        text-align: left;
      }
      .content {
        background-color: #efeff4;
        padding: 8px;
        border-radius: 3px;
        margin: 10px 0;
        text-align: left;
        line-height: 30px;
        min-height: 140px;
        &:focus {
          outline: none;
        }
        .el-button.is-disabled span{
          background-color: red;
        }
      }

      /deep/ .el-button.is-disabled,
      /deep/ .el-button.is-disabled:focus,
      /deep/ .el-button.is-disabled:hover {
        color: #1C88DC;
        cursor: move;
      }
    }
  .el-cascader{
  width: 100%;
  /deep/.el-icon-arrow-down:before {
    content: "\E6E1";
  }
  /deep/.el-icon-arrow-down{
    transform: rotate(180deg);
  }
  /deep/.is-reverse.el-icon-arrow-down{
    transform: rotate(0deg);
  }

}
.el-form {
  width: 100%;
  margin-top: 20px;
  margin-left: 25px;
 }
</style>
