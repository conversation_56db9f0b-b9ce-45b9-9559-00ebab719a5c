<template>
  <div class="hunsty" style="position: relative">
    <div :id="t" class="hunsty" :style="{ opacity: allData.length ? 1 : 0 }" />
    <div
      :style="{
        position: 'absolute',
        left: 0,
        top: 0,
        width: '100%',
        height: '100%',
        display: allData.length ? 'none' : 'block',
      }"
    >
      <Blank2 />
    </div>

    <div
      :style="{
        position:'absolute',
        width:'400px',
        height:'auto',
        borderRadius:'5px',
        minHeight:'100px',
        left:left,top:0,display:tipshow?'block':'none',
        background:'rgba(255,255,255,1)',
        boxShadow:'0px 2px 8px 0px rgba(102, 61, 0, 0.16)',
        zIndex:99}"
    >
      <div style="text-align:right;padding-right:5px;position:absolute;right:0px;top:0px;">
        <i class="el-icon-close" style="cursor:pointer;font-size:24px;color:rgb(89, 89, 89);" @click="tipshow=false" />
      </div>
      <div style="padding:10px">
        <div class="tcon">
          <div style="padding-bottom:5px;font-size:14px;font-weight:bolder">{{ tipappartName }}</div>
          <div>
            <div>
              <span class="tlab" style="font-weight:bold">
                热点业务名称
              </span>
              <span class="tval" style="font-weight:bold">
                <span>投诉量 </span>

              </span>
              <span class="tval" style="font-weight:bold">
                投诉环比
              </span>
            </div>
            <div v-loading="tipLoading" style="min-height:200px">
              <div v-for="(el,idx) in tipData" :key="idx" class="cellx">
                <span class="tlab">
                  {{ el.business }}
                </span>
                <span class="tval">
                  {{ el.score }}
                </span>
                <span class="tval">
                  {{
                    el.momrate && el.momratetype && el.momratetype == 1
                      ? '-' + 100 * Number(el.momrate).toFixed(2) + '%'
                      : '-'
                  }}
                </span>
              </div>
            </div>

          </div>
          <div v-if="false" style="padding-top:30px">
            <Blank2 />
          </div>
        </div>
      </div>

    </div>

  </div>
</template>

<script>
import { getPopupTable } from '@/api/complaint-topic/index.js'
import tool from '@/views/bj_proatal_web/utils/utils'
export default {
  name: 'GLineChart',
  components: {},
  props: {
    dataList: {
      type: Object
    },
    // x轴 seriesData 对应的
    keymap: {
      type: Object,
      default: () => {
        return {
          xData: 'appartName',
          seriesData: ['score', 'yoyrate', 'momrate']
        }
      }
    },
    isShowTip: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    allData: {
      type: Array,
      default: () => {
        return []
      }
    },
    option: {
      type: Object,
      default: () => {
        return {}
      }
    },
    legendData: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 控制echart图显示多少
    barlineControl: {
      type: Array,
      default: () => {
        return ['bar', 'line', 'line']
      }
    },
    isDoubleLine: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dataZoom: [
        // 给x轴设置滚动条
        {
          start: 0, // 默认为0
          end: 100 - 1500 / 31, // 默认为100
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          handleSize: 0, // 滑动条的 左右2个滑动条的大小
          height: 8, // 组件高度
          left: 0, // 左边的距离
          right: 0, // 右边的距离
          bottom: 26, // 右边的距离
          handleColor: '#eee', // h滑动图标的颜色
          handleStyle: {
            borderColor: '#eee',
            borderWidth: '1',
            shadowBlur: 2,
            background: '#eee',
            shadowColor: '#eee'
          },
          backgroundColor: '#eee', // 两边未选中的滑动条区域的颜色
          showDataShadow: false, // 是否显示数据阴影 默认auto
          showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
          handleIcon:
            'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
          // filterMode: "filter",
          moveHandleStyle: {
            color: '#eee'
          }
        }
      ],
      t: '',
      chart: null,
      valueList: [],
      showChart: true,
      left: 0,
      top: 0,
      tipshow: false,
      tipLoading: true,
      tipData: [],
      tipappartName: ''
    }
  },
  computed: {
    xData() {
      const temp = this.allData.map((i) => i[this.keymap['xData']])
      return temp
    },
    seriesData() {
      const temp = []
      this.keymap['seriesData'].forEach((i, idx) => {
        temp[idx] = this.allData.map((x) => x[i])
      })

      return temp
    }
  },
  watch: {
    allData(v, oldv) {
      this.renderChart()
    }
  },
  created() {
    this.t = new Date().getTime() + (Math.random() * 100).toFixed(0)
  },
  mounted() {
    var _this = this
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', function() {
      _this.chart.resize()
    })
  },
  methods: {
    initChart() {
      this.chart && this.chart.dispose()
      const dom = document.getElementById(this.t)
      this.chart = this.$echarts.init(dom)
      this.renderChart()
      const _self = this

      this.chart.on('click', (params) => {
        console.log('params:', params)

        const l = params.event.event.zrX - 400 + 'px'
        _self.left = l
        console.log('_self.left:', _self.left)
        _self.top = params.event.event.zrY + 'px'

        _self.tipData = []
        const x = JSON.parse(JSON.stringify(_self.dataList))
        x.appartName = params.name
        _self.tipappartName = params.name
        _self.tipshow = true
        _self.tipLoading = true
        let tipData = []
        console.log('_self.dataList:', x)
        getPopupTable(x).then(res => {
          if (res.data && res.data.data.length) {
            tipData = res.data.data
            _self.tipData = tipData
          }
        }).finally(() => {
          _self.tipLoading = false
        })

        // params.event.event.zrX
      })
    },
    renderChart() {
      const _self = this
      const option = {
        dataZoom: this.seriesData[0].length > 8 ? this.dataZoom : null,
        grid: {
          top: '20%',
          left: '10%',
          right: '10%',
          bottom: '15%',
          containLabel: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: this.legendData,
          top: '2%',
          textStyle: {
            color: '#747474'
          }
        },
        xAxis: {
          type: 'category',
          // data: ['2022-02', '2022-03'],
          data: this.xData,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#393939'
            }
          }
        },
        yAxis: [
          {
            // max: 100,
            type: 'value',
            name: this.legendData[0],
            nameTextStyle: {
              color: '#393939',
              padding: [0, 0, 10, -40]
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#eeeeee'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#393939'
              }
            }
          },
          {
            // max: 2,
            type: 'value',
            name: _self.isDoubleLine ? '' : this.legendData[1],
            nameTextStyle: {
              color: '#393939',
              padding: [0, 0, 10, 35] // 四个数字分别为上右下左与原位置距离
            },
            position: 'right',
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#393939'
              },
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: this.legendData[0],
            type: 'bar',
            barWidth: 15,
            label: {
              show: true,
              position: 'top'
            },
            itemStyle: {
              normal: {
                color: '#0682FF',
                borderRadius: [1, 1, 0, 0]
              }
            },
            data: this.seriesData[0]
          },
          {
            name: this.legendData[1],
            type: 'line',
            yAxisIndex: 1,
            showAllSymbol: true, // 小圆点
            symbol: 'circle',
            symbolSize: 1,
            // smooth: true, // 平滑曲线
            itemStyle: {
              color: 'rgb(246, 175, 56)',
              borderWidth: '2',
              borderColor: 'rgb(246, 175, 56)'
            },
            lineStyle: {
              // color: '#7097F6' // 线颜色
              color: 'rgb(246, 175, 56)' // 线颜色
            },
            data: this.seriesData[1]
          },
          _self.isDoubleLine
            ? {
              name: this.legendData[2],
              type: 'line',
              yAxisIndex: 1,
              showAllSymbol: true, // 小圆点
              symbol: 'circle',
              symbolSize: 1,
              // smooth: true, // 平滑曲线
              itemStyle: {
                color: '#4CD1C3',
                borderWidth: '2',
                borderColor: '#4CD1C3'
              },
              lineStyle: {
                color: '#4CD1C3' // 线颜色
              },
              data: this.seriesData[2]
            }
            : null
        ]
      }

      if (this.isShowTip && false) {
        option.tooltip = {
          trigger: 'axis',
          backgroundColor: 'rgba(255,255,255,1)',
          textStyle: {
            color: '#262626',
            align: 'left'
          },
          transitionDuration: 0,
          triggerOn: 'click',
          //  confine: true,
          extraCssText: 'box-shadow:0px 2px 8px 0px rgba(102, 61, 0, 0.16)',
          formatter: function(datas, ticket, callback) {
            _self.dataList.appartName = datas[0].name
            // console.log( _self.loading," _self.loading");
            // if (!_self.loading) {
            //   _self.loading = true;
            if (!_self.loading) {
              _self.loading = true
              _self.$emit('isLoading', _self.loading)
              getPopupTable(_self.dataList).then((res) => {
                let html = ''
                if (res.data && res.data.data.length) {
                  html = `
                <div
      class="legend-right"
    >
      <div class="table-all" style=" display: flex;
  flex-direction: column;">
        <div class="table-line table-headers" style="display: flex;
    align-items: center;
    margin-bottom: 7px;">
          <div class="table-cells" style="height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px; min-width: 200px; color: #8c8c8c !important;">热点业务名称</div>
          <div class="table-cells" style=" height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px; width: 50px;color: #8c8c8c !important;">投诉量</div>
          <div class="table-cells" style=" height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px; width: 50px;color: #8c8c8c !important;">投诉环比</div>
        </div>`

                  res.data.data.forEach((el) => {
                    html += ` <div class="table-line" style="display: flex;
    align-items: center;
    margin-bottom: 7px;">
          <div class="table-cells" style=" min-width: 200px;height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px;">${el.business}</div>
          <div class="table-cells" style="  width: 50px;  height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px;">${el.score}</div>
          <div class="table-cells" style=" width: 50px; height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 14px;
      font-family: SourceHanSansSC, SourceHanSansSC-Regular;
      font-weight: 400;
      color: #262626;
      margin-right: 10px;">${
  el.momrate && el.momratetype && el.momratetype == 1
    ? '-' + 100 * Number(el.momrate).toFixed(2) + '%'
    : '-'
}</div>
        </div>`
                  })
                  html += `</div>
    </div>`
                } else {
                  if (datas && datas.length) {
                    datas.forEach((i) => {
                      i.value = tool.dealFloatNum(i.value)
                    })
                  }

                  let emostype = ''
                  if (!_self.isDoubleLine) {
                    try {
                      const flagIndx = datas[0].dataIndex
                      const flagObj = _self.allData[flagIndx]

                      emostype = Number(flagObj.eomstype)
                    } catch (error) {}
                  }
                  console.log('emostype:', emostype)

                  html =
                    "<span style='color:#777;line-height:30px;'>" +
                    datas[0].name +
                    '</span><br>' +
                    datas[0]?.marker +
                    "<span style='color:#222;padding:0 8px 0 8px;'>" +
                    datas[0].seriesName +
                    "</span><span style='font-weight:bold;line-height:30px;'>" +
                    datas[0]?.value +
                    (datas[0].seriesName.indexOf('满意度') != -1
                      ? ''
                      : String(datas[0].seriesName).indexOf('时长') != -1
                        ? ' 分钟'
                        : datas[0].seriesName === '万投比'
                          ? ' ‱'
                          : datas[0].seriesName === '投诉量'
                            ? ''
                            : ' %') +
                    '</span><br>' +
                    (datas[1]?.marker || '') +
                    "<span style='color:#222;padding:0 8px 0 8px;'>" +
                    datas[1]?.seriesName +
                    "</span><span style='font-weight:bold;line-height:30px;'>" +
                    datas[1]?.value +
                    (datas[1]?.value == '-' ? '' : '%') +
                    '</span><br>'
                }
                _self.loading = false
                _self.$emit('isLoading', _self.loading)
                callback(ticket, html)
                // _self.loading = false;
              })
              // }
            }
            return '数据查询中'
          }
        }
      }

      this.chart.setOption(option)
      if (this.allData.length == 0 || !this.allData) {
        this.chart.showLoading({
          text: '暂无数据',
          color: 'rgba(255, 255, 255, 0)',
          fontSize: 20,
          textColor: '#8a8e91',
          maskColor: 'rgba(255, 255, 255, 1)'
        })
      } else {
        this.chart.hideLoading()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.hunsty {
  height: 100%;
  width: 100%;
}
.cellx{
  padding:5px 0;
  *{
    vertical-align: middle;
  }
}
.tcon{
 color: rgb(89, 89, 89);
 font-size: 14px;
 min-height: 150px;
 .tlab{
    display: inline-block;
    width:40%;
    text-align: center;
    line-height: 22px;
 }
 .tval{
    display: inline-block;
    width:30%;
    text-align: center;
    line-height: 22px;
    img{
      margin-left: 10px;
    }
    *{
      vertical-align: middle;
    }
 }

}
</style>
