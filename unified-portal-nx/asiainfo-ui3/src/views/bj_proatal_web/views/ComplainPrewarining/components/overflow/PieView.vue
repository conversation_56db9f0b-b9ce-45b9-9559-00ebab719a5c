<template>
  <div class="hunsty" ref="pieview">
    <div :id="t"  style="width:100%;height:100%;" />
    <div style="position:absolute;top:5%;left:35%;right:0;height:30px;line-height:30px;">
      <span style="display:inline-block;width:40%;height:30px;text-align:left;padding-left:10px;" />
      <span style="display:inline-block;width:32%;height:30px;text-align:left;color:#8c8c8c;font-size:14px">数量</span>
      <span style="display:inline-block;width:28%;height:30px;text-align:left;padding-left:20px;color:#8c8c8c;font-size:14px">占比</span>
    </div>

  </div>

</template>

<script>
import { eventBus } from '@/main.js'
// import mergeObj from "@/vendor/Utils/merage.js";
// 求data中value的总和
function arrCount(arr) {
  let count = 0
  arr.forEach(item => {
    count = count + Number(item.value)
  })
  return count
}
export default {
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    option: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      t: '',
      chart: null,
      valueList: [],
      clientWidth:0,
      
    }
  },
  watch: {
    data() {
      this.initChart()
    }
  },

  created() {
    this.t = new Date().getTime() + (Math.random() * 100).toFixed(0) + Math.random()
    eventBus.$on('changeIsCollapse', this.changeIsCollapse)
  },
  mounted() {
    this.clientWidth = Math.floor(this.$refs['pieview'].clientWidth * 0.65);
    console.log('clientWidth:',this.clientWidth)

    
    var _this = this
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', function() {
      _this.chart.resize()
    })
  },
  methods: {
    changeIsCollapse() {
      setTimeout(() => {
        this.chart.resize()
      }, 500)
    },
    initChart() {
      const dom = document.getElementById(this.t)

      const _this = this
      this.chart = this.$echarts.init(dom)
      this.chart && this.chart.off('legendselectchanged')

      this.chart && this.chart.on('legendselectchanged', function(params) {
        _this.chart.setOption({
          legend: { selected: { [params.name]: true }}
        })
        console.log('this.data:', _this.data)
        console.log(params)
        let temp = null
        _this.data.forEach(j => {
          if (j.name == params.name) {
            temp = j
          }
        })
        console.log('点击了', temp)
        _this.$emit('pieClick', temp)
        // do something
      })
      let numCount;
      if(this.data&&Array.isArray(this.data)&&this.data.length){
          numCount = arrCount(this.data)
      }
     
      const options = {
         title: {
          show: numCount?true:false,
          left: '14%',
          top: '30%',
          textAlign: 'center',
          text: '投诉量',
          subtext: numCount,
          textStyle: {
            fontSize: 16,
            fontWeight: 500,
            color: '#262626'

          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bolder',
            color: '#262626',
            lineHeight: 24
          }
        },
        color: [
          '#FF8000', '#FEFF00', '#00EA86', '#0083FF'
        ],

        tooltip: {
          trigger: 'item',
          // position: ['30%', '60%']
        },
        legend: {
          // type: "scroll",
          top: '25%',
          right: '1%',
          left: '35%',
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 15,
          orient: 'vertical',
          tooltip: {
            show: true
          },
          textStyle: {
            rich: {
              name: {
                verticalAlign: 'right',
                align: 'left',
                width: Math.floor(this.clientWidth * 0.35),
                fontSize: 14,
                overflow: 'hidden'
              },
              val: {
                align: 'left',
                width: Math.floor(this.clientWidth * 0.35),
                fontSize: 14
              },
              partrate: {
                align: 'left',
                width: Math.floor(this.clientWidth * 0.3),
                fontSize: 14
              }

            }
          },
          formatter(name) {
            // console.log('_this.data', _this.data)
            // console.log('name:', name)
            // 找到data中name和文本name值相同的对象
            let val = _this.data.filter(item => {
              return item.name === name
            })[0]?.value
            val = (val === 0 || val === '0') ? '0' : val ? `${val}` : '-'

            let partrate = _this.data.filter(item => {
              return item.name === name
            })[0]?.partrate
            partrate = (partrate === 0 || partrate === '0') ? '0%' : partrate ? `${partrate}%` : '-'

            if (name.length > 12) {
              name = name.substring(0, 12) + '...'
            }
            return '{name| ' + name + '}' + '{val| ' + val + '}' + '{partrate| ' + partrate + '}'
          }
        },
        series: {
          type: 'pie',
          radius: ['38%', '50%'],
          center: ['15%', '40%'],
          avoidLabelOverlap: false,

          label: {
            show: false,
            position: 'center'
          },
          labelLine: {
            show: false
          },
          data: this.data
        }
      }
      // let merageobj = mergeObj(options, this.option);
      this.chart && this.chart.setOption(options)
    }
  }
}
</script>

<style lang="scss" scoped>
.hunsty{
  width:100%;
  height:100%;

}
</style>
