<template>
  <div>
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      class="form-overview"
      label-width="130px"
      label-suffix=" :"
      
    >
      <el-form-item label="归属地市" prop="localCity">
        <el-cascader
          v-model="form.localCity"
          style="width:60%"
          :placeholder="'请选择地市'"
          :options="area"
          :props="props"
        />
      </el-form-item>
      <el-row v-show="false">
        <el-col :span="11">
          <el-form-item label="指标关系" prop="indexRelationShip">
            <el-select v-model="form.indexRelationShip">
              <el-option value="或" label="或" />
              <el-option value="与" label="与" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="统计周期" prop="statisticalCycle">
            <el-select v-model="form.statisticalCycle">
              <el-option value="当日实时" label="当日实时" />
              <el-option value="日" label="日" />
              <el-option value="周" label="周" />
              <el-option value="月" label="月" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-form-item prop="loopWarn" label-width="20px">
            <el-checkbox v-model="form.loopWarn" :true-label="1" :false-label="0">循环触发</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="5">
        <el-col :span="11">
          <el-form-item label="取数口径" prop="correspondenceTable" :rules="{required:true}">
            <el-select v-model="form.correspondenceTable" @change="correspondenceTableChange">
              <el-option
                v-for="op in paramTypes"
                :key="op.type"
                :label="op.complanitWarningLevelType"
                :value="op.type"
              />
            </el-select>

          </el-form-item>

        </el-col>
        <el-col :span="11">
          <el-form-item label="选择下级口径" prop="calibers" :rules="{required:true,message:'选择下级口径'}">
            <!-- <el-cascader
              v-model="form.calibers"
              :options="qsCascaderOptions"
              :props="qsprops"
              collapse-tags
              clearable
              @change="calibersChange"
            /> -->
            <el-button size="small" type="primary" @click="dialogTableVisible=true">下级口径</el-button>
          </el-form-item>

        </el-col>
      </el-row>
      <el-row :gutter="5">
        <el-col v-show="false" :span="11">
          <el-form-item label="指标类型" prop="indexType">
            <el-select v-model="form.indexType">
              <el-option
                v-for="op in indicatorTypes"
                :key="op.typeid"
                :label="op.complanitwarningleveltype"
                :value="op.typeid"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item label="阈值基准值" prop="referenceValue">
            <el-input v-model="form.referenceValue" />
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>

    <div v-show="false" class="form-parameter">
      <div class="form-parameter-title">取数口径：</div>
      <el-form ref="detailForm" :model="detailForm" :hide-required-asterisk="true">
        <div v-for="(dl,index) in detail" :key="dl.__number" class="form-parameter-item">
          <el-row :gutter="20" style="position:relative;">
            <el-col :span="10">
              <el-form-item
                :label="`${index+1}、`"
                label-width="30px"
                :prop="`${dl.__number}correspondenceTable`"
                :rules="{ required: true, message: '请选择'}"
              >
                <el-select v-model="dl[`${dl.__number}correspondenceTable`]" @change="changeDetailType($event,dl)">
                  <el-option
                    v-for="op in paramTypes"
                    :key="op.type"
                    :label="op.complanitWarningLevelType"
                    :value="op.type"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="dl.__indicators ? 5 : 14">
              <el-form-item :prop="`${dl.__number}indicators`" :rules="{ required: true, message: '请选择'}">
                <el-select
                  v-model="dl[`${dl.__number}indicators`]"
                  value-key="fetchingId"
                  multiple
                  @change="changeIndicator($event,dl)"
                >
                  <el-option
                    v-for="op in (indicators[dl[`${dl.__number}correspondenceTable`]] || [])"
                    :key="op.fetchingId"
                    :label="op.fetchingName"
                    :value="op"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 选中特定的指标，显示目录树 -->
            <el-col v-show="dl.__indicators" :span="9">
              <el-form-item
                :prop="`${dl.__number}indicatorTree`"
                :rules="dl.__indicators ? (dl.__levelAndName ? null : { required: true, message: '请选择'}) : null"
              >

                <!-- 回显之前显示的数据 -->
                <div v-if="dl.__levelAndName" class="el-cascader__tags el-cascader__levelName">
                  <el-tag type="info" size="small" effect="light" closable @close="handleLevelNameClose(dl)">{{ dl.__levelAndName }}</el-tag>
                </div>
                <!-- dl.__loading主要是编辑的时候会有查询加载 -->
                <el-cascader
                  v-model="dl[`${dl.__number}indicatorTree`]"
                  v-loading="dl.__loading"
                  :placeholder="'请选择'"
                  :disabled="dl.__loading"
                  :class="{'select-loading':dl.__loading}"
                  element-loading-spinner="el-icon-loading"
                  :options="(treeData[dl.__number] || [])"
                  collapse-tags
                  :props="treeProps"
                  popper-class="tree-cascader"
                  @change="handleTreeChange(dl)"
                >
                  <template slot-scope="{ data }">
                    <div class="cascader-slot-item" @click="getSubTreeData(data)">
                      <span>{{ data.filedName }}</span>
                      <i v-if="!data.leaf" :class="[data.__loading ? 'el-icon-loading' : 'el-icon-arrow-right']" />
                    </div>
                  </template>
                </el-cascader>

              </el-form-item>
            </el-col>
            <!-- 删除按钮,详情项大于1才显示-->
            <i v-show="detail.length > 1" class="el-icon-error form-parameter-delete" @click="deleteFormItem(index)" />
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10">
              <el-form-item
                label="指标类型: "
                label-width="100px"
                :prop="`${dl.__number}indexType`"
                :rules="{ required: true, message: '请选择'}"
              >
                <el-select v-model="dl[`${dl.__number}indexType`]" @change="changeIndicatorType($event,dl)">
                  <el-option
                    v-for="op in indicatorTypes"
                    :key="op.typeid"
                    :label="op.complanitwarningleveltype"
                    :value="op.typeid"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="dl.__indexType ? 7 : 14">
              <el-form-item
                label="阈值基准值: "
                label-width="90px"
                :prop="`${dl.__number}referenceValue`"
                :rules=" dl.__indexType ? (dl.__referenceValueDisable ? null : { required: true, message: '请输入阈值'}):{ required: true, message: '请输入阈值'}"
              >
                <el-input
                  v-model="dl[`${dl.__number}referenceValue`]"
                  :disabled="dl.__indexType && dl.__referenceValueDisable"
                />
              </el-form-item>
            </el-col>
            <!-- 选中特定的指标类型，显示预警比例-->
            <el-col v-if="dl.__indexType" :span="7">
              <el-form-item
                label="触发预警比例: "
                label-width="105px"
                :prop="`${dl.__number}warningProportion`"
                :rules="dl.__indexType ? { required: true, message: '请输入预警比例'} : {}"
              >
                <el-input v-model="dl[`${dl.__number}warningProportion`]">
                  <el-button slot="append">%</el-button>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="form-parameter-add">
          <i class="el-icon-circle-plus" @click="addFormItem" />
        </div>
      </el-form>
    </div>
    <el-dialog title="选择口径" :visible.sync="dialogTableVisible" center width="85%">
      <el-form v-model="dialogForm" :inline="true">
        <el-row>
          <el-form-item>
            <el-select v-model="dialogForm.secondType" placeholder="请选择" size="small" @change="secondTypeChange">
              <el-option
                v-for="item in qsCascaderOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="关键字">
            <el-input v-model="dialogForm.keywords" placeholder="请输入内容" size="small" />
          </el-form-item>

          <el-form-item>
            <el-button size="small" type="primary" @click="queryFields">查询</el-button>
          </el-form-item>

        </el-row>
        <el-row v-loading="dialogTableLoading">
          <el-table
            ref="multipleTable"
            border
            :data="dialogTableData"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
            />
            <el-table-column
              prop="thirdName"
              label="名称"
            />
          </el-table>
          <!-- 分页功能 -->
          <div style="padding:10px 0;background:#fff;text-align:right">
            <el-pagination
              v-if="dialogTableData"
              :current-page="page.current"
              :page-sizes="[10, 20]"
              :page-size="page.size"
              layout="total, sizes, prev, pager, next, jumper"
              :pager-count="5"
              :total="page.total"
              @size-change="sizeChange"
              @current-change="pageCurrentChange"
            />
          </div>

        </el-row>
        <el-row>
          <el-col>
            <div style="font-weight:bold;margin-bottom:10px">已选项 </div>
            <!-- <div><el-button @click="add">增加</el-button></div> -->
            <div style="border:1px solid #e6e6e6;min-height:100px;overflow-y:scroll">
              <el-tag
                v-for="tag in choosed"
                :key="tag.thirdName"
                style="margin:5px"
                closable
                @close="handleTagClose(tag)"
              >
                {{ tag.thirdName }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="clean">清 除</el-button>
        <!-- <el-button size="small" @click="dialogCancel">取 消</el-button> -->
        <el-button type="primary" size="small" @click="dialogComfirm">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import ajaxRequest, { selectFirstMenu, selectSecondMenu, getSecondMenu, getThirdMenu, selectDirectoryTree } from '@/api/complain-warning'
// 初始化表单的数据结构
const paramForm = {
  correspondenceTable: '', // 指标大类
  indicators: [], // 指标名称
  indicatorTree: '', // 目录树
  indexType: '', // 指标类型
  referenceValue: '', // 阈值基准值
  warningProportion: '' // 触发预警比例
}

let correspondenceTable = ''

export default {
  name: 'Step2',
  props: ['form', 'treeLevel'],
  data() {
    var validateReferenceValue = (rule, value, callback) => {
      console.log('value:', value)
      if (!value && value !== '0' && value !== 0) {
        callback()
      } else if (/^(?!-)(\d*[1-9]+0*(\.\d{1,6})?$|0\.\d{1,6}$)/.test(value)) {
        callback()
      } else {
        callback(new Error('请输入大于0的数字或者不填'))
      }
    }
    var validateCalibers = (rule, value, callback) => {
      console.log('this.form.calibers:', this.form.calibers)
      if (this.form.calibers && this.form.calibers.length) {
        callback()
      } else {
        callback(new Error('请选择下级口径'))
      }
    }
    return {
      multipleSelection:[],
      area: [],
      props: {
        label: 'name',
        value: 'id',
        checkStrictly: true,
        emitPath: false
      },
      rules: {
        localCity: { required: true, message: '请选择归属地市' },
        indexRelationShip: { required: false, message: '请选择指标关系' },
        statisticalCycle: { required: false, message: '请选择统计周期' },
        referenceValue: [
          { validator: validateReferenceValue }
        ],
        calibers: [
          { validator: validateCalibers }
        ]
      },
      numberIndicatorTypeIds: ['10'], // 不显示比例的id
      paramTypes: [],
      indicators: {},
      indicatorTypes: [],
      treeProps: {
        label: 'filedName',
        value: 'nodeId',
        multiple: true,
        checkStrictly: true,
        emitPath: true
      },
      treeData: {},
      detail: [],
      number: 0,
      // 20221009取数口径从新做
      // ******start*****************/
      dialogTableLoading: false,
      dialogTableVisible: false,
      dialogTableData: [],
      choosed: [
        // {
        //   firstName: 'GCMX',
        //   secondName: 'val_21',
        //   thirdName: '银川0分公司'
        // }

      ],
      page: {
        current: 1,
        size: 10,
        total: 0

      },
      dialogForm: {
        secondType: '',
        keywords: ''
      },
      qsCascaderOptions: [],
      qsprops: {
        multiple: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level, value } = node
          if (level > 1) resolve([])
          getThirdMenu({ secondType: node.value, firstType: correspondenceTable }).then(res => {
            const { data } = res
            let nodes = []
            if (data && Array.isArray(data)) {
              nodes = data.map(i => {
                const obj = {
                  label: i,
                  value: i,
                  leaf: true
                }
                return obj
              })
            }
            resolve(nodes)
          })
          // setTimeout(() => {
          //   const nodes = Array.from({ length: level + 1 })
          //     .map(item => ({
          //       value: Math.random().toFixed(1),
          //       label: `选项${Math.random().toFixed(1)}`,
          //       leaf: level >= 2
          //     }))
          //     // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          //   resolve(nodes)
          // }, 1000)
        }
      }

      //* *****************over */

    }
  },
  computed: {
    detailForm() {
      const form = {}
      this.detail.forEach((item) => {
        Object.assign(form, item)
      })
      return form
    }
  },
  deactivated() {
    this.$refs.form.clearValidate()
    this.$refs.detailForm.clearValidate()
  },
  created() {
    this.getLists()
    if (this.form.rulerId) {
      console.log('this.form:', this.form)
      this.processSysWarningParam()
    } else {
      this.initEmptyForm()
    }
    //* ***************
    if (this.form.correspondenceTable) {
      this.correspondenceTableChange(this.form.correspondenceTable)
    }
    console.log('this.form.calibers:', this.form.calibers)
    this.choosed = this.form.calibers || []
  },

  methods: {
    // *********

    clean() {
      this.$refs.multipleTable.clearSelection();
      this.choosed = []
      this.$set(this.form, 'calibers', [])
    },
    dialogCancel() {
      this.choosed = []
      this.dialogTableVisible = false
    },
    dialogComfirm() {
      if(!this.choosed.length){
        this.$message.error('至少选择一个口径')
      }else{
        this.dialogTableVisible = false
      }
      
    },

    handleTagClose(tag) {
      console.log('tag:', tag)
      this.choosed.splice(this.choosed.indexOf(tag), 1)
    },
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      // 下方添加查询逻辑
      this.$nextTick(() => {
        this.queryFields()
      })
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.$nextTick(() => {
        this.queryFields()
      })

      // 下方添加查询逻辑
    },
    secondTypeChange(v) {
      this.$nextTick(() => {
        this.queryFields()
      })
    },
    queryFields() {
      // keywords,currentPage,pageSize
      const { current, size } = this.page
      const { keywords, secondType } = this.dialogForm
      const p = {
        firstType: correspondenceTable,
        secondType,
        keywords,
        currentPage: current,
        pageSize: size
      }
      this.dialogTableLoading = true
      getThirdMenu(p).then(res => {
        if (res.code == 200 && res.data) {
          const { records, current, total } = res.data
          this.page.currentPage = current
          this.page.total = total
          let temp = []
          //     firstName: 'GCMX',
          // secondName: 'val_25' + Math.random().toFixed(2),
          // thirdName: '小学生' + Math.random().toFixed(2)
          if (Array.isArray(records)) {
            temp = records.map(i => {
              return {
                firstName: correspondenceTable,
                secondName: secondType,
                thirdName: i
              }
            })
          }
          console.log('temp:', temp)
          this.dialogTableData = temp
        }
      }).finally(() => {
        this.dialogTableLoading = false
      })
    },

  
    handleSelectionChange(val) {
      
      let temp = this.choosed.concat(val)
      temp = this.filterArray(temp, 'thirdName')
      console.log('去重后的数组：', temp)
      this.choosed = temp || []
      this.$set(this.form, 'calibers', temp)
      console.log('this.form:', this.form)
    },
    filterArray(arr, id) {
      var have = {}
      return arr.reduce((item, next) => {
        if (!have[next[id]]) {
          have[next[id]] = next
          item.push(next)
        }
        return item
      }, [])
    },
    correspondenceTableChange(val) {
      console.log('取数口径选择的表：', val)
      correspondenceTable = val
      getSecondMenu({ firstType: val }).then(res => {
        const { data } = res
        if (data && Array.isArray(data)) {
          data.forEach(i => {
            i.label = i.name
          })
        }
        this.qsCascaderOptions = data || []
        if (data.length) {
          this.dialogForm.secondType = data[0].value
          this.queryFields()
        }
      })
    },
    // *********
    calibersChange(v) {
      console.log(v)
      console.log(this.form)
      const { correspondenceTable } = this.form
      let temp = []
      temp = v.map((i, idx) => {
        const obj = {
          firstName: correspondenceTable,
          secondName: i[0],
          thirdName: i[1]

        }
        return obj
      })
      console.log('temp:', temp)
    },
    handleLevelNameClose(dl) {
      dl.__levelAndName = ''
      this.$forceUpdate()
    },
    // 改变就充值__levelAndName数据
    handleTreeChange(dl) {
      dl.__levelAndName = ''
    },
    // 指标类型改变
    changeDetailType(val, item) {
      item[`${item.__number}indicators`] = []
      item[`${item.__number}indicatorTree`] = ''
      item.__levelAndName = ''
      this.querySecondMenu(val)
    },
    // 指标名称改变
    async changeIndicator(value, item) {
      const [last] = [].concat(value).reverse()
      if (!last) return
      // level为0表示没有下集目录树，不为0表示有下级目录树
      if (last.level) {
        item[`${item.__number}indicators`] = [last]
        item[`${item.__number}indicatorTree`] = ''
        item.__levelAndName = ''
        item.__indicators = true
        this.$set(this.treeData, item.__number, [])
        this.$set(item, '__loading', true)
        const data = await this.queryDirectoryTree({ level: last.level, filedName: '' })
        this.$set(this.treeData, item.__number, data)
        this.$set(item, '__loading', false)
      } else if (item.__indicators) {
        item[`${item.__number}indicators`] = [last]
        item[`${item.__number}indicatorTree`] = ''
        item.__levelAndName = ''
        item.__indicators = false
      }
    },
    // 指标类型改变
    changeIndicatorType(value, item) {
      item.__indexType = !this.numberIndicatorTypeIds.includes(value)
      if (item.__indexType && item.__referenceValueDisable) {
        item[`${item.__number}referenceValue`] = ''
        this.$refs.detailForm.clearValidate(`${item.__number}referenceValue`)
      }
    },
    // 删除参数
    deleteFormItem(index) {
      this.detail.splice(index, 1)
    },
    // 校验表单，返回数据
    validate() {
      return new Promise((resolve) => {
        let result = true
        this.$refs['form'].validate((valid) => {
          if (valid) {
            result = result && true
          } else {
            result = false
          }
        })
        // 20221009 隐藏之前的detailForm
        // this.$refs['detailForm'].validate((valid) => {
        //   if (valid) {
        //     result = result && true
        //   } else {
        //     result = false
        //   }
        // })
        // 如果表单校验通过，处理表单数据
        if (result) {
          const sysWarningParams = []
          this.detail.forEach((item) => {
            const list = {}
            for (const prop in item) {
              if (prop.indexOf(item.__number) === 0) {
                const [field] = prop.split(item.__number).reverse()
                if (field !== 'indicators' && field !== 'indicatorTree') {
                  list[field] = item[prop]
                }
              }
            }
            if (item.__indicators) {
              const [{ fetchingId: levelNum, level: levelName }] = item[`${item.__number}indicators`]
              // __levelAndName存在说明之前查询返回的参数未做修改，直接引用
              if (item.__levelAndName) {
                sysWarningParams.push({
                  ...list,
                  detailsType: '1',
                  levelAndName: item.__levelAndName,
                  sysWarningParams: [{ levelNum, levelName }]
                })
              } else {
              // 有目录树时，从目录树增加参数列表
                const trees = item[`${item.__number}indicatorTree`]
                // 处理目录树数据关键函数processTreeCheckedNode
                const levelAndNames = this.processTreeCheckedNode(trees)
                levelAndNames.forEach((levelAndName) => {
                  sysWarningParams.push({
                    ...list,
                    detailsType: '1',
                    levelAndName: levelAndName.join('-'),
                    sysWarningParams: [{ levelNum, levelName }]
                  })
                })
              }
            } else {
              // 从下拉组件增加参数列表
              item[`${item.__number}indicators`].forEach((item) => {
                const { fetchingId, fetchingName, level } = item
                sysWarningParams.push({
                  ...list,
                  fetchingId,
                  fetchingName,
                  detailsType: '2',
                  sysWarningParams: [{
                    levelNum: fetchingId,
                    levelName: level
                  }]
                })
              })
            }
          })
          resolve({ sysWarningParams })
        } else {
          resolve(result)
        }
      })
    },
    // 处理cascader的树数据,只处理被勾选的节点数据，排除未选中的父辈节点
    processTreeCheckedNode(nodes) {
      const levelName = []
      nodes.forEach((node) => {
        node.reduce((pre, cur, index) => {
          if (index == node.length - 1) {
            return pre.push({ __checked: true, __label: cur })
          } else {
            let [item] = pre.filter(({ __label }) => __label === cur)
            if (item) {
              if (!item.__children) { item.__children = [] }
              return item.__children
            } else {
              item = { __children: [], __label: cur }
              pre.push(item)
              return item.__children
            }
          }
        }, levelName)
      })
      return this.processLevelName(levelName, [])
    },
    // 拼接levelName数据
    processLevelName(checkedNode, initAry = []) {
      const totalAry = []
      checkedNode.forEach(({ __checked, __label, __children }) => {
        let ary = [].concat(initAry)
        if (__checked) {
          const [fetchingName, level] = __label.split('__')
          ary.push(`${fetchingName}(${level})`)
        }
        if (__children) {
          ary = this.processLevelName(__children, ary)
          totalAry.push(...ary)
        } else {
          totalAry.push(ary)
        }
      })
      return totalAry
    },
    addFormItem() {
      this.number++
      this.initEmptyForm()
    },
    initEmptyForm() {
      const form = {
        __number: `${this.number}__`,
        __indicators: false, // 是否选中只能单选的指标
        __indexType: false, // 指标类型
        __referenceValueDisable: true // 阈值是否可以编辑，referenceValue 有值而且数据类型__indexType为true时才可以编辑
      }
      for (const prop in paramForm) {
        form[`${form.__number}${prop}`] = typeof paramForm[prop] === 'object' ? [] : paramForm[prop]
      }
      // 如果有规则id，赋值规则id
      if (this.form.rulerId) {
        form[`${form.__number}rulerId`] = this.form.rulerId
      }
      this.detail.push(form)
    },
    getLists() {
      return Promise.all([
        // 查询地址
        ajaxRequest('6MujCDCs').then((res) => {
          const { data: [data] } = res
          this.area = data
        }),
        // 查询参数大类类别
        selectFirstMenu().then((res) => {
          // 只要GCMX这个表
          this.paramTypes = res.data.filter(i => i.type == 'GCMX')
        }),
        // 指标类型
        ajaxRequest('hvGwrnsj', { type: 'ZBLX' }).then((res) => {
          const { data: { data }} = res
          this.indicatorTypes = data
        })
      ])
    },
    // 查询二级菜单数据
    querySecondMenu(val) {
      if (this.indicators[val]) return Promise.resolve(this.indicators[val])
      return selectSecondMenu({ type: val }).then((res) => {
        this.$set(this.indicators, val, res.data)
        return res.data
      })
      // ******************
      // return getSecondMenu({ firstType: val }).then((res) => {
      //   this.$set(this.indicators, val, res.data)
      //   return res.data
      // })
      // ******************
    },
    // 查询目录树数据
    async queryDirectoryTree(data) {
      try {
        const res = await selectDirectoryTree(data)
        return res.data.map((item) => {
          item.nodeId = `${item.filedName}__${item.level}`
          item.leaf = !!item.isLast
          if (!item.isLast) item.children = []
          return item
        })
      } catch (e) {
        return []
      }
    },
    async getSubTreeData(data) {
      if ((data.children && data.children.length) || data.leaf) return
      data.__loading = true
      try {
        const child = await this.queryDirectoryTree({ filedName: data.filedName, level: Number(data.level) + 1 })
        this.$set(data, 'children', child)
        this.$forceUpdate()
        data.__loading = false
      } catch (e) {
        data.__loading = false
      }
    },
    // 编辑时处理规则查询回来的数据
    processSysWarningParam() {
      this.form.sysWarningParams.forEach((item) => {
        this.number++
        // 判断是否是数值，显示比例输入
        const __indexType = !this.numberIndicatorTypeIds.includes(item.indexType) // 指标类型
        // referenceValue判断是否有值
        const referenceValue = !item.referenceValue ? String(item.referenceValue) === '0' : true

        const form = {
          __number: `${this.number}__`,
          [`${this.number}__rulerId`]: item.rulerId, // 规则id
          __indicators: !!item.hasOwnProperty('indicatorTree'), // 是否选中只能单选的指标
          __indexType, // 指标类型
          // 阈值是否可以编辑，referenceValue 有值而且数据类型__indexType为true时才可以编辑
          __referenceValueDisable: !!(!referenceValue && __indexType),
          // 目录树loading，有可能数据查询较久
          __loading: false
        }
        // 赋值其他的字段
        for (const prop in paramForm) {
          if (prop === 'indicators') {
            form[`${form.__number}${prop}`] = [
              { fetchingId: item.fetchingId, fetchingName: item.fetchingName }
            ]
          } else {
            form[`${form.__number}${prop}`] = item[prop]
          }
        }
        // 查询二级下拉菜单的数据
        this.querySecondMenu(item.correspondenceTable).then((data) => {
          // 查询目录树数据
          if (item.hasOwnProperty('indicatorTree')) {
            // __levelAndName 之前的数据保存
            if (item.hasOwnProperty('levelAndName')) {
              form.__levelAndName = item.levelAndName
            }
            const [sel] = data.filter(({ fetchingId }) => {
              return fetchingId === item.fetchingId
            })
            if (sel) {
              this.$set(form, `${form.__number}indicators`, [{ ...sel }])
              // 递归过去树级节点的数据
              let levelAry = [].concat(this.treeLevel[item.detailsId] || [])
              // 如果没有父级节点时，level会是0；
              if (!levelAry[0].level) {
                levelAry = [{ level: sel.level }]
              }
              this.processTreeLevel(levelAry, null, 0, form)
            }
          }
        })
        this.detail.push(form)
      })
    },
    /**
    * 编辑，递归查询树形数据
    * @param ary 父辈节点层级数组
    * @param treeData 需要赋值的对象变量
    * @param index 递归的索引，用于判断
    * @param form 目录树对应的表单项
    */
    async processTreeLevel(ary, treeData, index, form) {
      const [{ levelNum, level }, ...next] = ary
      if (index == 0) this.$set(form, '__loading', true)
      const data = await this.queryDirectoryTree(
        index > 0 ? { ids: levelNum } : { level }
      )
      if (index > 0) {
        this.$set(treeData, 'children', data)
      } else {
        this.$set(this.treeData, form.__number, data)
      }
      let nextTreeData
      /**
      *  todo 根据levleNum筛选数据，现在返回的数据有问题
      *  现在只是根据层级下拉框查询了第一级的数据，所以没有递归查询，功能暂时无影响
      *  如果需要层级的选中，则需要后台处理的层级数据
      */
      if (index > 0) {
        if (next.length) {
          [nextTreeData] = data.filter(({ fetchingId }) => {
            return fetchingId === next[0].levelNum
          })
        }
      } else {
        [nextTreeData] = data.filter(({ filedName }) => {
          return filedName === levelNum
        })
      }
      // 有下一级递归查询，根据现在的逻辑，暂时没有下一级的数据查询
      if (nextTreeData) {
        const { fetchingId, fetchingName } = nextTreeData
        const values = form[`${form.__number}indicatorTree`]
        // 在倒数第二的位置插入父级数据
        values[0].splice(values.length - 2, 0, `${fetchingId}__${fetchingName}`)
        this.processTreeLevel(index === 0 ? ary : next, nextTreeData, index + 1, form)
      } else {
        // 没有递归结束，关闭loading
        this.$set(form, '__loading', false)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-overview{
  width: 60%;
  margin: 40px auto 0px 17%;
  .el-select{
    width: 100%;
  }
}
.form-parameter {
  width: 68%;
  margin: 0px auto 20px 16%;
 .form-parameter-title {
  font-size: 14px;
  font-weight: 500;
 }
 .el-form {
  width: 100%;
  margin-top: 20px;
  margin-left: 25px;
 }
 .el-select{
    width: 100%;
 }
 /deep/ .el-input-group__append{
   padding: 0 10px;
 }
}
.form-parameter-add {
  text-align: center;
  padding: 0 20px 20px;
  i{
    font-size: 22px;
    color: #ff9900;
    cursor: pointer;
  }
}
.form-parameter-delete{
  color: #ff3333;
  font-size: 22px;
  position: absolute;
  right: -30px;
  top:50%;
  transform: translateY(-90%);
  cursor: pointer;
}
.el-cascader{
  width: 100%;
  /deep/.el-icon-arrow-down:before {
    content: "\E6E1";
  }
  /deep/.el-icon-arrow-down{
    transform: rotate(180deg);
  }
  /deep/.is-reverse.el-icon-arrow-down{
    transform: rotate(0deg);
  }

}
.select-loading{
  /deep/ .el-loading-mask{
    background-color: rgba(255, 255, 255, 0)
  }
}
/deep/.el-dialog__header{
  padding:10px 0 0 0;
  font-weight: bold;
}
</style>

<style lang="scss">
 .tree-cascader {

  .el-cascader-node {
    padding-right: 0!important;
  }
 .el-cascader-node__postfix{
    display: none;
  }
  .cascader-slot-item {
    display: flex;
    align-items: center;

    span{
      flex: 1;
    }
  }
}
.el-cascader__levelName{
  z-index: 10;
  display: inline-block;
  right: auto;
}
</style>
