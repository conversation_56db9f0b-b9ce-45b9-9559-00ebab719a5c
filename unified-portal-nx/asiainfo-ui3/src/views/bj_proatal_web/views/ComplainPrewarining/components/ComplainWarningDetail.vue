<template>
  <el-dialog :visible.sync="visible" :title="title">
    <el-table :data="tableData" v-loading="loading">
     <el-table-column label="序号" prop="historyId" align="center"></el-table-column>
     <el-table-column label="预警时间" prop="warningTime" align="center"></el-table-column>
     <el-table-column label="是否推送成功" align="center">
       <template slot-scope="{row}">
        {{Number(row.successOrNot) ? '是' : '否'}}
       </template>
     </el-table-column>
    </el-table>
    <div class="warning-pagination">
      <el-pagination
            :current-page="page.current"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            @size-change="sizeChange"
            @current-change="pageCurrentChange"
          />
    </div>
  </el-dialog>
</template>

<script>
import {getWarningHistory} from '@/api/complain-warning';

export default {
  name:'ComplainWarningDetail',
  data(){
    return {
      visible: false,
      title:'',
      rulerid: '',
      tableData:[],
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      loading: false
    }
  },
  methods:{
    sizeChange(size) {
      this.page.size = size;
      this.page.current = 1;
      this.getWarningHistory();
    },
    pageCurrentChange(current) {
      this.page.current = current;
      this.getWarningHistory();
    },
    getWarningHistory() {
      const {current,size} = this.page;
      this.loading = true;
      getWarningHistory(this.rulerid,{
        // 分页参数
        pageNum: current,
        pageSize: size,
        firstnum: (current -1) * size
      }).then((res)=>{
        this.tableData = res.rows;
        this.page.total = Number(res.total);
      }).catch(()=>{

      }).finally(()=>{
        this.loading = false;
      })
    },
    handleDetail(row) {
      const {rulerid,businessscenario,complaintwarningtype,indexname} = row;
      this.rulerid = rulerid;
      this.pageCurrentChange(1);
      this.title = `${businessscenario} -> ${complaintwarningtype} -> ${indexname}`;
      this.visible = true;
    }
  },
  watch:{
    visible(val) {
      if(!val) {
        this.tableData = [];
        this.page.current = 1;
      }
    }
  }

}
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__title{
  font-size: 16px;
}
.el-table{
  border: 1px solid #dfe6ec;
  border-bottom: none;
}
.warning-pagination{
  text-align: right;
  margin: 20px 0;
}
</style>
