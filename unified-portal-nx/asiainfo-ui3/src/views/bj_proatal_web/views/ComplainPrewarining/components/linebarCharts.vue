<template>
  <div class="hunsty" style="position: relative">
    <div :id="t" class="hunsty" :style="{ opacity: allData.length ? 1 : 0 }" />
    <div
      :style="{
        position: 'absolute',
        left: 0,
        top: 0,
        width: '100%',
        height: '100%',
        display: allData.length ? 'none' : 'block',
      }"
    >
      <Blank2 />
    </div>
  </div>
</template>

<script>
import tool from '@/views/bj_proatal_web/utils/utils'
export default {
  name: 'GLine<PERSON><PERSON>',
  props: {
    allData: {
      type: Array,
      default: () => {
        return []
      }
    },
    option: {
      type: Object,
      default: () => {
        return {}
      }
    },
    legendData: {
      type: Array,
      default: () => {
        return []
      }
    },
    xData: {
      type: Array,
      default: () => {
        return []
      }
    },
    seriesData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      t: '',
      chart: null,
      valueList: [],
      showChart: true
    }
  },
  watch: {
    allData(v, oldv) {
      this.initChart()
    },
    xData(v, oldv) {},
    legendData(v, oldv) {},
    seriesData(v, oldv) {}
  },
  created() {
    this.t = new Date().getTime() + (Math.random() * 100).toFixed(0)
  },
  mounted() {
    var _this = this
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', function() {
      _this.chart.resize()
    })
  },
  methods: {
    initChart() {
      this.chart = null
      const dom = document.getElementById(this.t)
      this.chart = this.$echarts.init(dom)
      this.renderChart()
      const _self = this
      this.chart.on('click', (params) => {
        _self.allData.forEach((j) => {
          if (params.name == j.appartName) {
            _self.$emit('barclick', {
              appartId: j.appartId,
              appartName: j.appartName
            })
          }
        })
      })
    },
    renderChart() {
      const _self = this
      const option = {
        grid: {
          top: '20%',
          left: '10%',
          right: '10%',
          bottom: '15%',
          containLabel: false
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255,255,255,1)',
          textStyle: {
            color: '#262626',
            align: 'left'
          },
          confine: true,
          extraCssText: 'box-shadow:0px 2px 8px 0px rgba(102, 61, 0, 0.16)',
          formatter: function(datas) {
            if (datas && datas.length) {
              datas.forEach((i) => {
                i.value = tool.dealFloatNum(i.value)
              })
            }

            const html =
              "<span style='color:#777;line-height:30px;'>" +
              datas[0].name +
              '</span><br>' +
              datas[0]?.marker +
              "<span style='color:#222;padding:0 8px 0 8px;'>" +
              datas[0].seriesName +
              "</span><span style='font-weight:bold;line-height:30px;'>" +
              datas[0]?.value +
              (datas[0].seriesName.indexOf('满意度') != -1
                ? ''
                : String(datas[0].seriesName).indexOf('时长') != -1
                  ? ' 分钟'
                  : datas[0].seriesName === '万投比'
                    ? ' ‱'
                    : ' %') +
              '</span><br>' +
              (datas[1]?.marker || '') +
              "<span style='color:#222;padding:0 8px 0 8px;'>" +
              datas[1]?.seriesName +
              "</span><span style='font-weight:bold;line-height:30px;'>" +
              (datas[1]?.value || '') +
              '%</span><br>' +
              (datas[2]?.marker || '') +
              "<span style='color:#222;padding:0 8px 0 8px;'>" +
              datas[2]?.seriesName +
              "</span><span style='font-weight:bold;line-height:30px;'>" +
              (datas[2]?.value || '') +
              '%</span><br>'

            return html
          }
        },
        legend: {
          data: this.legendData,
          top: '2%',
          textStyle: {
            color: '#747474'
          }
        },
        xAxis: {
          type: 'category',
          // data: ['2022-02', '2022-03'],
          data: this.xData,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#393939'
            }
          }
        },
        yAxis: [
          {
            // max: 100,
            type: 'value',
            name: this.legendData[0],
            nameTextStyle: {
              color: '#393939',
              padding: [0, 0, 10, -40]
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#eeeeee'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#393939'
              }
            }
          },
          {
            // max: 100,
            type: 'value',
            name: '',
            nameTextStyle: {
              color: '#393939',
              padding: [0, 0, 10, 35] // 四个数字分别为上右下左与原位置距离
            },
            position: 'right',
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#393939'
              },
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: this.legendData[0],
            type: 'bar',
            barWidth: 15,
            itemStyle: {
              normal: {
                color: '#0682FF',
                borderRadius: [1, 1, 0, 0]
                // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //   {
                //     offset: 1,
                //     color: '#0084FF',
                //     opacity: 0.85
                //   },
                //   {
                //     offset: 0,
                //     color: '#3BAFFF',
                //     opacity: 0.79
                //   }
                // ])
                // barBorderRadius: 11,
              }
            },
            data: this.seriesData[0]
          },
          {
            name: this.legendData[1],
            type: 'line',
            yAxisIndex: 1,
            showAllSymbol: true, // 小圆点
            symbol: 'circle',
            symbolSize: 1,
            // smooth: true, // 平滑曲线
            itemStyle: {
              color: '#7097F6',
              borderWidth: '2',
              borderColor: '#7097F6'
            },
            lineStyle: {
              color: '#7097F6' // 线颜色
            },
            data: this.seriesData[1]
          },

          {
            name: this.legendData[2],
            type: 'line',
            yAxisIndex: 1,
            showAllSymbol: true, // 小圆点
            symbol: 'circle',
            symbolSize: 1,
            // smooth: true, // 平滑曲线
            itemStyle: {
              color: '#fdcb6c',
              borderWidth: '2',
              borderColor: '#fdcb6c'
            },
            lineStyle: {
              color: '#FF9900' // 线颜色
            },
            data: this.seriesData[2]
          }
        ]
      }
      this.chart.setOption(option)

      if (this.allData.length == 0 || !this.allData) {
        this.chart.showLoading({
          text: '暂无数据',
          color: 'rgba(255, 255, 255, 0)',
          fontSize: 20,
          textColor: '#8a8e91',
          maskColor: 'rgba(255, 255, 255, 1)'
        })
      } else {
        this.chart.hideLoading()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.hunsty {
  height: 100%;
  width: 100%;
}
</style>
