<template>
<div class="complain-warning">
  <!-- 预警列表 -->
  <complain-warning-list v-if="type == 'list'"  @switch="switchView"/>
  <!-- 新增列表 -->
  <complain-warning-step v-if="type == 'add'"
                         :ruler-id="rulerId"
                         @switch="switchView" />
</div>
</template>


<script>
import ComplainWarningList from './ComplainWarningList.vue';
import ComplainWarningStep from './ComplainWarningStep.vue';

export default {
  name:'ComplainWarning',
  components:{
    ComplainWarningList,
    ComplainWarningStep
  },
  data() {
     return {
      type: 'list', // list add
      rulerId:'',
     }
  },
   methods:{
     switchView(type,row) {
      this.type = type;
      if(row && row.rulerid) {
         this.rulerId = row.rulerid;
      }else{
         this.rulerId = null;
      }
     }
   }
}
</script>

<style lang="scss" scoped>
.complain-warning{
  background-color: rgba(242, 242, 242, 1);
  padding: 30px 40px 40px 40px;
  min-height: 70vh;
}
</style>
