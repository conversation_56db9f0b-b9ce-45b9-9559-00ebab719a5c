<template>
  <div class="overallComplaints">
    <overAllFirst />
    <overAllSecond/>
    <overAllThird/>
    <overAllFour/>
  </div>
</template>
<script>
import overAllFirst from "../components/overAllFirst";
import overAllSecond from "../components/overAllSecond";
import overAllThird from "../components/overAllThird";
import overAllFour from "../components/overAllFour";
export default {
  name: "overallComplaints",
  components: {
    overAllFirst,overAllSecond,overAllThird,overAllFour
  },
  data() {
    return {};
  }
};
</script>
<style lang="scss" scoped>
.overallComplaints {
position: relative;
  width: 100%;
  height: 100%;
}
</style>>
