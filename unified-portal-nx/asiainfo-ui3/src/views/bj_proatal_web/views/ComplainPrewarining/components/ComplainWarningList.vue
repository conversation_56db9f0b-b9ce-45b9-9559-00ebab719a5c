<template>
  <div class="complain-warning-box">
    <el-form ref="form" :model="form" class="warning-form" label-width="120px" size="small">
      <el-row>
        <el-col :span="6">
          <el-form-item label="业务场景" prop="businessScenario">
            <el-select v-model="form.businessScenario" clearable @change="getWarningType">
              <el-option
                v-for="{businesscenariotypeid,businesscenariotypename} in scene"
                :key="businesscenariotypeid"
                :label="businesscenariotypename"
                :value="businesscenariotypename"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="投诉预警类型" prop="complaintWarningType">
            <el-select v-model="form.complaintWarningType" clearable>
              <el-option
                v-for="{complaintwarntypeid,complaintwarntypename} in warningType"
                :key="complaintwarntypeid"
                :label="complaintwarntypename"
                :value="complaintwarntypename"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="当前处理工作组" prop="processingWorkGroup">
            <el-select v-model="form.processingWorkGroup" clearable>
              <el-option
                v-for="{processingworkgroup} in workerGroup"
                :key="processingworkgroup"
                :label="processingworkgroup"
                :value="processingworkgroup"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="是否启用" prop="enableOrNot">
            <el-select v-model="form.enableOrNot" clearable>
              <el-option v-for="(text,key) in enableList" :key="key" :value="key" :label="text" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="归属地市" prop="localCity">
            <el-cascader
              v-model="form.localCity"
              :placeholder="'请选择地市'"
              :options="area"
              :props="props"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="预警级别类型" prop="warningLevel">
            <el-select v-model="form.warningLevel" clearable>
              <el-option
                v-for="{complanitwarningleveltype} in warningLevel"
                :key="complanitwarningleveltype"
                :label="complanitwarningleveltype"
                :value="complanitwarningleveltype"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="提醒号码" prop="remindPhone">
            <el-input v-model="form.remindPhone" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="" label-width="55px">
            <el-button type="primary" @click="pageCurrentChange(1)">查询</el-button>
            <el-button type="default" @click="clearParams">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="btn-box">
      <el-button type="default" size="small" @click="handleDelete">删除</el-button>
      <el-button type="default" size="small" @click="handleEnableWarning(0)">禁用</el-button>
      <el-button type="primary" size="small" @click="handleEnableWarning(1)">启用</el-button>
      <!-- <el-button type="primary" size="small" @click="handleDetail">查看预警</el-button> -->
      <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
      <el-button type="primary" size="small" @click="$emit('switch','add')">新增</el-button>
    </div>
    <el-table v-loading="loading" :data="tableData" @selection-change="handleSelection">
      <el-table-column type="selection" align="center" fixed />·
      <el-table-column label="是否启用" prop="_enableText" align="center" fixed />
      <el-table-column label="指标名称" prop="indexname" align="center" min-width="150" show-overflow-tooltip fixed />
      <el-table-column label="业务场景" prop="businessscenario" min-width="120" show-overflow-tooltip align="center" />
      <el-table-column label="投诉预警类型" prop="complaintwarningtype" min-width="140" align="center" />
      <el-table-column label="阀值基准值" min-width="120" prop="referencevalue" align="center" />
      <el-table-column label="当前处理工作组" min-width="120" prop="processingworkgroup" align="center" />
      <el-table-column label="统计周期" prop="statisticalcycle" align="center" />
      <el-table-column label="归属地市" prop="cityname" min-width="120" align="center" />
      <el-table-column label="预警级别" prop="warninglevel" align="center" min-width="150" />
      <el-table-column label="提醒号码" prop="receivephone" min-width="120" align="center" show-overflow-tooltip />
      <el-table-column label="短信内容" prop="message" min-width="200" show-overflow-tooltip />
      <!-- <el-table-column label="短信内容" min-width="200" show-overflow-tooltip>
        <template slot-scope="scope">
           <el-tooltip class="item" effect="dark" :content="`${scope.row['message']}`" placement="top">
              <div>{{scope.row['message']}}</div>
          </el-tooltip>
        </template>
      </el-table-column> -->
      <el-table-column label="创建时间" prop="createtime" min-width="180" align="center" />
      <el-table-column label="创建人" prop="createby" align="center" width="100" />
    </el-table>
    <div class="warning-pagination">
      <el-pagination
        :current-page="page.current"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        @size-change="sizeChange"
        @current-change="pageCurrentChange"
      />
    </div>

    <complain-warning-detail ref="detail" />
  </div>
</template>

<script>
import ajaxRequest, { delWarningRule, enableWarning } from '@/api/complain-warning'
import ComplainWarningDetail from './ComplainWarningDetail.vue'

export default {
  name: 'ComplainWarningList',
  components: {
    ComplainWarningDetail
  },
  data() {
    return {
      scene: [], // 业务场景
      warningType: [],
      workerGroup: [],
      warningLevel: [],
      area: [],
      props: {
        label: 'name',
        value: 'id',
        checkStrictly: true
      },
      enableList: {
        0: '否',
        1: '是'
      },
      form: {
        businessScenario: '',
        complaintWarningType: '',
        processingWorkGroup: '',
        enableOrNot: '',
        localCity: [],
        warningLevel: '',
        remindPhone: ''
      },
      page: {
        current: 1,
        size: 10,
        total: 0
      },
      loading: false,
      tableData: [],
      selection: []
    }
  },
  created() {
    this.getSelections()
    this.getWarningList()
  },
  methods: {
    clearParams() {
      this.$refs.form.resetFields()
      this.warningType = []
      this.getWarningList()
    },
    getSelections() {
      // 查询地址
      ajaxRequest('6MujCDCs').then((res) => {
        const { data: [data] } = res
        this.area = data
      })
      // 业务场景查询
      ajaxRequest('2KqbjOSV').then((res) => {
        const { data: { data }} = res
        this.scene = data
      })
      // 工作组
      ajaxRequest('Stl5Ji8p').then((res) => {
        const { data: { data }} = res

        this.workerGroup = data.filter(i => {
          return i.processingworkgroup != ''
        })
      })
      // 级别类型
      ajaxRequest('hvGwrnsj', { type: 'WJB' }).then((res) => {
        const { data: { data }} = res
        this.warningLevel = data
      })
    },
    // 投诉预警类型
    getWarningType(val) {
      this.form.complaintWarningType = ''
      this.warningType = []
      if (!val) return
      ajaxRequest('o5sMnH9p', {
        businesScenarioTypeName: val
      }).then((res) => {
        const { data: { data }} = res
        this.warningType = data
      })
    },
    getWarningList() {
      const { current, size } = this.page
      const { localCity, ...params } = this.form
      const [cityId] = [].concat(localCity).reverse()

      this.loading = true
      ajaxRequest('p4XG8Weg', {
        ...params,
        localCity: cityId,
        // 分页参数
        current,
        size,
        firstnum: (current - 1) * size
      }).then((res) => {
        const { data: { data, total }} = res
        this.tableData = data.map((item) => {
          if (item.message) {

            //   warningLevel: '预警级别',
            //   indexName: '预警规则名称',
            //   referenceValue: '阈值',
            //   resultValue: '实际值',
            //   calibers: '引用类型'
            //   receiveTime: '受理日期',
            //  item.message = item.message.replace(new RegExp('#warningLevel#', 'g'), '预警级别')
            //  item.message =item.message.replace(new RegExp('#indexName#', 'g'), '预警规则名称')
            //  item.message =item.message.replace(new RegExp('#referenceValue#', 'g'), '阈值')
            //  item.message =item.message.replace(new RegExp('#resultValue#', 'g'), '实际值')
            //  item.message =item.message.replace(new RegExp('#calibers#', 'g'), '引用类型')
            //  item.message =item.message.replace(new RegExp('#receiveTime#', 'g'), '受理日期')

          }
          item._enableText = this.enableList[item.enableornot]
          return item
        })
        this.page.total = Number(total)
      }).finally(() => {
        this.loading = false
      })
    },
    sizeChange(size) {
      this.page.size = size
      this.page.current = 1
      this.getWarningList()
    },
    pageCurrentChange(current) {
      this.page.current = current
      this.getWarningList()
    },
    handleSelection(selection) {
      this.selection = selection
    },
    // 编辑
    handleEdit() {
      if (!this.selection.length || this.selection.length > 1) return this.$message.error('请勾选一条规则')
      const [row] = this.selection
      this.$emit('switch', 'add', row)
    },
    // 预警详情
    handleDetail() {
      if (!this.selection.length || this.selection.length > 1) return this.$message.error('请勾选一条规则')
      const [row] = this.selection
      this.$refs.detail.handleDetail(row)
    },
    // 删除规则
    handleDelete() {
      if (!this.selection.length) return this.$message.error('请勾选一条指标')
      const ids = this.selection.map(({ rulerid }) => {
        return rulerid
      })
      this.$confirm('此操作将删除所选的规则, 是否继续?', '提示', {
        type: 'warning'
      }).then(() => {
        delWarningRule(ids.join(',')).then(() => {
          this.pageCurrentChange(1)
          this.$message.success('删除规则成功!')
        }).catch((e) => {
          this.$message.error('删除规则失败!')
        })
      }).catch(() => {

      })
    },
    // 启用，禁用规则
    handleEnableWarning(type) {
      if (!this.selection.length) return this.$message.error('请勾选一条指标')
      const list = this.selection.map(({ rulerid }) => {
        return { rulerId: rulerid, enableOrNot: type }
      })
      this.$confirm('此操作将' + (type ? '启用' : '禁用') + '所选的规则, 是否继续?', '提示', {
        type: 'warning'
      }).then(() => {
        enableWarning({ list }).then(() => {
          this.getWarningList() // 当前页刷新
          this.$message.success(type ? '启用规则成功!' : '禁用规则成功!')
        }).catch((e) => {
          this.$message.error(e || (type ? '启用规则失败!' : '禁用规则失败!'))
        })
      }).catch(() => {

      })
    }
  }
}
</script>

<style lang="scss" scoped>
.complain-warning-box {
  padding: 30px 10px;
  background: #fff;
}
.warning-form{
  padding-right: 30px;

  .el-cascader{
    width: 100%;
    /deep/.el-icon-arrow-down:before {
      content: "\E6E1";
    }
    /deep/.el-icon-arrow-down{
      transform: rotate(180deg);
    }
    /deep/.is-reverse.el-icon-arrow-down{
      transform: rotate(0deg);
    }
  }
  .el-select{
    width: 100%;
  }
}
.btn-box{
  text-align: right;
  margin: 20px 0;
}
.el-table{
  border: 1px solid #dfe6ec;
  border-bottom: none;
}
.warning-pagination{
  text-align: right;
  margin: 20px 0;
}
</style>
