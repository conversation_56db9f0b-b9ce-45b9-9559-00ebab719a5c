<template>
  <div class="time-select">
    <el-select
      v-model="activeName"
      style="margin-right: 10px; width: 60px"
      placeholder="请选择"
      :clearable="false"
      @change="changeTime"
    >
      <el-option
        v-for="item in monthOptions"
        :key="item.name"
        :label="item.label"
        :value="item.name"
      />
    </el-select>
    <el-date-picker
      v-if="activeName == 1"
      v-model="selectedDate"
      type="date"
      :clearable="false"
      :picker-options="pickerOptionsDate"
      value-format="yyyy-MM-dd"
      @change="handleClick"
    />
    <el-date-picker
      v-if="activeName == 2"
      v-model="selectedWeek"
      style="width: 300px"
      type="week"
      :clearable="false"
      :picker-options="pickerOptionsDate1"
      :format="startTimeStamp + ' ⾄ ' + endTimeStamp"
      value-format="yyyy-MM-dd"
      @change="handleClick"
    />
    <el-date-picker
      v-if="activeName == 3"
      v-model="selectedMonth"
      type="month"
      :clearable="false"
      :picker-options="pickerOptionsDate"
      value-format="yyyy-MM"
      @change="handleClick"
    />
    <el-select
      v-if="activeName == 4"
      v-model="selectedSeason"
      placeholder="请选择"
      :clearable="false"
      @change="handleClick"
    >
      <el-option
        v-for="item in seasonList"
        :key="item.name"
        :label="item.label"
        :value="item.name"
      />
    </el-select>
    <el-button
      style="margin-left: 10px"
      type="primary"
      size="small"
      @click="checkDate"
    >查询</el-button>
  </div>
</template>
<script>
export default {
  name: 'TimeSelect',
  props: {
    serviceMode: {
      type: Boolean,
      default: false
    },
    compainMode: {
      type: Boolean,
      default: false
    },
    totalMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const _this = this
    return {
      week: '',
      selectedDate: '',
      selectedWeek: '',
      selectedMonth: '',
      selectedSeason: 'Q1',
      activeName: '3',
      pickerOptionsDate1: {
        firstDayOfWeek: 1,
        disabledDate(time) {
          const bb = new Date()
          bb.setFullYear(bb.getFullYear() - 1)
          return time.getTime() > Date.now()
        }
      },
      pickerOptionsDate: {
        disabledDate(time) {
          const bb = new Date()
          bb.setFullYear(bb.getFullYear() - 1)
          return time.getTime() > Date.now()
        }
      },
      seasonList: [
        { name: 'Q1', label: '第一季度' },
        { name: 'Q2', label: '第二季度' },
        { name: 'Q3', label: '第三季度' },
        { name: 'Q4', label: '第四季度' }
      ],
      startTimeStamp: '',
      endTimeStamp: ''
    }
  },
  computed: {
    monthOptions() {
      let array = []
      if (this.totalMode) {
        array = [
          { name: '3', label: '月' },
          { name: '4', label: '季' }
        ]
      } else if (this.serviceMode) {
        array = [
          { name: '1', label: '日' },

          { name: '3', label: '月' },
          { name: '4', label: '季' }
        ]
      } else if (this.compainMode) {
        array = [
          { name: '1', label: '日' },
          { name: '3', label: '月' }
        ]
      } else {
        array = [
          { name: '1', label: '日' },
          { name: '2', label: '周' },
          { name: '3', label: '月' },
          { name: '4', label: '季' }
        ]
      }
      return array
    }
  },
  created() {
    this.selectedDate = this.parseTime(
      new Date(new Date().getTime() - 1000 * 3600 * 24),
      '{y}-{m}-{d}'
    )
    this.selectedWeek = this.parseTime(
      new Date(new Date().getTime() - 1000 * 3600 * 24 * 7),
      '{y}-{m}-{d}'
    )
    this.changeWeekDate(this.selectedWeek)
    this.selectedMonth = this.parseTime(
      new Date(new Date().getTime() - 1000 * 3600 * 24 * 30),
      '{y}-{m}'
    )
    const year = new Date().getFullYear()
    const month = new Date().getMonth()
    const otherSeason = [
      { name: year + '-Q1', label: year + '第一季度' },
      { name: year + '-Q2', label: year + '第二季度' },
      { name: year + '-Q3', label: year + '第三季度' },
      { name: year + '-Q4', label: year + '第四季度' }
    ]
    if (month >= 0 && month <= 2) {
      otherSeason.length = 1
    }
    if (month >= 3 && month <= 5) {
      otherSeason.length = 2
    }
    if (month >= 6 && month <= 8) {
      otherSeason.length = 3
    }
    this.seasonList = [
      { name: year - 1 + '-Q1', label: year - 1 + '第一季度' },
      { name: year - 1 + '-Q2', label: year - 1 + '第二季度' },
      { name: year - 1 + '-Q3', label: year - 1 + '第三季度' },
      { name: year - 1 + '-Q4', label: year - 1 + '第四季度' }
    ]
    this.seasonList = this.seasonList.concat(otherSeason)
    this.selectedSeason = this.seasonList[this.seasonList.length - 2].name
    this.checkDate()
  },
  methods: {
    resetTime(mode, time) {
      this.activeName = mode
      if (this.activeName == 3) {
        this.selectedMonth = time
      }
      if (this.activeName == 4) {
        this.selectedSeason = time
      }
    },
    handleClick() {
      if (this.activeName == 2) {
        this.changeWeekDate(this.selectedWeek)
      }
    },
    addDate(date, n) {
      date.setDate(date.getDate() + n)
      return date
    },
    setDate(date) {
      var week = date.getDay() - 1
      date = addDate(date, week * -1)
      const currentFirstDate = new Date(date)
      return currentFirstDate
    },
    changeWeekDate(str) {
      const now = new Date(str)
      const nowTime = now.getTime()
      const day = now.getDay()
      const oneDayTime = 24 * 60 * 60 * 1000
      // 显示周一
      const MondayTime = nowTime - (day - 1) * oneDayTime
      // 显示周日
      const SundayTime = nowTime + (7 - day) * oneDayTime
      // 初始化日期时间
      const monday = new Date(MondayTime)
      const sunday = new Date(SundayTime)
      this.weeks = ''
      this.startTimeStamp = this.parseTime(monday, '{y}-{m}-{d}')
      this.endTimeStamp = this.parseTime(sunday, '{y}-{m}-{d}')
      return this.startTimeStamp + '|' + this.endTimeStamp
    },
    changeTime() {},
    checkDate() {
      const resWeek = this.changeWeekDate(this.selectedWeek)
      this.$emit(
        'checkDate',
        this.activeName,
        this.selectedDate,
        resWeek,
        this.selectedMonth,
        this.selectedSeason
      )
    }
  }
}
</script>

<style lang="less" scoped>
.time-select {
  width: 100%;
  // margin-top: 10px;
  // margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
