<template>
  <div class="complainTitle">
    <div class="service-header">
      <NumberTitle num="01" text="投诉效能指标" />
      <div class="header-search">
        <div class="block">
          <span class="demonstration">选择时间：</span>
          <el-select
            v-model="valueType"
            style="width: 65px"
            placeholder="选择时间"
          >
            <el-option label="月" value="3" />
          </el-select>

          <el-date-picker
            v-model="dateValue"
            style="margin-left: 0; width: 220px;"
            value-format="yyyy-MM"
            type="monthrange"
            range-separator="-"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
          />
        </div>
      </div>

      <div class="complainContent">
        <el-tabs
          v-model="activeName"
          @tab-click="handleClick"
        >
          <el-tab-pane label="投诉处理满意度" name="7001"> 投诉处理满意度</el-tab-pane>
          <el-tab-pane label="投诉处理满意度" name="7002"> 投诉处理满意度</el-tab-pane>
          <el-tab-pane label="投诉处理满意度" name="7003"> 投诉处理满意度</el-tab-pane>
          <el-tab-pane label="投诉处理满意度" name="7004"> 投诉处理满意度</el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div class="picture">
      <div v-if="activeName==='7001'" class="first">
        <div class="echar-box" style="width: 90%; height: 500px;" />
      </div>

    </div>

  </div>

</template>

<script>
import NumberTitle from '../../../components/common/numberTitle'
import echarts from 'echarts'

export default {
  name: 'Indicators',

  components: {
    NumberTitle,
    echarts
  },

  data() {
    return {
      activeName: 7001
    }
  },

  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    }
  }
}

</script>

<style lang="less">
.complainTitle {
  padding: 30px 50px 40px 50px;
  box-sizing: border-box;
  width: 100%;
  background: #f0f0f0;
  .header-search {
    margin-bottom: 20px;
  }
}

.complainContent {
  padding: 20px 40px 40px 40px;
  background-color: rgb(255, 255, 255);
}
</style>
