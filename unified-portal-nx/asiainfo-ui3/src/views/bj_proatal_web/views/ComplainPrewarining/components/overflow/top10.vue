<template>
  <div ref="rightChart" class="progress-right">

    <div style="width: 100%; min-height: 200px; position: relative">
      <div class="header">
        <div class="body-name" style="font-weight:bolder;font-size:14px;color:#262626;width:150px">{{ keymap.yName }}</div>
        <div class="hader-progress" />
        <div class="hader-cell">数量</div>
        <div class="hader-cell">占比</div>
      </div>
      <div v-for="(elment, index) in channelData" :key="index" class="body">
        <!-- <el-tooltip :content="elment.filed" placement="top"> -->
        <div :class="{'body-name':true}" style="line-height: 30px; line-height: 30px; text-align:right;color:#595959;  font-size:12px;">
          {{ elment.filed }}
        </div>
        <!-- </el-tooltip> -->
        <div class="body-progress">
          <el-progress
            :percentage="elment.pir"
            :show-text="false"
            :stroke-width="12"
            stroke-linecap="square"
            :color="customColor2"
          />
        </div>
        <div class="body-cell" style="line-height:32px">
          {{ elment.score }}
          <!-- {{
            (elment.score===0|| elment.score==='0')? '0 %':elment.score?`${Number(elment.score*100).toFixed(2)} %`:'-'
          }} -->
        </div>
        <div class="body-cell " style="line-height:32px">
          {{ elment.sampleNum }}
        </div>
        <!-- <div class="body-cell" style="line-height:30px">

          {{
            (elment.repeat_complaint_rate===0|| elment.repeat_complaint_rate==='0')? '0 %':elment.repeat_complaint_rate?`${elment.repeat_complaint_rate} %`:'-'
          }}
        </div> -->
      </div>
      <div
        :style="{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '100%',
          height: '100%',
          display: channelData.length ? 'none' : 'none',
        }"
      >
        <Blank2 />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    keymap: {
      type: Object,
      default: () => {
        return {
          yName: 'Top', // y轴的名称
          yData: 'filed', // y轴取的字段
          seriesData: ['score'] // 对应值取的字段
        }
      }
    },
    allData: {
      type: Array,
      default: () => {
        return []
      }
    },
    option: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isShowTip: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },

  data() {
    return {
      channelData: [],
      customColor: '#409EFF',
      customColor2: '#E6A23C',
      allCount: 0
    }
  },

  watch: {
    allData(v, oldv) {
      let allCount = 0
      v.forEach(x => {
        const n = Number(x.score)
        allCount += n
      })
      console.log('allCount:', allCount)
      this.allCount = allCount
      v.forEach(x => {
        x.pir = x.score / allCount * 100
      })

      this.channelData = v || []
    }
  },
  mounted() {
    let allCount = 0
    this.allData.forEach(x => {
      const n = Number(x.score)
      allCount += n
    })
    this.allCount = allCount

    this.allData.forEach(x => {
      x.pir = x.score / allCount * 100
    })
    this.channelData = this.allData || []
  }
}
</script>
<style lang="scss" scoped>
.progress-right {
    width: 100%;
    .header {
      color: rgb(140, 140, 140);
      font-size: 12px;
      padding: 10px 0 0 10px;
      line-height: 40px;
    }
    .header,
    .body {
      width: 100%;
      display: flex;
      .hader-cell,
      .body-cell {
        width: 15%;
        text-align: center;
        font-size:12px;
      }
      .body-name {
        width: 150px;

        padding-right:20px;
        box-sizing: border-box;
        height: 40px;
        text-align: center;
        overflow:hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        &.first{
          text-align: right;
        }
      }
    }
    .hader-progress,
    .body-progress {
      width: 40%;
      margin-top: 10px;
    }
    /deep/ .el-progress-bar .el-progress-bar_inner {
      border-radius: inherit;
    }
    /deep/.el-progress-bar .el-progress-bar__outer {
      height: 13px !important;
      border-radius: inherit;
    }
    .body {
      font-size: 16px;
      .body-cell {
        font-size:12px;
      }
      .body-name .body-progress {
        padding: 5px 0;
      }
    }
  }
/deep/.el-progress-bar__inner{
position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    background-color: #FF9900;
    text-align: right;
    border-radius: 0;
    line-height: 1;
    white-space: nowrap;
    -webkit-transition: width 0.6s ease;
    transition: width 0.6s ease;
}
</style>
