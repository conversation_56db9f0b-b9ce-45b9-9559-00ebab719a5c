<template>
  <div class="overAllSecond">
    <NumberTitle num="04" text="投诉指标" />
    <div class="con">
      <div class="timecon flexr">
        <div>
        <NewDatePicker :dateTypes="['date','month','daycumulative']" :tableName="'portal_indicator_appeal_data_info'" @change="dateChange"></NewDatePicker>
        </div>
        <!-- 只有家宽报障 灭灯行动治理情况 才有地市部门 后面三个只有部门没有地市-->
        <div>
          <AreaPicker
            size="small"
            :ext-options="mapPermission.mapLevel == 1 ? [
              {name:'全省按部门',id:'640000x',data:{ name:'全省按部门',id: '640000x',level:1}}
            ] : []"
            @change="areaChange"
          />
          <!-- <AreaSelect @sendCityId="areaChange" /> -->
        </div>
      </div>
      <div class="box">
        <div class="echartscon">
          <div class="item">
            <div class="t flex-item">
              <el-tabs
                v-model="activeTargetId"
                style="width: 550px"
                @tab-click="handleTabClick"
              >
                <el-tab-pane
                  v-for="item in labelArr"
                  :key="item.targetId"
                  :label="item.name"
                  :name="item.targetId"
                />
              </el-tabs>
              <div class="two">
                <el-button
                  v-show="showExportBtn"

                  type="primary"
                  size="small"
                  @click="exportOne"
                >导出</el-button>
              </div>
            </div>
            <div class="chartbox">
              <LineBarChart
                :all-data="second"
                :legend-data="['投诉量', '环比']"
                :keymap="{
                  xData: 'statdate',
                  seriesData: ['score', 'momrate'],
                }"
                :is-double-line="false"
              />
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>
<script>
import NumberTitle from '../../../components/common/numberTitle'
import AreaSelect from './areaSelect'
import Hot5Table from './hot5Table'
import LineBarChart from './linebarChartsT'
// import { NaDAbYpd } from '@/api/complainPrewarining/index.js'
import { esportComplaint } from '@/api/complaint-analysis/api'
import PieChart from './pieChart.vue'
import { getCHBNdata } from '@/api/complaint-topic/index.js'
import horizontalBar from './horizontalBars.vue'
import AreaPicker from 'bj_src/nx-components/area-picker/AreaPicker.vue'
import NewDatePicker from '@/views/bj_proatal_web/components/date-picker/newDatePicker'

export default {
  name: 'OverAllFour',
  components: {
    NumberTitle,
    AreaSelect,
    LineBarChart,
    PieChart,
    Hot5Table,
    horizontalBar,
    AreaPicker,
    NewDatePicker
  },
  filters: {
    // 判断超出5个字显示省略号
    ellipsis(value) {
      if (!value) return ''
      if (value.length > 8) {
        return value.slice(0, 7) + '...'
      }
      return value
    }
  },
  inject: ['showExportBtn', 'mapPermission'],

  data() {
    return {
      loading: false,
      defaultDate: '202206',
      activeTargetId: '402020202',
      dateType: '',
      isShowTip: true,
      first: [],
      second: [],
      third: [],
      activeData1: [],
      userCounty: {},
      cityId: this.mapPermission.cityId,
      type: '月',
      date: '',
      lists: ['日', '月', '日累计'],
      labelArr: [
        {
          targetId: '402020202',
          name: '预警投诉指标'
        },
        {
          targetId: '402020203',
          name: '语音预警投诉指标'
        },
        {
          targetId: '402020204',
          name: '手机业务投诉指标'
        }
      ]
    }
  },
  created() {
    this.defaultDate = this.parseTime(
      new Date(new Date().getTime() - 1000 * 3600 * 24 * 30),
      '{y}-{m}'
    ).replace('-', '')
    this.date = this.parseTime(
      new Date(new Date().getTime() - 1000 * 3600 * 24 * 30),
      '{y}-{m}'
    )
    console.log('this.defaultDate=>', this.defaultDate)
    console.log('this.date=>', this.date)
  },
  mounted() {
    this.queryData()
  },
  methods: {
    changeLoading(flag) {
      this.loading = flag
    },
    exportOne() {
      const statType = this.type == '日' ? '1' : this.type == '月' ? '3' : '5'
      // 左侧导出
      if (statType == '3' && this.defaultDate.indexOf('-', 0) == '-1') {
        this.defaultDate =
          this.defaultDate.slice(0, 4) + '-' + this.defaultDate.slice(4)
      }
      const monthNum = parseFloat(this.defaultDate.slice(6, 7))
      const params = `?cityId=${this.cityId || '640000'}&opTime=${
        this.defaultDate
      }&startTime=2022-06-05&endTime=2022-06-08&statType=${statType}&targetId=${
        this.activeTargetId
      }&chartNum=4&monthNum=${monthNum}&businessLevel=""&appartLevel=""&businessName=""&selectType=""&appartRoute=""`
      esportComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(response, 'CHBN投诉情况' + '.xls')
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', 'CHBN投诉情况' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    areaChange(value) {
      const val = Array.isArray(value) ? value[value.length - 1] : value
      this.cityId = val
      //  this.date = value;
      this.queryData()
    },
    dateChange(params) {
      const [ type, value ] = params
      this.type = type
      this.date = value
      this.queryData()
    },
    handleTabClick() {
      this.queryData()
    },
    // 获取数据
    queryData() {
      console.log('this.date=>', this.date)
      const p = {
        statType: this.type == '日' ? '1' : this.type == '月' ? '3' : '5',
        opTime:
          this.date ||
          this.parseTime(
            new Date(new Date().getTime() - 1000 * 3600 * 24 * 30),
            '{y}-{m}'
          ),
        cityId: this.cityId || '640000',
        targetId: this.activeTargetId,
        monthNum: new Date().getMonth() + 1
      }
      console.log('p=>', p)
      getCHBNdata(p).then((res) => {
        if (res.code == 200 && res.data) {
          const { data } = res.data
          const { first = [], second = [], third = [] } = data
          this.activeData1 = first
          second.forEach((el) => {
            if (el.momrateType == 1 || el.momratetype == 1) {
              if (el.momrate && el.momrate != '-') {
                el.momrate = '-' + el.momrate
              }
            }
          })
          console.log('second:', second)
          this.second = second
          third.forEach((el) => {
            el.opTime = this.date
            el.cityId = this.cityId || '640000'
            el.targetId = el.targetid
            el.statType =
              this.type == '日' ? '1' : this.type == '月' ? '3' : '5'
          })
          this.third = third
          console.log('data=>', data)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.lengendbox {
  position: absolute;
  width: 40%;
  height: 100%;
  right: 0;
  top: 0;

  .itembox {
    display: flex;
    text-align: left;
    > div {
      width: 33.3%;
    }
    &.cll {
      height: 40px;
      line-height: 42px;
      font-size: 14px;
      color: #606266;
    }
    &.t {
      color: #909399;
      font-size: 12px;
      padding-top: 30px;
      padding-bottom: 16px;
    }
  }
}
.overAllSecond {
  position: relative;
  background-color: rgba(242, 242, 242, 1);
  padding: 0px 40px 0px 40px;
  // min-height: 100vh;
  /deep/.el-tabs__nav {
    position: relative;
    left: 20px;
  }
}
.echartscon {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  > div {
    width: 50%;
  }
  .item {
    position: relative;
    margin-bottom: 20px;
    .chartbox {
      height: 300px;
    }
    .tl {
      display: flex;
      height: 50px;
      padding: 0 20px;
      align-items: center;
      > div {
        width: 50%;
        &.one {
          font-weight: bold;
          font-size: 16px;
          line-height: 32px;
        }
        &.two {
          text-align: right;
        }
      }
    }
  }
}
.flex-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}
.con {
  position: relative;
}
.timecon {
  position: absolute;
  right: 0;
  top: -55px;
}
.flex {
  display: flex;
  &.tbox {
    padding: 0px 30px 0px 20px;
  }
  > div {
    width: 50%;
  }
}
.flexr {
  display: flex;
  flex-direction: row-reverse;
}
.box {
  background: #fff;
  margin-top: 20px;
  padding-top: 10px;
}
/deep/.el-tabs__nav-wrap::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: #dfe4ed;
  z-index: 1;
}
</style>
>
