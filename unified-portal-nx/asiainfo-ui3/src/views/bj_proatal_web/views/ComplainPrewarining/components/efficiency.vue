<template>
  <div class="imp">
    <NumberTitle num="01" text="投诉效能指标" />
    <!-- 只有省级用户有此权限 -->
    <div class="con" v-permission="mapPermission.mapLevel == 1">
      <div class="timecon flexr">
        <DateChoose :default-date-type-list="['日','月','日累计']" :default-date="defaultDate" @dateChange="dateChange" />
        <!-- <TimeSelect /> -->
      </div>
      <!-- echart 图 -->
      <div class="box">
        <el-tabs v-model="activeTargetId" @tab-click="handleTabClick">
          <el-tab-pane v-for="item in labelArr" :key="item.target_id" :label="item.target_name" :name="item.target_id" />
        </el-tabs>
        <div v-loading="loading">
          <div class="chartbox">
            <div v-show="showExportBtn" class="export" style="text-align:right;z-index:1000" @click="downChart1">
              <el-button type="primary" size="small">导出</el-button>
            </div>

            <div style="height:350px;width:100%">
              <LineBarChart
                min-rotate-xaxis="12"
                :all-data="chart1Data"
                :legend-data="chart1DataLengendData"
                :keymap="chart1KeyMap"
                @barclick="chart1BarClick"
              />
            </div>
          </div>
          <el-divider />
          <div v-loading="trendLoading" class="chartbox">
            <div class="flex" style="padding:10px 20px 0 20px;">
              <div class="cardtitle" style="padding-left:50px">趋势变化</div>
              <div v-show="showExportBtn" style="text-align:right;margin-right:10px">
                <el-button type="primary" size="small" @click="downTrendChart">导出</el-button>
              </div>
            </div>
            <div style="height:350px;width:100%">
              <TrendLine
                :all-data="chartTrendData"
                :legend-data="chartTrendDataLengendData"
                :x-data="chartTrendxData"
                :series-data="chartTrendSeriesData"
              />

              <!-- <LineBarChart
                :all-data="chartTrendData"
                :legend-data="chartTrendDataLengendData"
                :keymap="chartTrendKeyMap"
              /> -->

            </div>
          </div>
        </div>

      </div>

    </div>
  </div>
</template>
<script>
import NumberTitle from '@/views/bj_proatal_web/components/common/numberTitle'
import LineBarChart from './linebarChartsT'
import TrendLine from './trendLine'
import { uHHOPFVR, rtgCNTi4, e4ISOzuv, DDrLFvxR, exportComplaintSatisfaction, exportComplaintTrendChange } from '@/api/complainPrewarining/index.js'
import DateChoose from '@/views/bj_proatal_web/components/common/DateChooseT.vue'
import timetool from './timehandler'
import tool from '@/views/bj_proatal_web/utils/utils'

export default {
  components: {
    NumberTitle,
    LineBarChart,

    TrendLine,
    DateChoose
  },
  inject: ['showExportBtn','mapPermission'],
  data() {
    return {
      activeTargetId: '',
      activeName: '',

      // 第一个图
      chart1Data: [], // 各个指标同比环比的值
      chart1DataLengendData: [], // lengend的数据
      chart1KeyMap: {
        xData: 'appartName', // x轴要取的字段名字
        seriesData: ['score', 'yoyrate', 'momrate'] // seriesData要取的字段
      },

      // 第二个图
      chartTrendData: [], // 趋势图数据
      chartTrendDataLengendData: ['变化值'],
      chartTrendxData: [],
      chartTrendSeriesData: [],
      chartTrendKeyMap: {
        xData: 'statDate', // x轴要取的字段名字
        seriesData: ['scoreDiff'] // seriesData要取的字段
      },

      // 指标
      labelArr: [],
      loading: false,
      trendLoading: false,
      // 查询条件
      dateType: 3, // 默认是月3 日是1 日累计是5
      defaultDate: '202206',

      // 缓存查询echart图的参数 下载的时候传一样的参数
      chart1QueryParams: null,
      chartTrendQueryParams: null

    }
  },

  mounted() {
    // console.log(timetool.getStartTime('2022-03-12', '5'))
    // 只有省级用户有此权限
    if(this.mapPermission.mapLevel > 1) return;
    this.loading = true
    const p1 = this.getLabelsAll()
    const p2 = this.getDefaultDate()

    Promise.all([p1, p2]).then(values => {
      const res1 = values[0]
      const res2 = values[1]
      console.log('values:', values)
      if (res1.code == 200) {
        this.labelArr = res1.data || []
        this.activeName = this.labelArr[0].target_name
        this.activeTargetId = this.labelArr[0].target_id
      } else {
        this.labelArr = []
      }
      if (res2.code == 200) {
        this.defaultDate = res2.data.statDate || '2022-06'
        this.dateType = '3'
      }
      // 获取数据
      const p = {
        targetId: this.activeTargetId,
        statType: this.dateType,
        statDate: this.defaultDate
      }
      // 测试

      // 日2022-06-03，statType:1
      // 月2022-06，statType:3
      // 日累计2022-06-03，statType:5

      this.getChart1Data(p)
      // 获取趋势图
      const startTime = timetool.getStartTime(this.defaultDate, this.dateType)
      const p2 = {
        'targetId': this.activeTargetId,
        'appartId': this.mapPermission.cityId,
        'startTime': startTime,
        'endTime': this.defaultDate,
        'statType': this.dateType
      }
      this.getChartTrendData(p2)
    })
  },
  methods: {
    getChart1Data(p) {
      this.loading = true
      rtgCNTi4(p).then(res => {
        if (res.code == 200) {
          this.chart1QueryParams = p

          this.chart1DataLengendData = [this.activeName, '同比', '环比']
          // const chart1SeriesData = [[], [], []]
          // this.chart1xData = res.data.map(i => i.appartName)
          // chart1SeriesData[0] = res.data.map(i => i.score)
          // chart1SeriesData[1] = res.data.map(i => i.yoyrate)
          // chart1SeriesData[2] = res.data.map(i => i.momrate)
          // this.chart1SeriesData = chart1SeriesData
          tool.handlerMomrateAndYoyrate(res.data)
          this.chart1Data = res.data || []
        }
      }).finally(() => {
        this.loading = false
      })
    },
    downTrendChart() {
      const { chartTrendQueryParams } = this
      this.$confirm('是否确认导出数据项?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          console.log('chartTrendQueryParams:', chartTrendQueryParams)
          return exportComplaintTrendChange(chartTrendQueryParams)
        })
        .then((response) => {
          // window.open(response);
          // const currenTarget = this.lookForAll(this.allTarget).filter(
          //   (item) => item.targetId === targetId
          // )[0]

          // 兼容ie
          if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(
              response,
              '趋势变化' + '.xls'
            )
            return false
          }

          const url = URL.createObjectURL(response)
          const aLink = document.createElement('a')
          aLink.href = url
          aLink.setAttribute('download', '趋势变化' + '.xls')
          document.body.appendChild(aLink)
          aLink.click()
          document.body.removeChild(aLink)
        })
    },
    // 导出
    downChart1() {
      console.log('sssss')
      //     chart1QueryParams: null,
      // chartTrendQueryParams: null
      const { chart1QueryParams } = this

      this.$confirm('是否确认导出数据项?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return exportComplaintSatisfaction(chart1QueryParams)
        })
        .then((response) => {
          // window.open(response);
          // const currenTarget = this.lookForAll(this.allTarget).filter(
          //   (item) => item.targetId === targetId
          // )[0]

          // 兼容ie
          if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(
              response,
              this.activeName + '.xls'
            )
            return false
          }

          const url = URL.createObjectURL(response)
          const aLink = document.createElement('a')
          aLink.href = url
          aLink.setAttribute('download', this.activeName + '.xls')
          document.body.appendChild(aLink)
          aLink.click()
          document.body.removeChild(aLink)
        })
    },

    // 获取指标
    getLabelsAll() {
      // 获取指标
      return uHHOPFVR({ targetId: '622' })

      // .then(res => {
      //   if (res.code == 200) {
      //     this.labelArr = res.data || []
      //     this.activeTargetId = this.labelArr[0].target_id
      //   } else {
      //     this.labelArr = []
      //   }
      // })
    },
    // 获取最新有数据的月份
    getDefaultDate() {
      return DDrLFvxR({})
    },
    dateChange(v, dateType) {
      console.log('this.v:', v)
      console.log('dateType:', dateType)
      this.dateType = dateType == '月' ? '3' : dateType == '日' ? '1' : dateType == '日累计' ? '5' : ''
      this.defaultDate = v
      const startTime = timetool.getStartTime(this.defaultDate, this.dateType)
      const p = {
        targetId: this.activeTargetId,
        statType: this.dateType,
        statDate: this.defaultDate
      }
      this.getChart1Data(p)

      const p2 = {
        'targetId': this.activeTargetId,
        'appartId': this.mapPermission.cityId,
        'startTime': startTime,
        'endTime': this.defaultDate,
        'statType': this.dateType
      }
      this.getChartTrendData(p2)
    },
    chart1BarClick(item) {
      this.chart1Data.forEach(j => {
        if (j.appartName == item.xValue) {
          item = {
            appartId: j.appartId,
            appartName: j.appartName
          }
        }
      })

      const { defaultDate, dateType, activeTargetId } = this
      const startTime = timetool.getStartTime(defaultDate, dateType)
      const p = {
        'targetId': activeTargetId,
        'appartId': item.appartId,
        'startTime': startTime,
        'endTime': defaultDate,
        'statType': dateType
      }
      // 获取下面的趋势图
      this.getChartTrendData(p)
    },

    // 获取趋势图的数据
    getChartTrendData(p) {
      this.trendLoading = true
      e4ISOzuv(p).then(res => {
        if (res.code == 200) {
          this.chartTrendQueryParams = p

          this.chartTrendData = res.data || []
          this.chartTrendxData = res.data.map(i => i.statDate) || []
          this.chartTrendSeriesData = res.data.map(i => i.scoreDiff) || []
        }
      }).finally(() => {
        this.trendLoading = false
      })
    },

    handleTabClick(item) {
      this.activeTargetId = item.paneName
      this.activeName = item.label

      const p = {
        targetId: this.activeTargetId,
        statType: this.dateType, // 默认月
        statDate: this.defaultDate
      }
      this.getChart1Data(p)

      const startTime = timetool.getStartTime(this.defaultDate, this.dateType)
      console.log('startTime:', startTime)
      const p2 = {
        'targetId': this.activeTargetId,
        'appartId': this.mapPermission.cityId,
        'startTime': startTime,
        'endTime': this.defaultDate,
        'statType': this.dateType
      }
      this.getChartTrendData(p2)
    }

  }
}
</script>
<style lang="scss" scoped>
.imp{
     background-color: rgba(242, 242, 242, 1);
     padding:30px 40px 40px 40px;
     min-height: 100vh;

     /deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }
}
.con{
    position: relative;

}
.timecon{
    position: absolute;
    right:0;
    top:-60px;
}

// echarts 图
.box{
  background:#fff;
  margin-top:20px;
  padding-top:5px;
}
.cardtitle{
    font-family: "PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC", sans-serif;
    font-weight: bold;
    font-size: 16px;
    line-height: 32px;
}
.chartbox{

    width:100%;
    padding-top:10px;
    position: relative;
}
.export{
  position:absolute;
  right:30px;
  top:10px;

}
.flex{
  display:flex;
  >div{
    width:50%
  }
}

/deep/.el-tabs__nav-wrap::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background-color: #dfe4ed;
    z-index: 1;
}

</style>

