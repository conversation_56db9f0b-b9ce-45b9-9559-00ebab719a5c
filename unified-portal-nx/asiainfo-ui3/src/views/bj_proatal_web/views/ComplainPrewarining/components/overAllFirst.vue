<template>
  <div class="overallFirst">
    <NumberTitle num="01" text="投诉总量及万投比" />
    <div class="header-search">
      <div class="block">
        <!-- <div style="margin-right: 10px">
          <span class="labelcss">选择地市：</span>
          <el-cascader
            v-model="idLists"
            class="el-cascader"
            placeholder="请选择地市"
            :options="optionsCascader"
            :size="conditionsize"
            :props="props"
            @change="areaChange"
          />
        </div> -->
        <AreaPicker
          size="small"
          :ext-options="mapPermission.mapLevel == 1 ? [
            {name:'全省按部门',id:'999999',data:{ name:'全省按部门',id: '999999',level:1}}
          ] : []"
          @change="areaChange"
        />
      <NewDatePicker :dateTypes="['date','month','daycumulative']" :tableName="'portal_indicator_appeal_monitor_data'" @change="dateChange"></NewDatePicker>
      </div>
    </div>
    <div class="content">
      <div class="leftChart">
        <LineBarChart
          v-if="echartsLeave === 0"
          :all-data="chartLeftData"
          :legend-data="['投诉总量', '万投比']"
          :keymap="{
            xData: 'statdate',
            seriesData: ['score', 'eoms'],
          }"
          :is-double-line="false"
          @barclick="leftBarclick"
        />
        <LineBarChart
          v-if="echartsLeave >= 1"
          :all-data="chartLeftData2"
          :legend-data="['投诉量', '环比', '同比']"
          :keymap="{
            xData: 'businessname',
            seriesData: ['score', 'momrate', 'yearbasis'],
          }"
          @barclick="leftBarclick2"
        />
      </div>
      <img
        v-if="echartsLeave > 0"
        class="goBack"
        src="../../../assets/img/expCourse/return.png"
        @click="goBack"
      >
      <el-button
        v-show="showExportBtn"
        class="leftBtn"
        size="small"
        type="primary"
        @click="exportOne"
      >导出</el-button>
      <div class="rightChart">
        <LineBarChartR
          :is-show-tip="isShowTip"
          :all-data="chartRightData"
          :legend-data="['投诉量', '环比']"
          :data-list="dataList"
          :keymap="{
            xData: 'appartname',
            seriesData: ['score', 'momrate'],
          }"
          :is-double-line="false"
        />
      </div>
      <el-button
        v-show="showExportBtn"
        class="rightBtn"
        size="small"
        type="primary"
        @click="exportTwo"
      >导出</el-button>
    </div>
  </div>
</template>
<script>
import {
  getComplainByTime,
  getComplainBySection,
  getComplainByBusiness,
  esportComplaint
} from '@/api/complaint-analysis/api'
import NumberTitle from '../../../components/common/numberTitle'
import { AreaByUser } from '@/api/mobilePhoneTariff'
import LineBarChart from './linebarChartsT'
// import LineBarChartR from "./linebarChartsR";
import LineBarChartR from './linebarChartsRTipshow.vue'
import tool from '@/views/bj_proatal_web/utils/utils'
import AreaPicker from 'bj_src/nx-components/area-picker/AreaPicker.vue'
import NewDatePicker from '@/views/bj_proatal_web/components/date-picker/newDatePicker'
export default {
  name: 'OverallFirst',
  components: {
    NumberTitle,
    LineBarChart,
    LineBarChartR,
    AreaPicker,
    NewDatePicker
  },
  inject: ['showExportBtn', 'mapPermission'],
  data() {
    const _this = this
    const year = new Date().getFullYear()
    let month = Number(new Date().getMonth()) + 1
    if (month < 10) {
      month = '0' + month
    }

    return {
      conditionsize: 'small',
      props: {
        label: 'name',
        value: 'id',
        checkStrictly: true,
        emitPath: false
      },
      isShowTip: true,
      selectType: '2',
      appartLevel: '0',
      appartRoute: '',
      businessName: '',
      businessRoute: '',
      statType: '3',
      optionsCascader: [
        { cityName: '全省按部门', level: 1, name: '全省按部门', id: '999999' }
      ],
      idLists: ['640000'],
      cityId: this.mapPermission.cityId,
      defaultDateTypeList: ['日', '月', '日累计'],
      defaultDate: String(year) + String(month),
      chartLeftData: [],
      chartLeftData2: [],
      chartRightData: [],
      echartsLeave: 0,
      barclickTime: '',
      dataList: {
        targetId: 'a0001',
        opTime: '',
        cityId: '',
        selectType: '',
        statType: ''
      }
    }
  },

  mounted() {
    this.initArea()
    this.dataList = {
      targetId: 'a0001',
      opTime: this.formatDate(this.defaultDate),
      cityId: this.cityId,
      selectType: this.selectType,
      statType: this.statType
    }
  },
  created() {},
  methods: {
    exportOne() {
      // 左侧导出
      if (this.statType == '3' && this.defaultDate.indexOf('-', 0) == '-1') {
        this.defaultDate =
          this.defaultDate.slice(0, 4) + '-' + this.defaultDate.slice(4)
      }
      const monthNum = parseFloat(this.defaultDate.slice(6, 7))
      let params = ''
      if (this.echartsLeave == 0) {
        params = `?cityId=${this.cityId}&opTime=${this.defaultDate}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.statType}&targetId=a0001&chartNum=1&monthNum=${monthNum}&businessLevel=${this.echartsLeave}&appartLevel=${this.appartLevel}&businessName=${this.businessName}&selectType=${this.selectType}&appartRoute=${this.appartRoute}`
      } else {
        params = `?cityId=${this.cityId}&opTime=${this.barclickTime}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.statType}&targetId=a0001&chartNum=2&monthNum=${monthNum}&businessLevel=${this.echartsLeave}&appartLevel=${this.appartLevel}&businessName=${this.businessName}&selectType=${this.selectType}&appartRoute=${this.appartRoute}`
      }
      esportComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            '投诉总量及万投比' + '.xls'
          )
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '投诉总量及万投比' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    exportTwo() {
      // 右侧导出
      if (this.statType == '3' && this.defaultDate.indexOf('-', 0) == '-1') {
        this.defaultDate =
          this.defaultDate.slice(0, 4) + '-' + this.defaultDate.slice(4)
      }

      const monthNum = parseFloat(this.defaultDate.slice(6, 7))
      const params = `?cityId=${this.cityId}&opTime=${this.barclickTime}&startTime=2022-06-05&endTime=2022-06-08&statType=${this.statType}&targetId=a0001&chartNum=3&monthNum=${monthNum}&businessLevel=${this.echartsLeave}&appartLevel=${this.appartLevel}&businessName=${this.businessName}&selectType=${this.selectType}&appartRoute=${this.appartRoute}`
      esportComplaint(params).then((response) => {
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(
            response,
            '投诉总量及万投比' + '.xls'
          )
          return false
        }
        const url = URL.createObjectURL(response)
        const aLink = document.createElement('a')
        aLink.href = url
        aLink.setAttribute('download', '投诉总量及万投比' + '.xls')
        document.body.appendChild(aLink)
        aLink.click()
        document.body.removeChild(aLink)
      })
    },
    goBack() {
      this.echartsLeave = this.echartsLeave - 1
      if (this.echartsLeave == 1) {
        this.businessRoute = sessionStorage.getItem('businessRoute')
        this.businessName = this.businessRoute
        this.leftBarclick()
      } else if (this.echartsLeave >= 2) {
        this.businessRoute = this.businessRoute.substring(
          0,
          this.businessRoute.lastIndexOf('-')
        )
        if (this.echartsLeave > 2) {
          this.businessName = this.businessRoute.substring(
            this.businessRoute.lastIndexOf('-') + 1
          )
        } else {
          this.businessName = this.businessRoute
        }
        this.leftBarclick2()
      } else {
        this.getLeftEchartData()
      }
      this.getRightEchartData()
    },
    initArea() {
      this.getLeftEchartData()
      this.getRightEchartData()
      // 初始化获取所有区域
      // const { data, code } = await AreaByUser({
      //   restSerialNo: '6MujCDCs'
      // })
      // if (code == 200) {
      //   this.optionsCascader = this.optionsCascader.concat(data[0])
      //   this.getLeftEchartData()
      //   this.getRightEchartData()
      // }
    },
    // 日期变化
    dateChange(params) {
      const [ dateType, v ] = params
      this.statType =
        dateType == '月'
          ? '3'
          : dateType == '日'
            ? '1'
            : dateType == '日累计'
              ? '5'
              : ''
      this.defaultDate = v
      this.getLeftEchartData()
      this.getRightEchartData()
      this.dataList.statType = this.statType
      this.dataList.opTime = this.formatDate(this.defaultDate)
    },
    formatDate(val) {
      if (this.statType == '3' && val.indexOf('-', 0) == '-1') {
        val = val.slice(0, 4) + '-' + val.slice(4)
      }
      val = new Date(val)
      const year = val.getFullYear()
      const month =
        val.getMonth() + 1 < 10 ? `0${val.getMonth() + 1}` : val.getMonth() + 1
      const date = val.getDate() < 10 ? `0${val.getDate()}` : val.getDate()

      if (this.statType == '3') {
        return `${year}${month}`
      } else {
        return `${year}${month}${date}`
      }
    },
    // 地市变化
    areaChange(value) {
      const val = Array.isArray(value) ? value[value.length - 1] : value
      if (val == '999999') {
        this.cityId = '640000'
        this.selectType = '1'
        this.appartLevel = '1'
      } else {
        this.cityId = val
        this.selectType = '2'
        this.appartLevel = '0'
      }
      this.getLeftEchartData()
      this.getRightEchartData()
      this.dataList.cityId = this.cityId
      this.dataList.selectType = this.selectType
    },
    // 查询接口-左边图例
    async getLeftEchartData() {
      if (this.statType == '3' && this.defaultDate.indexOf('-', 0) == '-1') {
        this.defaultDate =
          this.defaultDate.slice(0, 4) + '-' + this.defaultDate.slice(4)
      }
      const monthNum = parseFloat(this.defaultDate.slice(6, 7))

      const params = {
        statType: this.statType,
        opTime: this.defaultDate,
        cityId: this.cityId,
        targetId: 'a0001',
        monthNum: monthNum
      }
      const { data } = await getComplainByTime(params)
      if (data) {
        tool.handlerMomrateAndYoyrate(data.data)
        this.chartLeftData = data.data
      }
    },
    // 查询接口-右边图例
    async getRightEchartData() {
      if (this.statType == '3' && this.defaultDate.indexOf('-', 0) == '-1') {
        this.defaultDate =
          this.defaultDate.slice(0, 4) + '-' + this.defaultDate.slice(4)
      }
      let level = ''
      if (this.echartsLeave > 0) {
        level = (this.echartsLeave - 1).toString()
      } else {
        level = '0'
        this.barclickTime = this.defaultDate
      }
      const params = {
        opTime: this.barclickTime,
        cityId: this.cityId,
        statType: this.statType,
        targetId: 'a0001',
        businessLevel: level,
        businessRoute: this.businessRoute,
        appartLevel: this.appartLevel,
        businessName: this.businessName,
        appartRoute: this.appartRoute,
        selectType: this.selectType
      }

      const { data } = await getComplainBySection(params)
      if (data) {
        tool.handlerMomrateAndYoyrate(data.data)
        this.chartRightData = data.data
      }
    },
    // 点击柱状图
    async leftBarclick(item) {
      this.echartsLeave = 1
      if (item) {
        this.businessRoute = item.xValue
        this.businessName = item.xValue
        this.barclickTime = item.xValue
        sessionStorage.setItem('businessRoute', this.businessRoute)
      }
      const params = {
        opTime: this.barclickTime,
        cityId: this.cityId,
        statType: this.statType,
        targetId: 'a0001',
        businessLevel: this.echartsLeave.toString(),
        businessRoute: this.businessRoute
      }
      const { data } = await getComplainByBusiness(params)
      if (data) {
        tool.handlerMomrateAndYoyrate(data.data)
        this.chartLeftData2 = data.data
        this.getRightEchartData()
      }
    },
    // 点击柱状图
    async leftBarclick2(item) {
      if (item) {
        this.echartsLeave++
        this.businessName = item.xValue
        sessionStorage.setItem('businessName', item.xValue)
        if (item.xValue && this.echartsLeave > 2) {
          this.businessRoute = this.businessRoute + '-' + item.xValue
        } else if (item.xValue && this.echartsLeave == 2) {
          this.businessRoute = item.xValue
        }
      }
      const params = {
        opTime: this.barclickTime,
        cityId: this.cityId,
        statType: this.statType,
        targetId: 'a0001',
        businessLevel: this.echartsLeave.toString(),
        businessRoute: this.businessRoute
      }
      const { data } = await getComplainByBusiness(params)
      if (data) {
        tool.handlerMomrateAndYoyrate(data.data)
        this.chartLeftData2 = data.data
        this.getRightEchartData()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.overallFirst {
  position: relative;
  background-color: rgba(242, 242, 242, 1);
  padding: 30px 40px 40px 40px;
  min-height: 45vh;
}
.el-cascader {
  /deep/.el-icon-arrow-down:before {
    content: "\E6E1";
  }
  /deep/.el-icon-arrow-down {
    transform: rotate(180deg);
  }
  /deep/.is-reverse.el-icon-arrow-down {
    transform: rotate(0deg);
  }
}
.header-search {
  position: absolute;
  right: 30px;
  top: 60px;
  .block {
    display: flex;
  }
}
.content {
  position: relative;
  width: 100%;
  min-height: 450px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  background: #ffffff;
  .leftChart,
  .rightChart {
    width: 45%;
    height: 450px;
  }
  button {
    position: absolute;
  }
  .rightBtn {
    right: 15px;
    top: 10px;
  }
  .leftBtn {
    left: 42%;
    top: 10px;
  }
  .goBack {
    cursor: pointer;
    position: absolute;
    left: 39%;
    top: 13px;
    z-index: 12;
  }
}
</style>
>
