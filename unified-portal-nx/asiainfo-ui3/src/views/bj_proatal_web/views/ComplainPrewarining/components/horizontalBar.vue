<template>
  <div class="hunsty" style="position: relative">
    <div :id="t" class="hunsty" :style="{ opacity: allData.length ? 1 : 0 }" />
    <div
      :style="{
        position: 'absolute',
        left: 0,
        top: 0,
        width: '100%',
        height: '100%',
        display: allData.length ? 'none' : 'block',
      }"
    >
      <Blank2 />
    </div>
  </div>
</template>

<script>
import { getTop5Detail } from '@/api/complaint-topic/index.js'
import Blank2 from '@/views/bj_proatal_web/components/common/Blank2'
export default {
  name: 'HorizontalBar',
  components: {
    Blank2
  },
  props: {
    keymap: {
      type: Object,
      default: () => {
        return {
          yName: '', // y轴的名称
          yData: 'appartName', // y轴取的字段
          seriesData: ['score'] // 对应值取的字段
        }
      }
    },
    allData: {
      type: Array,
      default: () => {
        return []
      }
    },
    option: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isShowTip: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
      t: '',
      chart: null,
      valueList: [],
      showChart: true
    }
  },
  computed: {
    yData() {
      const temp = this.allData.map((i) => i[this.keymap['yData']])
      temp.reverse()
      return temp
    },
    seriesData() {
      const temp = []
      this.keymap['seriesData'].forEach((i, idx) => {
        temp[idx] = this.allData.map((x) => x[i]).reverse()
      })

      return temp
    }
  },
  watch: {
    allData(v, oldv) {
      this.initChart()
    }
  },
  created() {
    this.t = new Date().getTime() + (Math.random() * 100).toFixed(0)
  },
  mounted() {
    var _this = this
    this.$nextTick(() => {
      this.initChart()
    })
    window.addEventListener('resize', function() {
      _this.chart.resize()
    })
  },
  methods: {
    initChart() {
      this.chart ? this.chart.dispose() : ''
      const dom = document.getElementById(this.t)
      this.chart = this.$echarts.init(dom)
      this.renderChart()
    },
    renderChart() {
      const option = {
        grid: {
          top: '20%',
          left: '2%',
          right: '8%',
          bottom: '5%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
          show: false
        },
        yAxis: [
          {
            name: this.keymap.yName,
            type: 'category',
            data: this.yData,
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            axisLabel: {
              show: true // 不显示坐标轴上的文字
            },
            splitLine: {
              show: false // 不显示网格线
            }
          }
        ],
        series: [
          {
            // name: "2011",
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              // 柱状图的背景颜色
              color: '#f0f0f0'
            },
            itemStyle: {
              // 柱状图实际数据的颜色
              color: '#7097F6'
            },
            barWidth: 15,
            // data: this.yAxisData
            data: this.seriesData[0]
          }
        ]
      }
      if (this.isShowTip) {
        option.tooltip = {
          trigger: 'axis',
          backgroundColor: 'rgba(255,255,255,1)',
          textStyle: {
            color: '#262626',
            align: 'left'
          },
          confine: true,
          extraCssText: 'box-shadow:0px 2px 8px 0px rgba(102, 61, 0, 0.16)',
          formatter: function(datas, ticket, callBack) {
            // if (datas && datas.length) {
            //   datas.forEach((i) => {
            //     i.value = tool.dealFloatNum(i.value);
            //   });
            // }
            getTop5Detail({
              opTime: '2022-04',
              cityId: '640000',
              targetAlias: '热点业务1',
              targetId: '402020301',
              statType: '3'
            }).then((res) => {
              const html = 1233333333333333333
              callback(ticket, html)
            })

            return '数据查询中'
          }
        }
      }

      this.chart.setOption(option)
      this.chart.hideLoading()
    }
  }
}
</script>
<style lang="scss" scoped>
.hunsty {
  height: 100%;
  width: 100%;
}
</style>
