<template>
  <div class="bar-main">
    <div ref="chart" class="barChart" />
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'TariffBarCharts',
  props: {
    activeName: String,
    data: Array
  },
  data() {
    return {}
  },
  computed: {
    // 时间x轴
    xAxisList() {
      const arr = []
      if (this.data && this.data.length) {
        this.data.forEach((item) => {
          if (item.targetName) {
            arr.push(item.targetName)
          } else {
            arr.push(item.targetAlias)
          }
        })
      }
      console.log(arr)
      return arr
    },
    yAxisData() {
      const arr = []
      if (this.data && this.data.length) {
        this.data.forEach((item) => {
          if (item.value) {
            arr.push(item.value)
          } else {
            arr.push(item.targetValue)
          }
        })
      }
      console.log(arr)

      return arr
    }
  },
  watch: {
    data() {
      console.log(this.activeName)
      this.initChart()
    }
  },
  mounted() {
    // this.createChart();
  },
  methods: {
    initChart() {
      if (
        this.chart !== null &&
        this.chart !== '' &&
        this.chart !== undefined
      ) {
        this.chart.dispose()
      }
      this.chart = echarts.init(this.$refs.chart, null, {
        render: 'svg'
      })
      this.createChart()
      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    },
    createChart() {
      const barOption = {
        title: {
          // text: "资费办理表现值TOP10",
          text: this.activeName,
          top: '10%',
          left: '3%',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#262626'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          top: '30%',
          left: '5%',
          right: '5%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
          show: false
        },
        yAxis: [
          {
            // name: "资费办理表现值TOP10",
            type: 'category',
            // data: ["Brazil", "Indonesia", "USA", "India", "China", "World"],
            data: this.xAxisList.reverse(),
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            axisLabel: {
              show: true // 不显示坐标轴上的文字
            },
            splitLine: {
              show: false // 不显示网格线
            }
          },
          {
            name: '表现值',
            position: {
              left: 100
            },
            nameTextStyle: {
              // padding: [0, 0, 0, 55],
            },
            // data: [18203, 23489, 29034, 104970, 131744, 630230],
            data: this.yAxisData.reverse(),
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            }
          }
        ],
        series: [
          {
            // name: "2011",
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              // 柱状图的背景颜色
              color: '#f0f0f0'
            },
            itemStyle: {
              // 柱状图实际数据的颜色
              color: '#7097F6'
            },
            barWidth: 15,
            data: this.yAxisData

          }
        ]
      }
      this.chart.clear()
      this.chart.setOption(barOption, true)

      if (this.data.length == 0) {
        this.chart.showLoading({
          text: '暂无数据',
          color: 'rgba(255, 255, 255, 0)',
          fontSize: 20,
          textColor: '#8a8e91',
          maskColor: 'rgba(255, 255, 255, 1)'
        })
      } else {
        this.chart.hideLoading()
      }
    }
  }
}
</script>

<style  lang='less' scoped>
.bar-main {
  position: relative;
  width: 100%;
  height: 380px;
  text-align: center;
  .barChart {
    width: 90%;
    height: 100%;
  }
}
</style>
