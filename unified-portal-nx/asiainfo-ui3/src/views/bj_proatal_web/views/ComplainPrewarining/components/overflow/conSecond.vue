<template>
  <div class="business">
    <NumberTitle num="02" text="投诉分类" />
    <div class="con">
      <div style="position:absolute;right:0px;top:-60px">
        <AreaPicker class="areabox" size="small" :max-area-level="2" @change="areaChange" />
        <div style="display:inline-block">
          <WeekDatePick @weekChange="weekChange" />
        </div>
      </div>
      <div class="con"  v-loading="loading">
        <div class="radiobox">
          <el-radio-group v-model="errorType" @change="radioChange">
            <el-radio-button label="1">两类差错</el-radio-button>
            <el-radio-button label="2">反悔办理</el-radio-button>
          </el-radio-group>
        </div>
        <div class="chartBox" v-if="echartData.xData.length">
          <div>
            <div class="borderBottom border" style="line-height:40px;padding-left:20px;font-size:14px;font-weight:bold;">
              地市网格类型分布
            </div>
          </div>
          <div class="border" style="height:500px;width:100%;border-top:none">
            <GridChart :dataMap="echartData" />
          </div>

        </div>
        <div class="chartBox" v-else>
          <Blank2  />
        </div>
       
      </div>
     
    </div>

  </div>
</template>
<script>
import NumberTitle from '@/views/bj_proatal_web/components/common/numberTitle'
import AreaPicker from '@/views/bj_proatal_web/nx-components/area-picker/AreaPicker.vue'
import WeekDatePick from './weekDatePicker.vue'
import GridChart from './gridEchart.vue'
import { getTwoErrorByArea } from '@/api/complainPrewarining'

// getTwoErrorByArea,getTwoErrorByGrid

export default {
  inject: ['mapPermission'],
  components: {
    NumberTitle,
    AreaPicker,
    WeekDatePick,
    GridChart
  },
  data() {
    const day = new Date().getDay()
    const oneDayTime = 24 * 60 * 60 * 1000
    const nowTime = new Date().getTime()
    // 显示周一 先算出本周一 再减去7 天 默认上周一
    let lastMondayTime = nowTime - (day - 1) * oneDayTime - 7 * oneDayTime
    // 显示周日 先算出本周末 再减去7 天 默认上周末
    let lastSundayTime = nowTime + (7 - day) * oneDayTime - 7 * oneDayTime
    // 初始化日期时间
    lastMondayTime = this.parseTime(lastMondayTime, '{y}-{m}-{d}')
    lastSundayTime = this.parseTime(lastSundayTime, '{y}-{m}-{d}')
    console.log('lastMondayTime=>', lastMondayTime)
    return {
      errorType: '1',
      endTime:`${lastMondayTime}|${lastSundayTime}`,
      cityId: this.mapPermission.cityId,
      loading:false,
      echartData:{
        xData:[],
        dataList:[],
        typeNames:['乡镇','商圈','综合','社区']
      }
    
    }
  },
  mounted(){
    this.query();
  },
  methods: {
    query(){
        let {endTime,cityId,errorType} = this;
        this.loading=true;
        getTwoErrorByArea({endTime,cityId,errorType}).then(res=>{
          let {code,data} = res;
          
          if(code==200 && data) {

            let typeObj = {
            '乡镇':[],
            '商圈':[],
            '综合':[],
            '社区':[]
          }
          
          let xData=[];
          let dataList = [];
            data.forEach((x)=>{
              x.data = x.data?x.data:{},
              // 乡镇 商圈 综合 社区
              xData.push(x.name)
              Object.keys(typeObj).forEach(k=>{
                typeObj[k].push(x.data[k])
              })
            })
            console.log('typeObj=>',typeObj);
            let tempKeys=['乡镇','商圈','综合','社区'];
            for(let i=0;i<tempKeys.length;i++) {
              console.log('===>',typeObj[tempKeys[i]]);
              dataList.push(typeObj[tempKeys[i]]);
            }
            console.log('==>',dataList)
            this.echartData.xData = xData||[]
            this.echartData.dataList= dataList||[]

          }
          console.log('data=>',this.echartData);
        }).finally(()=>{
          this.loading=false;
        })
    },
    areaChange(v) {
      if(v.length){
         this.cityId = v[v.length-1];
         this.query();
      }
    },
    radioChange() {
      this.query();
    },
    weekChange(week) {
      console.log('week=>',week);
      this.endTime = week;
      this.query();
    }
  }
}
</script>

<style lang="scss" scoped>

.business{
     background-color: rgba(242, 242, 242, 1);
     padding:30px 40px 0px 40px;
    //  min-height: 100vh;
     /deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }
}
.con{
  position: relative;
  background:#fff;
  margin-top:10px;
}
.chartBox{
  padding:10px;
  width:100%;
}
.borderBottom{
  border-bottom: 1px solid #e6e6e6;
}
.radiobox{
  padding:20px 0;
}
.flex{
  display:flex;
  &.mapbox{
    >div{
      width:50%;
      flex:1;
    }
  }
}
.cus-border {
      height: 93%;
      width: 1px;
      margin: 3% 0;
      background-color: #ddd;
      float: right;
    }
.pt{
    font-weight: bold;
    font-size: 16px;
    line-height: 32px;
    padding:10px 10px 0px 10px;

}

.show {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #6b6b6b;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hide {
  display: none;
}
.border{
  border:1px solid #e6e6e6;
}
</style>

