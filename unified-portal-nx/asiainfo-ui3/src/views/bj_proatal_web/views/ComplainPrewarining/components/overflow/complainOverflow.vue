<template>
  <div>
    <div class="home-broad-error">
      <Banner title="两类差错及反悔办理投诉" desc="两类差错及反悔办理投诉总览" />
      <ConFirst />
      <ConSecond />
      <Conthird />
    </div>
  </div>
</template>

<script>
import Banner from '../../../../components/common/Banner'
import Conthird from './conThird.vue'
import ConSecond from './conSecond.vue'
import ConFirst from './conFirst.vue'

export default {
  components: {
    Banner,
    Conthird,
    ConSecond,
    ConFirst

  },
  data() {
    return {

    }
  }

}
</script>

<style lang="less" scoped>
.content {
  width: 100%;
  height: 500px;
  color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
}

</style>
