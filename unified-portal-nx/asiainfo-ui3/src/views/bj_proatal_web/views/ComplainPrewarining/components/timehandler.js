import tool from '@/views/bj_proatal_web/utils/utils'
export default {
  // startTime处理
//   dateType 3 表示月  1表示日   5 表示日累计 4表示季度
  getStartTime: (endTime, dateType) => {
    let startTime = ''
    console.log('endTime:', endTime)
    // 日、日累计、月
    // 日：选中日期 展示前7天的数据（包括选中日期）
    // 日累计：展示选中日期，当月1日到选中日的累计值
    // 月：选中月份 展示当前年的全部月份（如果不满6个月往前补足6个月）
    if (dateType == 3) { // 月
      let d = `${endTime}-01`
      d = new Date(d)
      let mon = d.getMonth() + 1
      let year = d.getFullYear()
      if (mon > 5) {
        startTime = `${year}-01`
      } else { // 当年月份小于6个月 往前面补足6个月
        year = year - 1
        mon = 13 - (6 - mon)
        if (mon < 10) mon = `0${mon}`
        startTime = `${year}-${mon}`
      }
    } else if (dateType == 1) { // 日
      let d = new Date(endTime)
      d = d.getTime()
      startTime = new Date(d - 7 * 24 * 60 * 60 * 1000)
      startTime = tool.formatterDate(startTime, 'yyyy-MM-dd')
    } else if (dateType == 5) { // 日累计
      // '2022-02-21'
      startTime = endTime.slice(0, 8) + '01'
    } else if (dateType == 4) { // 选中的实 往前推4个季度
      // 2022-Q1
      // let endTime = '2022-Q1';
      const endTimeNum = endTime.slice(endTime.length - 1)
      const endTimeYear = endTime.slice(0, 4)
      const startTimeNum = endTimeNum == 4 ? 1 : endTimeNum == 3 ? 4 : endTimeNum == 2 ? 3 : endTimeNum == 1 ? 2 : ''
      const startYear = endTimeNum > 3 ? endTimeYear : endTimeYear - 1
      startTime = `${startYear}-Q${startTimeNum}`
    }
    return startTime
  },
  // 获取前面12个月 获取前面4个季度 返回数组
  getXtimes(endTime, dateType, len = 12) { // len表示往前推多少 要么是6 要么是12
    const monthJZ = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
    const season = ['1', '2', '3', '4']
    const temparr = []
    if (dateType == 3) { // 月份
      // 2022-01
      let d = `${endTime}-01`
      d = new Date(d)
      const mon = d.getMonth() + 1
      const year = d.getFullYear()
      //  回到前一年
      const lastyear = year - 1

      for (let i = 0; i < len; i++) {
        let str = ''
        const x = mon - i
        if (x > 0) {
          if (x < 10) str = `${year}-0${x}`
          if (x >= 10) str = `${year}-${x}`
        }
        if (x <= 0) {
          const t = 12 + x
          if (t < 10) str = `${lastyear}-0${t}`
          if (t >= 10) str = `${lastyear}-${t}`
        }
        temparr.push(str)
      }
    }
    return temparr
  }
}
