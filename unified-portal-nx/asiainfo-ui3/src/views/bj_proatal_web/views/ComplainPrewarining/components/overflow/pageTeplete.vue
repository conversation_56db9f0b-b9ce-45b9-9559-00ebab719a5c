<template>
  <div class="business">
    <NumberTitle num="03" text="投诉网格TOP" />
    <div class="con">
      <div style="position:absolute;right:0px;top:-60px">
        <AreaPicker class="areabox" size="small" :max-area-level="2" @change="areaChange" />
        <div style="display:inline-block">
          <WeekDatePick @weekChange="weekChange" />
        </div>

      </div>
      <div class="con">dssss</div>
    </div>

  </div>
</template>
<script>
import NumberTitle from '@/views/bj_proatal_web/components/common/numberTitle'
import AreaPicker from '@/views/bj_proatal_web/nx-components/area-picker/AreaPicker.vue'
import WeekDatePick from './weekDatePicker.vue'

export default {
  components: {
    NumberTitle,
    AreaPicker,
    WeekDatePick
  },
  methods: {
    areaChange() {

    },
    weekChange(week) {
      console.log(week)
    }
  }
}
</script>

<style lang="scss" scoped>

.business{
     background-color: rgba(242, 242, 242, 1);
     padding:30px 40px 0px 40px;
     min-height: 100vh;
     /deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }
}
.con{
  position: relative;
  background:#fff;
  margin-top:10px;
}
.flex{
  display:flex;
  &.mapbox{
    >div{
      width:50%;
      flex:1;
    }
  }
}
.cus-border {
      height: 93%;
      width: 1px;
      margin: 3% 0;
      background-color: #ddd;
      float: right;
    }
.pt{
    font-weight: bold;
    font-size: 16px;
    line-height: 32px;
    padding:10px 10px 0px 10px;

}

.show {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #6b6b6b;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hide {
  display: none;
}
</style>

