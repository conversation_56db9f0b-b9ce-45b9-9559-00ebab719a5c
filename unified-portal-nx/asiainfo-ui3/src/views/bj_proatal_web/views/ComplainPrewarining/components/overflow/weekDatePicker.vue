<template>
  <div class="time-select">
    <div style="font-size:14px">选择时间：</div>
    <el-date-picker
      v-model="weekChosed"
      style="width:210px"
      value-format="yyyy-MM-dd"
      type="week"
      size="small"
      :format="startTimeStamp + ' - ' + endTimeStamp"
      :clearable="false"
      placeholder="选择周"
      :picker-options="pickerOptionsDate"
      @input="weekhandleClick"
    />
  </div>

</template>
<script>

export default {

  data() {
    const day = new Date().getDay()
    const oneDayTime = 24 * 60 * 60 * 1000
    const nowTime = new Date().getTime()
    // 显示周一 先算出本周一 再减去7 天 默认上周一
    const MondayTime = nowTime - (day - 1) * oneDayTime - 7 * oneDayTime
    // 显示周日 先算出本周末 再减去7 天 默认上周末
    const SundayTime = nowTime + (7 - day) * oneDayTime - 7 * oneDayTime
    // 初始化日期时间
    const monday = new Date(MondayTime)
    const sunday = new Date(SundayTime)
    return {
      startTimeStamp: this.parseTime(monday, '{y}-{m}-{d}'),
      endTimeStamp: this.parseTime(sunday, '{y}-{m}-{d}'),
      weekChosed: new Date(monday),
      pickerOptionsDate: {
        firstDayOfWeek: 1,
        disabledDate(time) {
          const bb = new Date()
          bb.setFullYear(bb.getFullYear() - 1)
          return time.getTime() > Date.now()
        }
      }
    }
  },

  methods: {
    weekhandleClick() {
      const now = new Date(this.weekChosed)
      const nowTime = now.getTime()
      const day = now.getDay()
      const oneDayTime = 24 * 60 * 60 * 1000
      // 显示周一
      const MondayTime = nowTime - (day - 1) * oneDayTime
      // 显示周日
      const SundayTime = nowTime + (7 - day) * oneDayTime
      // 初始化日期时间
      const monday = new Date(MondayTime)
      const sunday = new Date(SundayTime)
      this.weeks = ''
      this.startTimeStamp = this.parseTime(monday, '{y}-{m}-{d}')
      this.endTimeStamp = this.parseTime(sunday, '{y}-{m}-{d}')
      this.$emit('weekChange',  `${this.startTimeStamp}|${this.endTimeStamp}`)
      return this.startTimeStamp + '|' + this.endTimeStamp
    }

  }
}
</script>

<style lang="scss" scoped>

.time-select {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.business{
     background-color: rgba(242, 242, 242, 1);
     padding:30px 40px 0px 40px;
     min-height: 100vh;
     /deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }
}
.con{
  position: relative;
  background:#fff;
  margin-top:10px;
}
.flex{
  display:flex;
  &.mapbox{
    >div{
      width:50%;
      flex:1;
    }
  }
}
.cus-border {
      height: 93%;
      width: 1px;
      margin: 3% 0;
      background-color: #ddd;
      float: right;
    }
.pt{
    font-weight: bold;
    font-size: 16px;
    line-height: 32px;
    padding:10px 10px 0px 10px;

}

.show {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  background-color: #6b6b6b;
  display: flex;
  justify-content: center;
  align-items: center;
}
.hide {
  display: none;
}
</style>

