<template>
  <div v-loading="loading" class="imp">
    <NumberTitle num="01" text="重点业务监控" />
    <div class="con">
      <div class="timecon flexr">
        <div>
          <DateChoose :default-date-type-list="['日','月','日累计']" :default-date="defaultDate" @dateChange="dateChange" />
        </div>
        <!-- 只有家宽报障 灭灯行动治理情况 才有地市部门 后面三个只有部门没有地市-->
        <div v-show="activeTargetId=='62302'||activeTargetId=='62301'">
          <!-- <AreaSelect @sendCityId="areaChange" /> -->
          <AreaPicker size="small" :ext-options="mapPermission.mapLevel == 1 ? [
          {name:'全省按部门',id:'640000x',data:{ name:'全省按部门',id: '640000x',level:1}}
          ] : []"  @change="areaChange"/>
        </div>

      </div>

      <div class="box">
        <el-tabs v-model="activeTargetId" @tab-click="handleTabClick">
          <el-tab-pane v-for="item in labelArr" :key="item.id" :label="item.name" :name="item.id">
            <el-radio-group v-show="activeTargetId=='62302'" v-model="radioChosed" size="mini" style="margin:10px 0 30px 10px" @change="handleRadioTabClick">
              <el-radio v-for="childitem in childLabelArr" :key="childitem.id" :label="childitem.id">{{ childitem.name }}</el-radio>
            </el-radio-group>
          </el-tab-pane>
        </el-tabs>
        <div v-loading="chart1Loading" v-permission="tabPermission" >
          <div class="flex">
            <div class="cus">
              <div class="flex tbox">
                <div class="cardtitle">投诉总量</div>
                <div v-show="chart1Data.length&&showExportBtn" style="text-align:right"><el-button type="primary" size="small" @click="down(1)">导出</el-button></div>
              </div>
              <div style="height:300px;width:100%;">
                <LineBarChart
                  :all-data="chart1Data"
                  :legend-data="['投诉量','环比','同比']"
                  :keymap="{
                    xData: 'statDate',
                    seriesData: ['score','momrate' ,'yoyrate', ]
                  }"
                />
              </div>
            </div>
            <div>
              <div class="flex tbox">
                <div class="cardtitle">万投比</div>
                <div v-show="chart2Data.length&&showExportBtn" style="text-align:right"><el-button type="primary" size="small" @click="down(2)">导出</el-button></div>
              </div>
              <div style="height:300px;width:100%;">

                <linebarChartsTAndTarget
                  v-if="cacheP2&&cacheP2.targetId=='623020102'"
                  :all-data="chart2Data"
                  :legend-data="['万投比','环比','同比','目标值']"
                  :keymap="{
                    xData: 'statDate',
                    seriesData: ['score','momrate' ,'yoyrate','targetNum' ]
                  }"
                />
                <LineBarChart
                  v-else
                  :all-data="chart2Data"
                  :legend-data="['万投比','环比','同比']"
                  :keymap="{
                    xData: 'statDate',
                    seriesData: ['score','momrate' ,'yoyrate']
                  }"
                />

              </div>

            </div>
          </div>
          <div v-if="echartsVisTxts.indexOf('部门投诉量情况')!=-1" class="flex" style="margin-top:10px">
            <div class="cus">
              <div class="flex tbox">
                <div class="cardtitle">部门投诉量情况</div>
                <div v-show="chart3Data.length&&showExportBtn" style="text-align:right"><el-button type="primary" size="small" @click="down(3)">导出</el-button></div>
              </div>

              <div style="height:300px;width:100%;">
                <barChart
                  v-if="!chart1Loading"
                  :all-data="chart3Data"
                  :legend-data="['投诉量']"
                  :keymap="chart3DataKeyMap"
                />
              </div>
            </div>
            <div v-if="hot5TableDataShow">
              <div class="flex tbox">
                <div class="cardtitle">TOP5热点问题</div>
                <div v-show="hot5TableData.length&&showExportBtn" style="text-align:right"><el-button type="primary" size="small" @click="down(4)">导出</el-button></div>
              </div>
              <div style="height:300px;width:100%;overflow-y:scroll">
                <Hot5Table :all-data="hot5TableData" :orgs="orgs" @rowclick="clickHot5TableRow" />
              </div>
            </div>

          </div>
          <div v-if="echartsVisTxts.indexOf('TOP5产品投诉')!=-1" class="flex" style="margin-top:10px">
            <div class="cus">
              <div class="flex tbox">
                <div class="cardtitle">TOP5产品投诉</div>
                <div v-if="chart5Data.length&&showExportBtn" style="text-align:right"><el-button type="primary" size="small" @click="down(5)">导出</el-button></div>
              </div>

              <div style="height:300px;width:100%;position:relative">
                <div :style="{width:chart5Data.length?'60%':'100%',height:'300px'}">
                  <horizontalBar

                    :all-data="chart5Data"
                    :keymap="{
                      yName:'产品名称',
                      yData: 'targetAlias',
                      seriesData: ['score']
                    }"
                  />
                </div>
                <div v-if="chart5Data.length" class="lengendbox">
                  <div class="itembox t">
                    <div>投诉量</div>
                    <div>占比</div>
                    <div>投诉量环比</div>
                  </div>
                  <div v-for="(item,idx) in chart5Data" :key="idx" class="itembox cll">
                    <div>{{ item.score }}</div>
                    <div>
                      {{ item.partRate === '0' ? (item.partRate)+' %' :item.partRate === 0 ? (item.partRate)+' %':
                        !item.partRate ? '-' : item.partRate+' %' }}

                    </div>
                    <div>
                      {{ item.momrate === '0' ? (item.momrate)+' %' :item.momrate === 0 ? (item.momrate)+' %':
                        !item.momrate ? '-' : item.momrate+' %' }}

                    </div>
                  </div>

                </div>
              </div>
            </div>

            <div>
              <div class="flex tbox">
                <div class="cardtitle">TOP5渠道投诉</div>

                <div v-if="chart6Data.length&&showExportBtn" style="text-align:right"><el-button type="primary" size="small" @click="down(6)">导出</el-button></div>
              </div>
              <div style="height:300px;width:100%;position:relative">
                <div :style="{width:chart6Data.length?'60%':'100%',height:'300px'}">
                  <horizontalBar

                    :all-data="chart6Data"
                    :keymap="{
                      yName:'渠道名称',
                      yData: 'targetAlias',
                      seriesData: ['score']
                    }"
                  />
                </div>
                <div v-if="chart6Data.length" class="lengendbox">
                  <div class="itembox t">
                    <div>投诉量</div>
                    <div>占比</div>
                    <div>投诉量环比</div>
                  </div>
                  <div v-for="(item,idx) in chart6Data" :key="idx" class="itembox cll">
                    <div>{{ item.score }}</div>
                    <div>
                      {{ item.partRate === '0' ? (item.partRate)+' %' :item.partRate === 0 ? (item.partRate)+' %':
                        !item.partRate ? '-' : item.partRate+' %' }}

                    </div>
                    <div>
                      {{ item.momrate === '0' ? (item.momrate)+' %' :item.momrate === 0 ? (item.momrate)+' %':
                        !item.momrate ? '-' : item.momrate+' %' }}

                    </div>
                  </div>

                </div>
              </div>
            </div>

          </div>
        </div>

      </div>
    </div>
  </div>
</template>
<script>
import NumberTitle from '@/views/bj_proatal_web/components/common/numberTitle'

import AreaSelect from './areaSelect'
import Hot5Table from './hot5Table'
import horizontalBar from './horizontalBar'
import LineBarChart from './linebarChartsT'
import barChart from './barChartsT'
import linebarChartsTAndTarget from './linebarChartsTAndTarget'
import DateChoose from '@/views/bj_proatal_web/components/common/DateChooseT.vue'
import { JKy3pW7f, dTB0sYq, HFtX8C7U, Yftf91by, fstzmS0, exportHomeBroadbandTotalComplaint, exportAppartAndCityComplaint, exportTopQuestions, LfdCw8rS } from '@/api/complainPrewarining/index.js'
import timetool from './timehandler'
import tool from '@/views/bj_proatal_web/utils/utils'
import AreaPicker from 'bj_src/nx-components/area-picker/AreaPicker.vue'
export default {
  components: {
    NumberTitle,
    linebarChartsTAndTarget,
    DateChoose,
    LineBarChart,
    barChart,
    AreaSelect,
    Hot5Table,
    horizontalBar,
    AreaPicker
  },
  inject: ['showExportBtn','mapPermission'],
  data() {
    return {
      loading: false,
      labelArr: [], // 所有指标
      childLabelArr: [],
      radioChosed: '6230201',

      cityId: this.mapPermission.cityId,
      defaultDate: '202206',
      activeTargetId: '',
      activeName: '',

      orgs: [],
      // echarts 图管理
      echartsVis: [],
      echartsVisTxts: [], // echarts中文的题目

      chart1Data: [], // card标题为 投诉总量
      chart2Data: [], // card标题为 万投比
      chart3Data: [], // card标题为 部门投诉量情况
      chart3DataKeyMap: {
        xData: 'appartName',
        seriesData: ['score']
      },
      hot5TableData: [], // card标题为 TOP5热点问题
      chart5Data: [], // card标题为 TOP5产品投诉
      chart6Data: [], // card标题为 TOP5渠道投诉

      chart1Loading: false,
      // 缓存图标的参数
      cacheP1: null,
      cacheP2: null,
      cacheP3: null,
      cacheP4: null,
      cacheP5: null,
      cacheP6: null

    }
  },
  computed:{
    tabPermission() {
      if(this.activeTargetId=='62303'|| this.activeTargetId=='62304' || this.activeTargetId=='62305') {
        return this.mapPermission.mapLevel == 1;
      }
      return true;
    },
    hot5TableDataShow(){
        if(this.activeTargetId=='62303'|| this.activeTargetId=='62304' ) {
        return false
      }
      return true;
    }
  },
  mounted() {
    // console.log(timetool.getStartTime('2022-03-12', '5'))

    this.loading = true
    const p1 = this.getLabelsAll()
    const p2 = this.getDefaultDate()

    Promise.all([p1, p2]).then(values => {
      const res1 = values[0]
      const res2 = values[1]
      if (res1.code == 200) {
        if (Array.isArray(res1.data) && res1.data.length) {
          this.labelArr = res1.data[0][0].children

          this.labelArr.forEach((i, idx) => {
            if (i.id == '62302') { // 灭灯行动治理情况
              this.childLabelArr = i.children || []
              this.radioChosed = i.children[0]?.id
            }
            if (idx == 0) { // 初始化下面echarts图形的显示
              this.echartsVis = i.children
              this.echartsVisTxts = i.children?.map(j => j.name)
            }
          })
        }

        this.activeName = this.labelArr[0].name
        this.activeTargetId = this.labelArr[0].id
      } else {
        this.labelArr = []
      }
      if (res2.code == 200) {
        this.defaultDate = res2.data.statDate || '2022-06'
        // this.defaultDate = res2.data.statDate

        this.dateType = '3'
      }

      this.init()
    }).finally(() => {
      this.loading = false
    })
  },
  methods: {
    // 初始化数据
    init() {
      const { echartsVisTxts } = this
      echartsVisTxts.forEach(x => {
        switch (x) {
          case '投诉总量':
            this.queryChart1Data()
            break
          case '万投比':
            this.queryChart2Data()
            break
          case '部门投诉量情况':
            this.queryChart3Data()
            break
          case 'TOP5热点问题':
            this.queryHot5TableData()
            break
          case 'TOP5产品投诉':
            this.queryChart5Data()
            break
          case 'TOP5渠道投诉':
            this.queryChart6Data()
            break
        }
      })
    },

    // 查询投诉总量 chart1Data
    queryChart1Data() {
      const p = this.getOptChart1Chart2('投诉总量')
      // p = {
      //   'statType': '3',
      //   'startTime': '2022-01',
      //   'endTime': '2022-03',
      //   'targetId': '6230101',
      //   'type': '1',
      //   'cityId': '640100',
      //   'appartId': ''
      // }
      console.log('投诉总量p=>', p)
      this.cacheP1 = p
      this.chart1Loading = true
      dTB0sYq(p).then(res => {
        if (res.code == 200) {
          tool.handlerMomrateAndYoyrate(res.data)
          this.chart1Data = res.data || []
        }
      }).finally(() => {
        this.chart1Loading = false
      })
    },

    // 查询万投比
    queryChart2Data(key) {
      const p = this.getOptChart1Chart2('万投比')
      this.cacheP2 = p
      console.log('万投比p2=>', p)
      dTB0sYq(p).then(res => {
        if (res.code == 200) {
          tool.handlerMomrateAndYoyrate(res.data)
          this.chart2Data = res.data || []
        }
      })
    },
    // 查询 部门投诉量情况
    queryChart3Data(key) {
      const p = this.getOptChart1Chart2('部门投诉量情况')

      p.statDate = p.endTime
      delete p.endTime
      delete p.startTime
      this.cacheP3 = p
      console.log('部门投诉量情况p=>', p)
      // p = {
      //   'targetId': '6230103',
      //   'statType': '3',
      //   'type': '3',
      //   'statDate': '2022-03',
      //   'cityId': '',
      //   'appartId': '640000'
      // }

      HFtX8C7U(p).then(res => {
        if (res.code == 200) {
          if (p.type == '3') {
            this.chart3DataKeyMap = {
              xData: 'appartName',
              seriesData: ['score']
            }
          } else if (p.type == '1' || p.type == '0') {
            this.chart3DataKeyMap = {
              xData: 'cityName',
              seriesData: ['score']
            }
          }
          tool.handlerMomrateAndYoyrate(res.data)
          this.chart3Data = res.data || []
        }
      }).finally(() => {

      })
    },

    // 查询 TOP5热点问题
    queryHot5TableData() {
      const p = this.getOptChart1Chart2('TOP5热点问题')

      p.statDate = p.endTime
      delete p.endTime
      delete p.startTime
      this.cacheP4 = p
      console.log('TOP5热点问题=>', p)

      // p = {
      //   appartId: '',
      //   cityId: '640000',
      //   statDate: '2022-06-12',
      //   statType: '1',
      //   targetId: '6230104',
      //   type: '0'
      // }

      Yftf91by(p).then(res => {
        if (res.code == 200) {
          console.log('res=>TOP5热点问题:', res)
          tool.handlerMomrateAndYoyrate(res.data)
          // res.data = [{
          //   score: 12
          // }]
          this.hot5TableData = res.data || []
        }
      })
    },
    // // 点击 热点排名
    clickHot5TableRow(row) {
      console.log('row:', row)
      //  appartId: '',
      // //   cityId: '640000',
      // //   statDate: '2022-06-12',
      // //   statType: '1',
      // //   targetId: '6230104',
      // //   type: '0'

      const { cacheP4 } = this

      const p = {
        'targetId': row.targetId,
        'statDate': cacheP4.statDate,
        'statType': cacheP4.statType,
        'cityId': cacheP4.cityId
      }

      // let p = {
      //   'targetId': '623010405',
      //   'statDate': '2022-06-12',
      //   'statType': '5',
      //   'cityId': '640000'
      // }
      fstzmS0(p).then(res => {
        if (res.code == 200) {
          tool.handlerMomrateAndYoyrate(res.data)
          this.orgs = res.data || []
        }
      })
    },
    // 查询 TOP5产品投诉
    queryChart5Data() {
      const p = this.getOptChart1Chart2('TOP5产品投诉')

      p.statDate = p.endTime
      delete p.endTime
      delete p.startTime
      this.cacheP5 = p
      console.log('TOP5产品投诉=>', p)
      // p = {
      //   'targetId': '6230104',
      //   'statDate': '2022-01',
      //   'statType': '3',
      //   'type': '0',
      //   'cityId': '640000',
      //   'appartId': ''
      // }
      Yftf91by(p).then(res => {
        if (res.code == 200) {
          console.log('res=>TOP5产品投诉:', res)
          tool.handlerMomrateAndYoyrate(res.data)
          this.chart5Data = res.data || []
        }
      })
    },
    // 查询 TOP5渠道投诉
    queryChart6Data() {
      const p = this.getOptChart1Chart2('TOP5渠道投诉')
      p.statDate = p.endTime
      delete p.endTime
      delete p.startTime
      this.cacheP6 = p
      console.log('TOP5渠道投诉=>', p)
      // p = {
      //   'targetId': '6230104',
      //   'statDate': '2022-01',
      //   'statType': '3',
      //   'type': '0',
      //   'cityId': '640000',
      //   'appartId': ''
      // }
      Yftf91by(p).then(res => {
        if (res.code == 200) {
          tool.handlerMomrateAndYoyrate(res.data)
          this.chart6Data = res.data || []
          // this.chart6Data = []
        }
      })
    },

    // 查询每个图标的targetId
    getTargetId(key) {
      const { echartsVis } = this
      // 获取targetId
      let targetId = ''
      echartsVis.forEach(i => {
        if (i.name == key) {
          targetId = i.id
        }
      })
      return targetId
    },
    // 根据指标获取投诉总量和万投比的参数
    getOptChart1Chart2(key) {
      // 全省按部门 cityId 就不传了 传appartId:640000
      //       {
      //  "statType": "3",//3月 1日 5日累计
      //  "startTime": "2022-01",
      //  "endTime": "2022-03",
      //  "targetId": "6230101",
      //  "type": "1",//type为3，表示“全省(按部门) 1代表普通
      //  "cityId": "640100",
      //  "appartId": ""//部门id
      // }
      // 地市的id
      const cityMapsId = ['640100', '640200', '640300', '640400', '640500']
      const targetId = this.getTargetId(key)
      const { dateType, defaultDate, cityId, activeTargetId } = this

      const startTime = timetool.getStartTime(defaultDate, dateType)
      let p = {}
      // 家宽报障 灭灯行动治理情况
      if (activeTargetId == '62302' || activeTargetId == '62301') {
        p = {
          statType: dateType,
          startTime,
          endTime: defaultDate,
          targetId,
          type: cityId == '640000x' ? '3' : cityId == '640000' ? '0' : cityMapsId.indexOf(cityId) != -1 ? '1' : '2', // 640000x全省按部门 type传'3'  640000代表全省 type传'0' 地市type传'1',区县传type传'2'
          cityId: cityId == '640000x' ? '640000' : cityId,
          appartId: cityId == '640000x' ? '640000' : ''
        }
      } else { // 不知情定制 两类差错投诉情况
        p = {
          statType: dateType,
          startTime,
          endTime: defaultDate,
          targetId,
          type: '3', // 640000x全省按部门
          cityId: this.mapPermission.cityId,// '640000',
          appartId:  this.mapPermission.cityId,// '640000'
        }
      }

      return p
    },

    areaChange(value) {
      const v = Array.isArray(value) ? value[value.length-1] : value;
      this.cityId = v
      this.$nextTick(() => {
        this.init()
      })
    },
    // 日期变化
    dateChange(v, dateType) {
      console.log('this.v:', v)
      console.log('dateType:', dateType)
      this.dateType = dateType == '月' ? '3' : dateType == '日' ? '1' : dateType == '日累计' ? '5' : ''
      this.defaultDate = v
      this.init()
    },
    // 查询投诉投诉总量
    // 指标变化
    handleTabClick(item) {
      const { labelArr } = this
      console.log('item.paneName:', item.paneName)
      this.activeTargetId = item.paneName
      this.activeName = item.label
      if (item.paneName == '62302') {
        this.radioChosed = '6230201'
      } else {
        this.radioChosed = ''
      }
      console.log('labelArr:', labelArr)

      labelArr.forEach(i => {
        if (item.paneName == '62302' && i.id == item.paneName) {
          const temp = this.childLabelArr[0]
          this.echartsVis = temp.children || []
          this.echartsVisTxts = temp.children?.map(j => j.name)
        }
        if (i.id == item.paneName && item.paneName != '62302') {
          this.echartsVis = i.children || []
          this.echartsVisTxts = i.children?.map(j => j.name) || []
        }

        // if (i.id == item.paneName && i.id != '62302') {
        //   this.echartsVis = i.children || []
        //   this.echartsVisTxts = i.children?.map(j => j.name) || []
        // } else if (i.id == '62302') { // 灭灯行动治理情况
        //   const temp = this.childLabelArr[0]
        //   this.echartsVis = temp.children || []
        //   this.echartsVisTxts = temp.children?.map(j => j.name)
        // }
      })

      console.log('echartsVis:', this.echartsVis)
      this.init()
    },
    handleRadioTabClick(item) {
      this.childLabelArr.forEach(i => {
        if (i.id == item) {
          this.echartsVis = i.children || []
          this.echartsVisTxts = i.children?.map(j => j.name)
        }
      })
      this.radioChosed = item
      this.init()
    },

    // 下载
    down(sort) {
      // 投诉总量和万投比的下载
    // exportHomeBroadbandTotalComplaint

      const arr = ['投诉总量', '万投比', '部门投诉量情况', 'TOP5热点问题', 'TOP5产品投诉', 'TOP5渠道投诉']
      const fileNameAppend = arr[sort - 1]

      // cacheP6
      const p = this[`cacheP${sort}`]
      this.$confirm('是否确认导出数据项?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if (sort == 1 || sort == 2) {
            return exportHomeBroadbandTotalComplaint(p)
          }
          if (sort == 3) {
            return exportAppartAndCityComplaint(p)
          }
          if (sort == 4 || sort == 5 || sort == 6) {
            return exportTopQuestions(p)
          }

          // return exportComplaintSatisfaction(p)
        })
        .then((response) => {
          // window.open(response);
          // const currenTarget = this.lookForAll(this.allTarget).filter(
          //   (item) => item.targetId === targetId
          // )[0]

          // 兼容ie
          if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(
              response,
              this.activeName + fileNameAppend + '.xls'
            )
            return false
          }

          const url = URL.createObjectURL(response)
          const aLink = document.createElement('a')
          aLink.href = url
          aLink.setAttribute('download', this.activeName + fileNameAppend + '.xls')
          document.body.appendChild(aLink)
          aLink.click()
          document.body.removeChild(aLink)
        })
    },

    // 获取指标
    getLabelsAll() {
      // 获取指标
      return JKy3pW7f()
    },
    // 获取最新有数据的月份
    getDefaultDate() {
      // const res = {
      //   code: 200,
      //   data: { statDate: '2022-06' }
      // }
      // return Promise.resolve(res)
      return LfdCw8rS({})
    }
  }
}
</script>
<style lang="scss" scoped>
.lengendbox{
  position: absolute;
  width:40%;
  height:100%;
  right:0;
  top:0;

  .itembox{
    display:flex;
    text-align: left;
    >div{
      width:33.3%;
    }
    &.cll{
      height:40px;
      line-height: 42px;
      font-size: 14px;
      color:#606266;

    }
    &.t{
      color:#909399;
      font-size: 12px;
      padding-top:30px;
      padding-bottom: 16px;
    }
  }
}
.imp{
     background-color: rgba(242, 242, 242, 1);
     padding:30px 40px 40px 40px;
     min-height: 100vh;

     /deep/.el-tabs__nav{
       position: relative;
       left: 20px;
     }
     /deep/.el-radio__input{
       display:none !important;
     }
}

.con{
    position: relative;

}
.timecon{
    position: absolute;
    right:0;
    top:-65px;
}
.cardtitle{
    font-family: "PingFangSC-Semibold", "PingFang SC Semibold", "PingFang SC", sans-serif;
    font-weight: bold;
    font-size: 16px;
    line-height: 32px;
}
// echarts 图
.box{
  background:#fff;
  margin-top:20px;
  padding-top:5px;
}
.flex{
  display:flex;
  &.tbox{padding:0px 30px 0px 20px}
  >div{
    // width: 50%;
    flex:1;
    &.cus{
      border-right:1px dotted #ddd;
    }
  }
}
.flexr{
  display: flex;
  flex-direction:row-reverse;
}
/deep/.el-tabs__nav-wrap::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background-color: #dfe4ed;
    z-index: 1;
}
/deep/.el-dialog__body{
  padding:0!important;
}
</style>
