<template>
  <div v-loading="queryLoading" class="complain-warning-box">
    <div class="complain-title">{{this.rulerId?'编辑':'新增'}}配置规则</div>
    <!-- 步骤进度 -->
    <step-progress style="width: 75%;" :current="step" />
    <!-- 步骤表单 -->
    <keep-alive>
      <component
        :is="`Step-${step}`"
        ref="comp"
        :form="form"
        :success="isSuccess"
        :tree-level="treeLevel"
        @canGetDetail="getDetail"
        :receivePhoneObj="receivePhoneObj"
        :receivePhoneArray="receivePhoneArray"
        :deptInfoArr="deptInfoArr"
      />
    </keep-alive>

    <div v-show="!(isSuccess === true)" class="step-btns">
      <el-button type="default" size="small" :disabled="loading" @click="$emit('switch','list')">取消</el-button>
      <el-button v-show="step > 1" type="primary" size="small" :disabled="loading" @click="changeStep(-1)">上一步</el-button>
      <el-button v-show=" step < steps.length" type="primary" size="small" :loading="loading" @click="changeStep(1)">
        {{ step === steps.length-1 ? '完成' : '下一步' }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { addWarningRule, getWarningDetail, updateWarningRule,getNickNameAndPhoneList ,getSysDeptAndUserInfos} from '@/api/complain-warning'
import date from '../../../lib/date.js'
import StepProgress from './steps/StepProgress.vue'
import Step1 from './steps/Step1.vue'
import Step2 from './steps/Step2.vue'
import Step3 from './steps/Step3.vue'
import Step4 from './steps/Step4.vue'

export default {
  name: 'ComplainWarningStep',
  components: {
    StepProgress,
    Step1,
    Step2,
    Step3,
    Step4
  },
  props: ['rulerId'],
  data() {
    return {
      step: 1,
      steps: [
        { step: 1, label: '配置基本信息' },
        { step: 2, label: '选择预警指标' },
        { step: 3, label: '预警推送配置' },
        { step: 4, label: '完成' }
      ],
      form: {
        rulerId: '', // 规则id
        enableOrNot: 1, // 启用 0 否 1 是，新增默认启用
        businessScenario: '', // 业务场景
        complaintWarningType: '', // 预警类型
        indexName: '', // 指标名称
        processingWorkGroup: '', // 工作组
        localProvince: '640000', // 归属省份 默认全省 宁夏 640000
        localCity: '640000', // 归属地市 默认全省 宁夏 640000
        indexRelationShip: '', // 指标关系
        statisticalCycle: '日', // 统计周期
        loopWarn: 1, // 循环触发
        warningLevel: '', // 预警级别
        notificationChannel: ['短信'], // 通知渠道
        receiveTime: [new Date('2022/1/1 00:00:00'), new Date('2022/1/1 23:59:59')], // 接受时间 receive_start_time receive_end_time 拼组
        message: '', // 短线内容
        receivePhone: '', // 提醒号码
        sysWarningParams: [],
        //* *************/
        // 取数口径 第一步选的表
        correspondenceTable: 'GCMX',
        // 选的字段
        calibers: [],
        // 指标类型
        indexType: '10',
        // 阀值
        referenceValue: '',
        //* ************ */
        
      },
      receivePhoneArray:[],
      receivePhoneObj:{},
      treeLevel: {},
      queryLoading: false, // 查询loading
      loading: false,
      isSuccess: false,
      deptInfoArr:[],
    }
  },
  created(){
    getNickNameAndPhoneList().then(res=>{
      let {code,data} = res;
      if(code==200 && data) {
        this.receivePhoneObj = data;//回写
        this.receivePhoneArray = Object.keys(data).map(i=>{
          let o = {label:`${i}(${data[i]})`,value:data[i]}
          return o
        })

      }
    })
    getSysDeptAndUserInfos().then(res=>{
      let {code,data}=res;
      if(code==200&&Array.isArray(data)){
        this.deptInfoArr=this.handleDeptInfo(data)
      }else{
         this.deptInfoArr
      }
    })
  },
  mounted() {
    if (this.rulerId) {
      this.getWarningDetail()
    }
  },
  methods: {
    // 处理数据
    handleDeptInfo(arr){
      if(Array.isArray(arr)&&arr.length){
        arr.forEach(i=>{
          if(i.childDeptAndUserInfoVOS&&i.childDeptAndUserInfoVOS.length==0){
            delete i.childDeptAndUserInfoVOS
          }else if(i.childDeptAndUserInfoVOS && i.childDeptAndUserInfoVOS.length){
            this.handleDeptInfo(i.childDeptAndUserInfoVOS)
          }
        })
      }
      return arr
    },
    getDetail() {

    },
    async submit() {
      const {
        notificationChannel,
        receiveTime: [receiveStartTime, receiveEndTime],
        ...param
      } = this.form
      try {
        this.loading = true
        const func = this.form.rulerId ? updateWarningRule : addWarningRule
        const p = {
          notificationChannel: notificationChannel.join(','),
          receiveStartTime: date.formatDate(receiveStartTime, 'hh:mm:ss'),
          receiveEndTime: date.formatDate(receiveEndTime, 'hh:mm:ss'),
          ...param
        }
        console.log('p==:', p)
        // calibers 处理
        //         [{
        //  firstName:"GCMX",
        //  secondName:"val_21",
        //  thirdName:"银川分公司"
        //  }]

        // if (p.calibers && Array.isArray(p.calibers) && p.calibers.length) {
        //   const temp = p.calibers.map((i, idx) => {
        //     const obj = {
        //       firstName: p.correspondenceTable,
        //       secondName: i[0],
        //       thirdName: i[1]

        //     }
        //     return obj
        //   })
        //   p.calibers = temp
        // }
        console.log('处理过的参数=', p)

        const { code, msg } = await func(p)
        this.loading = false
        return code === 200 ? true : msg
      } catch (e) {
        this.loading = false
        return e || false
      }
    },
    async changeStep(step) {
      // step为1，点击上一步
      if (step < 0 && this.step === 1) return
      // step为最后一步，点击上一步
      if (step > 0 && this.step === this.steps.length) return
      // 点击下一步有表单校验函数
      if (step > 0 && this.$refs.comp.validate) {
        const valid = await this.$refs.comp.validate()
        if (valid) {
          // 第二步有参数明细的返回
          if (typeof valid === 'object') {
            Object.assign(this.form, valid)
          }
          // 倒数第二步
          if (this.step === this.steps.length - 1) {
            const res = await this.submit()
            // 判断是否成功
            this.isSuccess = res
            this.step += step
            if (res === true) {
              this.$nextTick(() => {
                // 成功3s后切换成列表页面
                setTimeout(() => { this.$emit('switch', 'list') }, 2000)
              })
            }
          } else {
            this.step += step
          }
        }
      } else {
        this.step += step
      }
    },

    async getWarningDetail() {
      try {
        this.queryLoading = true
        const res = await getWarningDetail({ rulerId: this.rulerId })
        if (res.data) {
          const {
            rulerId,
            enableOrNot,
            businessScenario,
            complaintWarningType,
            indexName,
            processingWorkGroup,

            localProvince, // 归属省份 默认全省 宁夏 640000
            localCity, // 归属地市 默认全省 宁夏 640000
            indexRelationShip, // 指标关系
            statisticalCycle, // 统计周期
            loopWarn, // 循环触发
            warningLevel, // 预警级别
            notificationChannel, // 通知渠道
            // 接受时间 receive_start_time receive_end_time 拼组
            receiveStartTime,
            receiveEndTime,
            message, // 短线内容
            receivePhone, // 提醒号码
            sysWarningParams,
            //* *************/
            // 取数口径 第一步选的表
            correspondenceTable,
            // 选的字段
            calibers,
            // 指标类型
            indexType,
            // 阀值
            referenceValue
            //* ************ */

          } = res.data

          for (const prop in this.form) {
            // 测试
            // res.data.correspondenceTable = 'GCMX'
            // res.data.indexType = '10'

            if (res.data.hasOwnProperty(prop) && this.form.hasOwnProperty(prop)) {
              // 处理receiveTime
              if (prop == 'receiveTime') {
                this.$set(this.form, prop, [
                  new Date('2022/1/1 ' + receiveStartTime),
                  new Date('2022/1/1 ' + receiveEndTime)
                ])
              } else if (prop == 'notificationChannel') {
                this.$set(this.form, prop, res.data[prop].split(','))
              } else {
                this.$set(this.form, prop, res.data[prop])
              }
            }
          }
          console.log(this.form)

          // const { sysWarningVO, sysWarningDetailsVO, sysWarningDetailsLevelVO } = res.data
          // const levelObj = {}
          // sysWarningDetailsLevelVO.forEach((item) => {
          //   if (!levelObj[item.detailsId]) levelObj[item.detailsId] = []
          //   levelObj[item.detailsId].push(item)
          // })

          // for (const prop in this.form) {
          //   if ((sysWarningVO.hasOwnProperty(prop))) {
          //     if (prop == 'notificationChannel') { // 通知渠道
          //       this.$set(this.form, prop, sysWarningVO[prop].split(','))
          //     } else {
          //       this.$set(this.form, prop, sysWarningVO[prop])
          //     }
          //   } else if (prop == 'receiveTime') { // 接受时间
          //     this.$set(this.form, prop, [
          //       new Date('2022/1/1 ' + sysWarningVO.receiveStartTime),
          //       new Date('2022/1/1 ' + sysWarningVO.receiveEndTime)
          //     ])
          //   }
          // }

          // this.form.sysWarningParams = sysWarningDetailsVO.map((item) => {
          //   const { rulerId, detailsId, correspondenceTable, fetchingId, fetchingName, levelAndName, indexType, referenceValue, detailsType, warningProportion, level } = item
          //   const details = { rulerId, detailsId, correspondenceTable, indexType, referenceValue, warningProportion }
          //   // 有目录树选择
          //   if (detailsType === '1') {
          //     // 获取层级下拉框的层级对应的id
          //     const [{ levelNum }] = levelObj[detailsId]
          //     return { ...details, fetchingId: Number(levelNum), fetchingName: '', levelAndName, indicatorTree: '' }
          //   } else {
          //     return { ...details, fetchingId, fetchingName, level }
          //   }
          // })
          // // 树level赋值
          // this.treeLevel = levelObj
        }
        this.queryLoading = false
      } catch (e) {
        this.$message.error(e || '获取规则信息失败')
        this.$set(this.form, 'rulerId', this.rulerId)
        this.queryLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.complain-warning-box {
  padding: 15px 20px;
  background: #fff;
  min-height: 60vh;
}
.complain-title{
  font-size: 20px;
  color: #262626;
  font-weight: 500;
  margin-bottom: 20px;
}
.step-btns{
  text-align: center;
  padding-bottom: 20px;
}
</style>
