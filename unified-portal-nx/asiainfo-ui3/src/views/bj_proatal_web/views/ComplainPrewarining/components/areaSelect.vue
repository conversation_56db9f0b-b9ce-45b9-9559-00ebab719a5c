<template>
  <div class="subflexbox">
    <div class="select-box">
      <div style="margin-right: 10px" class="changeArea">
        <span class="labelcss">选择地市：</span>
        <el-cascader
          ref="time"
          v-model="EndAreaBuUser"
          :placeholder="'请选择地市'"
          :options="endToEndArea"
          :props="props"
          size="small"
          style="width: 200px"
          @change="inputChange"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { AreaByUser, getGridData } from '@/api/mobilePhoneTariff'

export default {
  data() {
    return {
      EndAreaBuUser: ['640000'],
      // 选择省份选项
      endToEndArea: [],
      // 渠道
      grid: [],
      props: {
        label: 'name',
        value: 'id',
        checkStrictly: true
      },
      cityId: ''
    }
  },
  watch: {
    level() {
      console.log(this.level)
    }
  },
  mounted() {
    this.getAreaByUser()
  },
  methods: {
    // 省份选择回调
    inputChange(el) {
      console.log(this.$refs.time.getCheckedNodes()[0])
      if (el) {
        this.cityId = el.slice(-1).toString()
        this.$emit(
          'sendCityId',
          this.cityId,
          this.$refs.time.getCheckedNodes()[0].level
        )
      }
      if (this.$refs.time.getCheckedNodes()[0].level == 3) {
        console.log(this.$refs.time.getCheckedNodes()[0].value)
        this.gridCityId = this.$refs.time.getCheckedNodes()[0].value
        console.log(1111)
      }
    },
    // 获取地市数据接口
    async getAreaByUser() {
      const { data, code } = await AreaByUser({
        restSerialNo: '6MujCDCs'
      })
      if (code == 200) {
        console.log('res=>', data)
        const temparr = data[0]

        const temp = {

          'cityName': '全省(按部门)',
          'level': 1,
          'name': '全省(按部门)',
          'pid': '0',
          'id': '640000x'

        }
        temparr.unshift(temp)
        console.log('temparr=>', temparr)

        this.endToEndArea = temparr
      }
    }
  }
}
</script>
<style lang="scss" scoped>
 .el-cascader {
        /deep/.el-icon-arrow-down:before {
          content: "\E6E1";
        }
       /deep/.el-icon-arrow-down{
         transform: rotate(180deg);
       }
       /deep/.is-reverse.el-icon-arrow-down{
         transform: rotate(0deg);
       }

  }
.select-box {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;

  .changeArea {
    display: flex;
    .labelcss {
      width: 85px;
      line-height: 36px;
    }
  }
}
</style>
