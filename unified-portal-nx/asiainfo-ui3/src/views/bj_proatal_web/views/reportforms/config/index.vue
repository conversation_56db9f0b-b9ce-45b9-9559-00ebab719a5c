<template>
  <div class="report-config-container">
    <h2>报表配置管理</h2>

    <!-- 配置表单 -->
    <el-form :model="configForm" label-width="120px" class="config-form">
      <el-form-item label="报表名称">
        <el-input v-model="configForm.name" placeholder="请输入报表名称"></el-input>
      </el-form-item>

      <el-form-item label="报表描述">
        <el-input type="textarea" v-model="configForm.description" placeholder="请输入报表描述"></el-input>
      </el-form-item>

      <el-form-item label="数据源表">
        <el-input v-model="configForm.tableName" placeholder="请输入数据源表名"></el-input>
      </el-form-item>

      <el-form-item label="查询条件">
        <div class="condition-list">
          <div v-for="(condition, index) in configForm.conditions" :key="index" class="condition-item">
            <el-row :gutter="10">
              <el-col :span="3">
                <el-input v-model="condition.field" placeholder="字段名"></el-input>
              </el-col>
              <el-col :span="3">
                <el-input v-model="condition.displayName" placeholder="中文名称"></el-input>
              </el-col>
              <el-col :span="3">
                <el-select v-model="condition.controlType" placeholder="控件类型" @change="onControlTypeChange(condition, index)">
                  <el-option label="文本框" value="input"></el-option>
                  <el-option label="下拉框" value="select"></el-option>
                  <el-option label="日期选择" value="date"></el-option>
                  <el-option label="日期范围" value="daterange"></el-option>
                  <el-option label="月份选择" value="month"></el-option>
                </el-select>
              </el-col>
              <el-col :span="3">
                <el-select v-model="condition.operator" placeholder="操作符">
                  <el-option label="等于" value="EQ"></el-option>
                  <el-option label="包含" value="LIKE"></el-option>
                  <el-option label="不等于" value="NE"></el-option>
                  <el-option label="大于等于" value="GE"></el-option>
                  <el-option label="小于等于" value="LE"></el-option>
                  <el-option label="为空" value="IS_NULL"></el-option>
                  <el-option label="不为空" value="IS_NOT_NULL"></el-option>
                  <el-option label="在列表中" value="IN"></el-option>
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-input
                  v-if="condition.controlType !== 'select'"
                  v-model="condition.value"
                  placeholder="默认值">
                </el-input>
                <el-input
                  v-else
                  v-model="condition.options"
                  placeholder="选项值(用逗号分隔)">
                </el-input>
              </el-col>
              <el-col :span="3">
                <el-checkbox v-model="condition.required">必填</el-checkbox>
              </el-col>
              <el-col :span="2">
                <el-button type="danger" size="mini" @click="removeCondition(index)">删除</el-button>
              </el-col>
            </el-row>
            <!-- 下拉框选项配置 -->
            <el-row v-if="condition.controlType === 'select'" :gutter="10" style="margin-top: 10px;">
              <el-col :span="24">
                <div class="select-options-config">
                  <label style="font-size: 12px; color: #606266;">下拉选项配置：</label>
                  <div class="options-list">
                    <div v-for="(option, optIndex) in condition.selectOptions" :key="optIndex" class="option-item">
                      <el-input
                        v-model="option.label"
                        placeholder="显示文本"
                        size="mini"
                        style="width: 150px; margin-right: 10px;">
                      </el-input>
                      <el-input
                        v-model="option.value"
                        placeholder="选项值"
                        size="mini"
                        style="width: 150px; margin-right: 10px;">
                      </el-input>
                      <el-button size="mini" type="danger" @click="removeSelectOption(condition, optIndex)">删除</el-button>
                    </div>
                    <el-button size="mini" type="primary" @click="addSelectOption(condition)">添加选项</el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
          <el-button type="primary" @click="addCondition">添加条件</el-button>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="saveConfig">{{ editingId ? '更新配置' : '保存配置' }}</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 配置列表 -->
    <h3>现有配置</h3>
    <el-table :data="configList" style="width: 100%">
      <el-table-column prop="id" label="报表编码"></el-table-column>
      <el-table-column prop="name" label="报表名称"></el-table-column>
      <el-table-column prop="description" label="描述"></el-table-column>
      <el-table-column prop="tableName" label="数据表"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="mini" @click="editConfig(scope.row)">编辑</el-button>
          <el-button size="mini" @click="openFieldConfig(scope.row)">字段配置</el-button>
          <el-button size="mini" type="danger" @click="deleteConfig(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 字段配置对话框 -->
    <el-dialog title="字段配置" :visible.sync="fieldDialogVisible" width="70%">
      <el-alert
        v-if="configForm.tableName"
        :title="'当前数据表: ' + configForm.tableName"
        type="info"
        show-icon
        :closable="false"
        style="margin-bottom: 20px"
      >
      </el-alert>

      <el-transfer
        v-model="selectedFields"
        :data="tableFields"
        :titles="['可用字段', '已选字段']"
        :button-texts="['移除', '添加']"
        filterable
        :filter-method="filterMethod"
        filter-placeholder="请输入字段名"
        @change="handleFieldSelectionChange">
        <span slot-scope="{ option }">{{ option.displayName }} ({{ option.name }})</span>
      </el-transfer>

      <!-- 已选字段详细配置 -->
      <div v-if="fieldDetails.length > 0" style="margin-top: 20px;">
        <h4>字段中文名称设置</h4>
        <el-table :data="fieldDetails" style="width: 100%" border>
          <el-table-column prop="name" label="字段名" width="180"></el-table-column>
          <el-table-column prop="dataType" label="数据类型" width="120"></el-table-column>
          <el-table-column label="中文名称">
            <template slot-scope="scope">
              <el-input v-model="scope.row.displayName" placeholder="请输入中文名称"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="敏感字段" width="100">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.sensitive" true-label="1" false-label="0"></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="fieldDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveFieldsConfig">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { saveReportConfig, getReportConfigList, updateReportConfig, deleteReportConfig, getTableFields, saveReportFields, getReportFields } from '@/api/reportforms/config.js'

export default {
  name: 'ReportConfig',
  data() {
    return {
      configForm: {
        name: '',
        description: '',
        tableName: '',
        conditions: []
      },
      configList: [],
      editingId: null,
      tableFields: [], // 表字段列表
      selectedFields: [], // 选中的字段
      fieldDetails: [], // 字段详细信息，包含中文名称
      savedFieldDetails: [], // 已保存的字段详情，用于保留用户编辑的中文名称
      fieldDialogVisible: false, // 字段配置对话框可见性
      currentConfigId: null // 当前配置ID
    }
  },
  methods: {
    addCondition() {
      this.configForm.conditions.push({
        field: '',
        displayName: '',
        controlType: 'input', // 默认为文本框
        operator: 'EQ',
        value: '',
        options: '', // 用于下拉框的选项值
        selectOptions: [], // 用于下拉框的选项配置
        required: false // 是否必填
      })
    },

    // 添加下拉框选项
    addSelectOption(condition) {
      if (!condition.selectOptions) {
        this.$set(condition, 'selectOptions', [])
      }
      condition.selectOptions.push({
        label: '',
        value: ''
      })
    },

    // 删除下拉框选项
    removeSelectOption(condition, index) {
      condition.selectOptions.splice(index, 1)
    },

    // 控件类型变化时的处理
    onControlTypeChange(condition, index) {
      // 清空旧的配置
      condition.value = ''
      condition.options = ''

      // 根据控件类型初始化选项
      if (condition.controlType === 'select') {
        if (!condition.selectOptions) {
          this.$set(condition, 'selectOptions', [])
        }
      } else {
        // 非下拉框类型，清空下拉框选项
        condition.selectOptions = []
      }
    },

    // 字段过滤方法
    filterMethod(query, item) {
      return item.name.toLowerCase().indexOf(query.toLowerCase()) > -1
    },

    // 处理字段选择变化
    handleFieldSelectionChange(value, direction, movedKeys) {
      if (direction === 'right') {
        // 添加字段到fieldDetails
        movedKeys.forEach(key => {
          const field = this.tableFields.find(f => f.key === key);
          if (field && !this.fieldDetails.some(fd => fd.name === field.name)) {
            // 优先使用已保存的字段详情（包含用户设置的中文名称和敏感字段配置）
            const savedField = this.savedFieldDetails && this.savedFieldDetails.find(f => f.name === field.name);
            this.fieldDetails.push({
              name: field.name,
              displayName: savedField ? savedField.displayName : (field.displayName || field.name),
              dataType: savedField ? savedField.dataType : (field.dataType || 'string'),
              sensitive: savedField ? savedField.sensitive : '0' // 默认为非敏感字段
            });
          }
        });
      } else if (direction === 'left') {
        // 从fieldDetails中移除字段
        this.fieldDetails = this.fieldDetails.filter(field => !movedKeys.includes(field.name));
      }
    },
    removeCondition(index) {
      this.configForm.conditions.splice(index, 1)
    },
    saveConfig() {
      // 保存配置逻辑
      if (!this.configForm.name) {
        this.$message.error('请输入报表名称')
        return
      }

      if (!this.configForm.tableName) {
        this.$message.error('请输入数据源表名')
        return
      }

      // 显示loading
      const loading = this.$loading({
        lock: true,
        text: this.editingId ? '更新中...' : '保存中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 确保所有条件都有displayName字段
      const conditionsWithDisplayName = this.configForm.conditions.map(condition => ({
        ...condition,
        displayName: condition.displayName || ''
      }));

      // 将conditions数组转换为JSON字符串，以匹配后端期望的数据结构
      const configData = {
        ...this.configForm,
        conditions: JSON.stringify(conditionsWithDisplayName),
        id: this.editingId
      }

      // 如果是编辑状态，则更新配置，否则新增配置
      const savePromise = this.editingId
        ? updateReportConfig(this.editingId, configData)
        : saveReportConfig(configData)

      savePromise
        .then(response => {
          const message = this.editingId ? '配置更新成功' : '配置保存成功'
          this.$message.success(message)

          // 保存成功后，获取配置ID并打开字段配置对话框
          const configId = this.editingId || response.data.id
          this.currentConfigId = configId
          this.configForm.tableName = this.configForm.tableName

          // 显示字段配置loading
          const fieldLoading = this.$loading({
            lock: true,
            text: '加载中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          // 加载表字段和配置字段
          Promise.all([
            this.loadTableFields(),
            this.loadConfigFields(configId)
          ]).then(() => {
            // 初始化字段详情
            this.initializeFieldDetails()
            this.fieldDialogVisible = true
            fieldLoading.close()
          }).catch(() => {
            fieldLoading.close()
            this.fieldDialogVisible = true
          })

          this.resetForm()
          this.loadConfigList()
        })
        .catch(error => {
          const message = this.editingId ? '配置更新失败: ' : '配置保存失败: '
          this.$message.error(message + error.message)
        })
        .finally(() => {
          // 关闭loading
          loading.close()
        })
    },
    resetForm() {
      this.configForm = {
        name: '',
        description: '',
        tableName: '',
        conditions: []
      }
      this.editingId = null
    },
    editConfig(row) {
      // 编辑配置逻辑
      this.configForm = { ...row }
      // 将conditions字符串转换回数组
      if (this.configForm.conditions && typeof this.configForm.conditions === 'string') {
        try {
          this.configForm.conditions = JSON.parse(this.configForm.conditions)
        } catch (e) {
          console.error('解析conditions失败:', e)
          this.configForm.conditions = []
        }
      }

      // 确保每个条件都有所有必要的字段
      if (this.configForm.conditions && Array.isArray(this.configForm.conditions)) {
        this.configForm.conditions = this.configForm.conditions.map(condition => ({
          ...condition,
          displayName: condition.displayName || '',
          controlType: condition.controlType || 'input',
          options: condition.options || '',
          selectOptions: condition.selectOptions || [],
          required: condition.required || false
        }))
      }

      this.editingId = row.id
    },
    deleteConfig(id) {
      // 删除配置逻辑
      this.$confirm('此操作将永久删除该配置, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 显示loading
        const loading = this.$loading({
          lock: true,
          text: '删除中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        deleteReportConfig(id)
          .then(response => {
            this.$message.success('配置删除成功')
            this.loadConfigList()
          })
          .catch(error => {
            this.$message.error('配置删除失败: ' + error.message)
          })
          .finally(() => {
            // 关闭loading
            loading.close()
          })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    loadConfigList() {
      // 显示loading
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 加载配置列表
      getReportConfigList({})
        .then(response => {
          if (response.code === 200) {
            this.configList = response.data || []
            // 处理列表中每个配置的conditions字段
            this.configList.forEach(config => {
              if (config.conditions && typeof config.conditions === 'string') {
                try {
                  config.conditions = JSON.parse(config.conditions)
                } catch (e) {
                  console.error('解析conditions失败:', e)
                  config.conditions = []
                }
              }

              // 确保每个条件都有displayName字段
              if (config.conditions && Array.isArray(config.conditions)) {
                config.conditions = config.conditions.map(condition => ({
                  ...condition,
                  displayName: condition.displayName || ''
                }))
              }
            })
          }
        })
        .catch(error => {
          this.$message.error('加载配置列表失败: ' + error.message)
        })
        .finally(() => {
          // 关闭loading
          loading.close()
        })
    },

    // 加载表字段列表
    loadTableFields() {
      if (!this.configForm.tableName) {
        this.tableFields = []
        return Promise.resolve()
      }

      return getTableFields(this.configForm.tableName)
        .then(response => {
          if (response.code === 200) {
            // 转换字段格式以适应el-transfer组件
            this.tableFields = (response.data || []).map(field => ({
              key: field.name,
              label: field.name,
              name: field.name,
              displayName: field.displayName || field.name,
              dataType: field.dataType || 'string'
            }))
          }
        })
        .catch(error => {
          console.warn('加载表字段失败:', error.message)
          this.$message.warning('加载表字段失败')
          // 即使失败也返回resolved promise，以确保Promise.all继续执行
          return Promise.resolve()
        })
    },

    // 加载配置字段
    loadConfigFields(configId) {
      return getReportFields(configId)
        .then(response => {
          if (response.code === 200) {
            const fieldsData = response.data && response.data.fields ? response.data.fields : []
            this.selectedFields = fieldsData.map(field => field.name)
            // 保存已配置的字段详情，包含用户设置的中文名称和敏感字段配置
            this.savedFieldDetails = fieldsData.map(field => ({
              name: field.name,
              displayName: field.displayName || field.name,
              dataType: field.dataType || 'string',
              sensitive: field.sensitive || '0'
            }))
          }
        })
        .catch(error => {
          this.$message.error('加载配置字段失败: ' + error.message)
          // 即使失败也返回resolved promise，以确保Promise.all继续执行
          return Promise.resolve()
        })
    },

    // 保存字段配置
    saveFieldsConfig() {
      if (!this.currentConfigId) {
        this.$message.error('配置ID不存在')
        return
      }

      // 显示loading
      const loading = this.$loading({
        lock: true,
        text: '保存中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 使用fieldDetails中的数据，包含用户编辑的中文名称和敏感字段配置
      const fields = this.fieldDetails.map(field => ({
        name: field.name,
        displayName: field.displayName,
        dataType: field.dataType,
        sensitive: field.sensitive
      }))

      saveReportFields(this.currentConfigId, fields)
        .then(response => {
          this.$message.success('字段配置保存成功')
          this.fieldDialogVisible = false
        })
        .catch(error => {
          this.$message.error('字段配置保存失败: ' + error.message)
        })
        .finally(() => {
          // 关闭loading
          loading.close()
        })

    },

    // 打开字段配置对话框
    openFieldConfig(row) {
      this.currentConfigId = row.id
      this.configForm.tableName = row.tableName
      // 清空之前的字段详情
      this.fieldDetails = []
      this.savedFieldDetails = []
      // 显示loading
      const loading = this.$loading({
        lock: true,
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 加载表字段和配置字段
      Promise.all([
        this.loadTableFields(),
        this.loadConfigFields(row.id)
      ]).then(() => {
        // 不再默认选中所有字段，只选中已配置的字段
        // this.selectedFields已经在loadConfigFields中设置
        // 初始化字段详情
        this.initializeFieldDetails()
        this.fieldDialogVisible = true
        loading.close()
      }).catch(() => {
        loading.close()
        this.fieldDialogVisible = true
      })
    },

    // 初始化字段详情
    initializeFieldDetails() {
      this.fieldDetails = this.selectedFields.map(fieldName => {
        // 优先使用已保存的字段详情（包含用户设置的中文名称和敏感字段配置）
        const savedField = this.savedFieldDetails && this.savedFieldDetails.find(f => f.name === fieldName);
        if (savedField) {
          return {
            name: savedField.name,
            displayName: savedField.displayName,
            dataType: savedField.dataType,
            sensitive: savedField.sensitive || '0'
          };
        }

        // 如果没有保存的详情，则使用表字段信息
        const field = this.tableFields.find(f => f.key === fieldName);
        return {
          name: field ? field.name : fieldName,
          displayName: field ? (field.displayName || field.name) : fieldName,
          dataType: field ? (field.dataType || 'string') : 'string',
          sensitive: '0' // 默认为非敏感字段
        };
      });
    }
  },
  mounted() {
    // 初始化配置列表
    this.loadConfigList()
  }
}
</script>

<style scoped>
.report-config-container {
  padding: 20px;
}

.config-form {
  margin-bottom: 30px;
  border: 1px solid #ebeef5;
  padding: 20px;
  border-radius: 4px;
}

.condition-list {
  width: 100%;
}

.condition-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.select-options-config {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.options-list {
  margin-top: 10px;
}

.option-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}
</style>
