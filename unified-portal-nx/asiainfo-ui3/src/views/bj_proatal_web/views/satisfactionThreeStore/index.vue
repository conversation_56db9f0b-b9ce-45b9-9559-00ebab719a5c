<template>
  <div>
    <Banner title="满意度三库" desc="满意度三库" />
    <div class="santis-three-box">
      <el-radio-group v-model="layoutType" style="padding-left:50px;position:absolute;top:-32px;left:0;">
        <el-radio-button label="layoutType1">满分客户库</el-radio-button>
        <el-radio-button label="layoutType2">中立客户库</el-radio-button>
        <el-radio-button label="layoutType3">不满客户库</el-radio-button>
        <el-radio-button label="layoutType4">短信调研库</el-radio-button>
        <el-radio-button label="layoutType5">集团cati不满-省内测评客户库</el-radio-button>
        <el-radio-button label="layoutType6">观察库</el-radio-button>
      </el-radio-group>
      <div class="layout-box">
        <fullMarks v-if="layoutType == 'layoutType1'"></fullMarks>
        <neutrality v-if="layoutType == 'layoutType2'"></neutrality>
        <dissatisfied v-if="layoutType == 'layoutType3'"></dissatisfied>
        <messageStore v-if="layoutType == 'layoutType4'"></messageStore>
        <catiView v-if="layoutType == 'layoutType5'"></catiView>
        <observeView v-if="layoutType == 'layoutType6'"></observeView>
      </div>
    </div>
  </div>
</template>
<script>
import Banner from './../../components/common/Banner.vue'
import fullMarks from './components/fullMarks.vue';
import neutrality from './components/neutrality.vue';
import dissatisfied from './components/dissatisfied.vue';
import messageStore from './components/messageStore.vue';
import catiView from './components/catiView.vue';
import observeView from './components/observeView.vue';
export default {
  components: { Banner, fullMarks, neutrality, dissatisfied, messageStore, catiView,observeView },
  data() {
    return {
      layoutType: 'layoutType1'
    }
  }
}
</script>
<style lang="scss" scoped>
.santis-three-box {
  position: relative;
  z-index: 1;

  .layout-box {
    padding: 20px 50px;
  }

}
</style>