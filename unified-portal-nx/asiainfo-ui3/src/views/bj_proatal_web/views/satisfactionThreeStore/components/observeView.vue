<template>
  <div>
    <el-form inline label-position="right" label-width="130px" label-suffix="：" size="small">
      <el-form-item label="手机号">
        <el-input v-model="form.phoneNumber" clearable></el-input>
      </el-form-item>
      <el-form-item label="入库原因">
        <el-select v-model="form.reason" placeholder="请选择入库原因" clearable>
          <el-option v-for="(item, index) in reasonList" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="测评时间">
        <el-date-picker v-model="form.testTime" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="业务办理名称">
        <el-input v-model="form.serviceName" clearable></el-input>
      </el-form-item>
      <el-form-item label="投诉内容">
        <el-input v-model="form.complaintContent" clearable></el-input>
      </el-form-item>
      <el-form-item style="margin-left: 50px;">
        <el-button type="primary" @click="query">查询</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="exportData">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" style="width: 100%" v-loading="loading" max-height="560">
      <el-table-column prop="statisDate" label="数据时间" align="center">
      </el-table-column>
      <el-table-column prop="msisdn" label="手机号码" align="center">
      </el-table-column>
      <el-table-column prop="insertResult" label="入库原因" align="center">
        <template slot-scope="scope">
          {{ scope.row.insertResult?reasonList.find((item,index)=>index==scope.row.insertResult):'' }}
        </template>
      </el-table-column>
      <el-table-column prop="score" label="得分" align="center">
      </el-table-column>
      <el-table-column prop="envTime" label="测评时间" align="center">
      </el-table-column>
      <el-table-column prop="complaintContent" label="投诉内容" align="center">
      </el-table-column>
      <el-table-column prop="complaintTime" label="投诉时间" align="center">
      </el-table-column>
      <el-table-column prop="serviceName" label="业务办理名称" align="center">
      </el-table-column>
      <el-table-column prop="servTime" label="业务办理时间" align="center">
      </el-table-column>
    </el-table>
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
      :page-sizes="[5, 10, 20, 50]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
      :total="total" v-if="total">
    </el-pagination>
  </div>
</template>
<script>
import { getDownloadAuthority } from '@/api/reportforms/index'
import { exportSatisfactionThreeDbObserve, getSatisfactionThreeDbObserve, getObserveDropDownMenu } from '@/api/satisfactionThreeStore/index'
export default {
  data() {
    return {
      form: {
        phoneNumber: '',
        reason: '',
        testTime: '',
        serviceName: '',
        complaintContent: '',
      },
      reasonList: [],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 20,
      loading: false
    }
  },
  mounted() {
    this.queryList()
    this.queryReasonList()
  },
  methods: {
    // 查询原因
    queryReasonList() {
      getObserveDropDownMenu().then(res => {
        if (res.code == 200) {
          this.reasonList = res.data
        }
      })
    },
    // 查询列表
    queryList() {
      let params = {
        phoneNumber: this.form.phoneNumber,
        reason: this.form.reason,
        startEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[0] : '',
        endEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[1] : '',
        complaintContent: this.form.complaintContent,
        serviceName: this.form.serviceName,
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      this.loading = true
      getSatisfactionThreeDbObserve(params).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
      }).catch(e => {
        console.error(e)
      }).finally(() => {
        this.loading = false
      })
    },
    // 重置筛选
    reset() {
      this.form = {
        phoneNumber: '',
        reason: '',
        testTime: '',
        serviceName: '',
        complaintContent: '',
      }
      this.currentPage = 1
      this.pageSize = 10
      this.queryList()
    },
    // 导出数据
    async exportData() {
      let jkFlag = await getDownloadAuthority();
      if (!jkFlag.data) {
        eventBus.$emit('startJKauth');
        return
      }
      let params = {
        phoneNumber: this.form.phoneNumber,
        reason: this.form.reason,
        startEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[0] : '',
        endEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[1] : '',
        complaintContent: this.form.complaintContent,
        serviceName: this.form.serviceName,
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.downLoading = true
          return exportSatisfactionThreeDbObserve(params)
        })
        .then((response) => {
          let blobData = response;
          let fileName = '观察库报表' + new Date().getTime()
          const blob = new Blob([blobData], { type: 'application/vnd.ms-excel;charset=utf-8' })
          const name = `${fileName}.xlsx`
          if ('download' in document.createElement('a')) {
            // 非IE下载
            const elink = document.createElement('a')
            elink.download = name
            elink.style.display = 'none'
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            elink.click()
            URL.revokeObjectURL(elink.href) // 释放URL 对象
            document.body.removeChild(elink)
          } else {
            // IE10+下载
            navigator.msSaveBlob(blob, fileName)
          }
        }).finally(() => {
          this.downLoading = false
        })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.queryList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryList()
    },
    query(){
      this.currentPage = 1
      this.queryList()
    },
  }
}
</script>
<style lang="scss" scoped></style>
