<template>
  <div>
    <el-form inline label-position="right" label-width="130px" label-suffix="：" size="small">
      <el-form-item label="手机号">
        <el-input v-model="form.phoneNumber" clearable></el-input>
      </el-form-item>
      <el-form-item label="一级测评渠道">
        <el-select v-model="form.firstChannel" placeholder="请选择一级测评渠道" clearable>
          <el-option v-for="(item, index) in channelOneList" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="二级测评渠道">
        <el-select v-model="form.secondChannel" placeholder="请选择二级测评渠道" clearable>
          <el-option v-for="(item, index) in channelTwoList" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="归属地区">
        <el-cascader v-model="form.areaId" :options="areaListOptions" :props="{ checkStrictly: true }"
          :show-all-levels="false" clearable></el-cascader>
      </el-form-item>
      <el-form-item label="测评时间">
        <el-date-picker v-model="form.testTime" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="数据类型">
        <el-select v-model="form.type">
          <el-option v-for="(item, index) in dataTypeList" :key="index" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="margin-left: 50px;">
        <el-button type="primary" @click="query">查询</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="exportData">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" style="width: 100%" v-loading="loading" max-height="560">
      <el-table-column prop="evaTime" label="评测时间" align="center">
      </el-table-column>
      <el-table-column prop="evaClass1Channel" label="评测渠道" align="center">
      </el-table-column>
      <el-table-column prop="evaClass2Channel" label="二级评测渠道" align="center">
      </el-table-column>
      <el-table-column prop="msisdn" label="手机号" align="center">
      </el-table-column>
      <el-table-column prop="cityName" label="归属地市" align="center">
      </el-table-column>
      <el-table-column prop="districtName" label="归属区县" align="center">
      </el-table-column>
      <el-table-column prop="groupName" label="集团单位名称" align="center">
      </el-table-column>
      <el-table-column prop="groupClass" label="集团单位等级" align="center">
      </el-table-column>
      <el-table-column prop="manageName" label="客户经理" align="center">
      </el-table-column>
      <el-table-column prop="managePhone" label="客户经理电话" align="center">
      </el-table-column>
      <el-table-column prop="brandName" label="品牌" align="center">
      </el-table-column>
      <el-table-column prop="bandSpeed" label="宽带" align="center">
      </el-table-column>
      <el-table-column prop="insertScore" label="得分" align="center">
      </el-table-column>
    </el-table>
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
      :page-sizes="[5, 10, 20, 50]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
      :total="total" v-if="total">
    </el-pagination>
  </div>
</template>
<script>
import { getDownloadAuthority } from '@/api/reportforms/index'
import { exportSatisfactionThreeDb, getSatisfactionThreeDb, getDropDownMenu } from '@/api/satisfactionThreeStore/index'
export default {
  data() {
    return {
      form: {
        phoneNumber: '',
        firstChannel: '',
        secondChannel: '',
        areaId: '',
        testTime: '',
        type: ''
      },
      channelOneList: [],
      channelTwoList: [],
      areaListOptions: [],
      dataTypeList: [],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false
    }
  },
  mounted() {
    this.queryList()
    this.queryCondition()
  },
  methods: {
    // 查询条件
    queryCondition() {
      getDropDownMenu({ tableType: '0' }).then(res => {
        if (res.code == 200) {
          this.channelOneList = res.data.firstChannel || []
          this.channelTwoList = res.data.secondChannel || []
          this.areaListOptions = res.data.areaList0ptions || []
          this.dataTypeList = res.data.numberType || []
        }
      })
    },
    // 查询列表
    queryList() {
      let params = {
        phoneNumber: this.form.phoneNumber,
        firstChannel: this.form.firstChannel,
        secondChannel: this.form.secondChannel,
        cityName: this.form.areaId && this.form.areaId.length ? this.form.areaId[0] : '',
        districtName: this.form.areaId && this.form.areaId.length > 1 ? this.form.areaId[1] : '',
        startEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[0] : '',
        endEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[1] : '',
        type: this.form.type,
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      this.loading = true
      getSatisfactionThreeDb(0, params).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
      }).catch(e => {
        console.error(e)
      }).finally(() => {
        this.loading = false
      })
    },
    // 重置筛选
    reset() {
      this.form = {
        phoneNumber: '',
        firstChannel: '',
        secondChannel: '',
        areaId: '',
        testTime: '',
        type: ''
      }
      this.currentPage = 1
      this.pageSize = 10
      this.queryList()
    },
    // 导出数据
    async exportData() {
      let jkFlag = await getDownloadAuthority();
      if (!jkFlag.data) {
        eventBus.$emit('startJKauth');
        return
      }
      const params = {
        phoneNumber: this.form.phoneNumber,
        firstChannel: this.form.firstChannel,
        secondChannel: this.form.secondChannel,
        cityName: this.form.areaId && this.form.areaId.length ? this.form.areaId[0] : '',
        districtName: this.form.areaId && this.form.areaId.length > 1 ? this.form.areaId[1] : '',
        startEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[0] : '',
        endEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[1] : '',
        type: this.form.type,
        currentPage: this.currentPage,
        pageSize: this.pageSize
      }
      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.downLoading = true
          return exportSatisfactionThreeDb(0, params)
        })
        .then((response) => {
          let blobData = response;
          let fileName = '满分客户库报表' + new Date().getTime()
          const blob = new Blob([blobData], { type: 'application/vnd.ms-excel;charset=utf-8' })
          const name = `${fileName}.xlsx`
          if ('download' in document.createElement('a')) {
            // 非IE下载
            const elink = document.createElement('a')
            elink.download = name
            elink.style.display = 'none'
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            elink.click()
            URL.revokeObjectURL(elink.href) // 释放URL 对象
            document.body.removeChild(elink)
          } else {
            // IE10+下载
            navigator.msSaveBlob(blob, fileName)
          }
        }).finally(() => {
          this.downLoading = false
        })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.queryList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryList()
    },
    query(){
      this.currentPage = 1
      this.queryList()
    },
  }
}
</script>
<style lang="scss" scoped></style>
<style>
input[aria-hidden="true"] {
  display: none !important;
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none;
}
</style>