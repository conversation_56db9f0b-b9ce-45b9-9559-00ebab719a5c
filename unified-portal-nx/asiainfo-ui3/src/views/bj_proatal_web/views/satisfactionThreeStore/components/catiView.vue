<template>
  <div>
    <el-form inline label-position="right" label-width="130px" label-suffix="：" size="small">
      <el-form-item label="手机号">
        <el-input v-model="form.phoneNumber" clearable></el-input>
      </el-form-item>
      <el-form-item label="归属地区">
        <el-cascader v-model="form.areaId" :options="areaListOptions" :props="{ checkStrictly: true }"
          :show-all-levels="false" clearable></el-cascader>
      </el-form-item>
      <el-form-item label="省内测评时间">
        <el-date-picker v-model="form.testTime" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item style="margin-left: 50px;">
        <el-button type="primary" @click="query">查询</el-button>
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="exportData">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" style="width: 100%" v-loading="loading" max-height="560">
      <el-table-column prop="msisdn" label="手机号" align="center">
      </el-table-column>
      <el-table-column prop="cityName" label="归属地市" align="center">
      </el-table-column>
      <el-table-column prop="districtName" label="归属区县" align="center">
      </el-table-column>
      <el-table-column prop="groupName" label="集团单位名称" align="center">
      </el-table-column>
      <el-table-column prop="groupClass" label="集团单位等级" align="center">
      </el-table-column>
      <el-table-column prop="manageName" label="客户经理" align="center">
      </el-table-column>
      <el-table-column prop="managePhone" label="客户经理电话" align="center">
      </el-table-column>
      <el-table-column prop="brandName" label="品牌" align="center">
      </el-table-column>
      <el-table-column prop="bandSpeed" label="宽带" align="center">
      </el-table-column>
      <el-table-column prop="groupCatiTime" label="集团测评时间" align="center">
      </el-table-column>
      <el-table-column prop="groupCatiScore" label="集团cati得分" align="center">
      </el-table-column>
      <el-table-column prop="provCatiTime" label="省内测评时间" align="center">
      </el-table-column>
      <el-table-column prop="provCatiScore" label="省内测评得分" align="center">
      </el-table-column>
    </el-table>
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
      :page-sizes="[5, 10, 20, 50]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper"
      :total="total" v-if="total">
    </el-pagination>
  </div>
</template>
<script>
import { getDownloadAuthority } from '@/api/reportforms/index'
import { exportSatisfactionThreeDbCati, getSatisfactionThreeDbCati, getCatiDropDownMenu } from '@/api/satisfactionThreeStore/index'
export default {
  data() {
    return {
      form: {
        phoneNumber: '',
        areaId: '',
        testTime: '',
      },
      areaListOptions: [],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 20,
      loading: false
    }
  },
  mounted() {
    this.queryList()
    this.queryAreaList()
  },
  methods: {
    // 查询区域
    queryAreaList() {
      getCatiDropDownMenu().then(res => {
        if (res.code == 200) {
          this.areaListOptions = res.data.areaList0ptions
        }
      })
    },
    // 查询列表
    queryList() {
      let params = {
        phoneNumber: this.form.phoneNumber,
        cityName: this.form.areaId && this.form.areaId.length ? this.form.areaId[0] : '',
        districtName: this.form.areaId && this.form.areaId.length > 1 ? this.form.areaId[1] : '',
        startEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[0] : '',
        endEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[1] : '',
        currentPage:this.currentPage,
        pageSize:this.pageSize
      }
      this.loading = true
      getSatisfactionThreeDbCati(params).then(res => {
        if (res.code == 200) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
      }).catch(e => {
        console.error(e)
      }).finally(() => {
        this.loading = false
      })
    },
    // 重置筛选
    reset() {
      this.form = {
        phoneNumber: '',
        areaId: '',
        testTime: '',
      }
      this.currentPage = 1
      this.pageSize = 10
      this.queryList()
    },
    // 导出数据
    async exportData() {
      let jkFlag = await getDownloadAuthority();
      if (!jkFlag.data) {
        eventBus.$emit('startJKauth');
        return
      }
      let params = {
        phoneNumber: this.form.phoneNumber,
        cityName: this.form.areaId && this.form.areaId.length ? this.form.areaId[0] : '',
        districtName: this.form.areaId && this.form.areaId.length > 1 ? this.form.areaId[1] : '',
        startEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[0] : '',
        endEnvTime: this.form.testTime && this.form.testTime.length ? this.form.testTime[1] : '',
        currentPage:this.currentPage,
        pageSize:this.pageSize
      }
      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.downLoading = true
          return exportSatisfactionThreeDbCati(params)
        })
        .then((response) => {
          let blobData = response;
          let fileName = '集团cati不满-省内测评客户库报表' + new Date().getTime()
          const blob = new Blob([blobData], { type: 'application/vnd.ms-excel;charset=utf-8' })
          const name = `${fileName}.xlsx`
          if ('download' in document.createElement('a')) {
            // 非IE下载
            const elink = document.createElement('a')
            elink.download = name
            elink.style.display = 'none'
            elink.href = URL.createObjectURL(blob)
            document.body.appendChild(elink)
            elink.click()
            URL.revokeObjectURL(elink.href) // 释放URL 对象
            document.body.removeChild(elink)
          } else {
            // IE10+下载
            navigator.msSaveBlob(blob, fileName)
          }
        }).finally(() => {
          this.downLoading = false
        })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.queryList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryList()
    },
    query(){
      this.currentPage = 1
      this.queryList()
    },
  }
}
</script>
<style lang="scss" scoped></style>
