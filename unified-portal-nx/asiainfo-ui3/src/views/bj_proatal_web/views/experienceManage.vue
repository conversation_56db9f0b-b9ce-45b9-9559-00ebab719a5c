<template>
    <div class="exp-mg">
        <div class="banner">
            <h1>管理中心</h1>
            <p>查看和管理C、H、B、渠道体验历程场景和权限</p>
            <!-- <button>查看详情</button> -->
        </div>
        <!-- <exp-header></exp-header> -->
        <div class="main-con">
            <div class="main-title">
                <span>管理中心</span>
            </div>
            <div class="mg-con">
                <div class="mg-condition">
                    <div class="condition-1">
                        <div class="condition-1-input">
                            <el-input v-model="keyWords" placeholder="请输入关键字搜索"></el-input>
                        </div>
                        <!-- <div class="condition-1-icon" @click="initTable()">
                            <i class="el-icon-search"></i>
                        </div> -->
                    </div>
                    <div class="condition-btn">
                        <div class="condition-1-btn">
                            <el-button @click="initTable()" type="primary" size='small'>查询</el-button>
                        </div>
                        
                    </div>
                    <div class="condition-2">
                        <span class="condition-label">创建时间:</span>
                        <el-date-picker
                                class="condition-opt"
                                v-model="sDate"
                                value-format="yyyy-M-dd"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :picker-options="pickerOptions"
                                @change="change()">
                        </el-date-picker>
                    </div>
                    <div class="condition-3">
                        <span class="condition-label">状态：</span>
                        <el-select class="condition-opt" v-model="status" placeholder="请选择" @change="change()">
                            <el-option v-for="(option,i) in statusOptions" :label="option.statusName" :value="option.statusCode"></el-option>
                        </el-select>
                    </div>
                    <!--<div class="condition-4">
                        <span class="condition-label">修改内容：</span>
                        <el-select class="condition-opt" v-model="editType" placeholder="请选择">
                            <el-option v-for="(option,i) in editTypeOptions" :label="option.name" :value="option.name"></el-option>
                        </el-select>
                    </div>-->
                    <div class="condition-5">
                        <span class="condition-label">业务类型：</span>
                        <el-select class="condition-opt" v-model="businessType" placeholder="请选择" @change="change()">
                            <el-option v-for="(option,i) in businessTypeOptions" :label="option.name" :value="option.code"></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="mg-table">
                    <el-table
                            border
                            :data="tableData"
                            style="width: 100%;">
                            <el-table-column
                                    :formatter="setIndex"
                                    align="center"
                                    label="序号"
                                    width="50">
                            </el-table-column>
                            <el-table-column
                                    align="center"
                                    prop="businessType"
                                    label="业务类型"
                                    width="auto">
                            </el-table-column>
                            <el-table-column
                                    prop="minorBusiness"
                                    label="业务线条"
                                    align="center"
                                    width="auto">
                            </el-table-column>
                            <el-table-column
                                    prop="sceneName"
                                    label="场景名称"
                                    align="center"
                                    width="auto">
                            </el-table-column>
                            <el-table-column
                                    prop="returnReason"
                                    align="center"
                                    show-overflow-tooltip
                                    label="退回说明"
                                    >
                            </el-table-column>
                            <el-table-column
                                    prop="creator"
                                    align="center"
                                    label="创建人"
                                    width='auto'>
                            </el-table-column>
                            <el-table-column
                                    prop="createTime"
                                    align="center"
                                    label="创建时间"
                                    width="auto">
                            </el-table-column>
                            <el-table-column
                                    prop="status"
                                    align="center"
                                    label="状态"
                                    width="auto">
                            </el-table-column>
                            <!-- <el-table-column
                                    prop="onlineTime"
                                    label="预计上线时间"
                                    width="130">
                            </el-table-column> -->
                            <el-table-column
                                    prop=""
                                    align="center"
                                    label="操作"
                                    width="auto">
                                <template slot-scope="scope">
                                    <!--<el-popover
                                        v-if="(userType==1||userType==2) && (scope.row.status=='已提交' || scope.row.status=='评估中' || scope.row.status=='已排期')"
                                        popper-class="mg-pop"
                                        title="撤销"
                                        width="25"
                                        visible-arrow=false
                                        trigger="hover">
                                        <img slot="reference" src="../assets/img/expCourse/return.png" @click="recall(scope.row)">
                                    </el-popover>-->
                                    <!-- <el-popover v-if="userType==1||userType==2"
                                            popper-class="mg-pop"
                                            title="下载"
                                            trigger="hover"> -->
                                        <!-- <img slot="reference" src="../assets/img/expCourse/dLoad.png" @click="downLoad(scope.row)"> -->
                                        <span @click="downLoad(scope.row)" slot="reference" class="download">下载</span>
                                    <!-- </el-popover> -->
                                    <!--<el-popover
                                        v-if="(userType==1||userType==2) && (scope.row.status=='已提交' || scope.row.status=='评估中' || scope.row.status=='已排期'||scope.row.status=='被退回')"
                                        popper-class="mg-pop"
                                        title="上传"
                                        width="25"
                                        trigger="hover">
                                        <img slot="reference" src="../assets/img/expCourse/uLoad.png" @click="upLoad(scope.row)">
                                    </el-popover>
                                    <el-popover v-if="userType==1||userType==2"
                                            popper-class="mg-pop"
                                            title="提交"
                                            width="25"
                                            trigger="hover">
                                        <img slot="reference" src="../assets/img/expCourse/shp.png" @click="commit(scope.row)">
                                    </el-popover>
                                    <el-popover v-if="userType==1 && scope.row.status!='被退回'"
                                            popper-class="mg-pop"
                                            content="退回"
                                            width="25"
                                            trigger="hover">
                                            <img slot="reference" src="../assets/img/expCourse/shc.png" @click="sendBack(scope.row)">
                                    </el-popover>-->
                                    <!-- <el-popover v-if="userType==1"
                                                popper-class="mg-pop"
                                                content="权限"
                                                width="25"
                                                trigger="hover">
                                        <img slot="reference" src="../assets/img/expCourse/permission.png" @click="configPermission(scope.row)">
                                    </el-popover> -->
                                </template>
                            </el-table-column>
                    </el-table>
                    <div class="page">
                        <span class="totNum">总条数：{{this.totalNum}}</span>
                        <el-pagination
                                background
                                layout="prev, pager, next, jumper"
                                :page-size="pageSize"
                                :current-page.sync="currentPage"
                                :total="totalNum"
                                @current-change="handleCurrentChange">
                        </el-pagination>
                    </div>
                    
                </div>
            </div>
        </div>

        <el-dialog title="权限配置" :visible.sync="dialogVisible" width="40%" center class="rule-dialog" :modal=true>
            <div style="position: relative">
                <h3 class="manTitle title-f">职能部门</h3>
                <el-divider></el-divider>
                <template>
                    <el-checkbox :indeterminate="deptIndeterminate" v-model="deptCheckAll" @change="deptHandleCheckAllChange">全选</el-checkbox>
                    <div style="margin: 15px 0;"></div>
                    <el-checkbox-group v-model="checkedDepts" @change="deptHandleCheckedCitiesChange">
                        <el-checkbox v-for="dept in depts" :label="dept" :key="dept">{{dept}}</el-checkbox>
                    </el-checkbox-group>
                </template>

                <h3 class="manTitle title-s">分公司</h3>
                <el-divider></el-divider>
                <template>
                    <el-checkbox :indeterminate="isIndeterminate" v-model="cityCheckAll" @change="handleCheckAllChange">全选</el-checkbox>
                    <div style="margin: 15px 0;"></div>
                    <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange">
                        <el-checkbox v-for="city in cities" :label="city" :key="city">{{city}}</el-checkbox>
                    </el-checkbox-group>
                </template>
            </div>

            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="permissionCommit">提交</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
    import Vue from 'vue';

    import {Input,DatePicker,Select,Option,Table,
        TableColumn,Pagination,Popover, MessageBox,Notification,Dialog,CheckboxGroup,Checkbox} from 'element-ui';
    import expHeader from "../components/expHeader.vue";
    Vue.use(Input)
        .use(DatePicker)
        .use(Select)
        .use(Option)
        .use(Table)
        .use(TableColumn)
        .use(Pagination)
        .use(Popover).use(Dialog)
        .use(CheckboxGroup).use(Checkbox);
    export default {
        name:'experienceManage',
        components:{
            expHeader,
        },
        data(){
            return {
                
                keyWords:'', // 搜索关键字
                sDate:'', // 日期
                pickerOptions:{
                    disabledDate(time) {
                        return time.getTime() > Date.now() - 8.64e6;//如果没有后面的-8.64e6就是不可以选择今天的
                    }
                },
                status:'', // 状态
                pageSize:10,
                currentPage:1,
                totalNum:0,
                statusOptions:[],
                editType:'', // 修改内容
                /*editTypeOptions:[{
                    name:'新增场景',
                    code:'1'
                },{
                    name:'编辑场景',
                    code:'2'
                }],*/
                businessType:'', // 业务类型
                businessTypeOptions:[
                {
                    name:'全部',
                    code:''
                },{
                    name:'个人业务',
                    code:'个人业务'
                },{
                    name:'家庭业务',
                    code:'家庭业务'
                },{
                    name:'政企业务',
                    code:'政企业务'
                },{
                    name:'新兴业务',
                    code:'新兴业务'
                },{
                    name:'网络',
                    code:'网络'
                },{
                    name:'渠道',
                    code:'渠道'
                },{
                    name:'渠道',
                    code:'渠道'
                }], // 个人业务（C）、家庭业务（H）、政企业务（B）、新兴业务（N）、网络、渠道、其他
                tableData: [],
                userType:'', // 用户类型1-管理员用户 2-业务用户、数据列表

                /*权限配置*/
                dialogVisible:false,
                // 分公司
                cityCheckAll: false,  //是否全选
                checkedCities: [], //选中的项
                cities: [], //所有选项
                citiesId: [], //所有选项编码
                isIndeterminate: true, //是否部分选中
                //职能部门
                deptCheckAll: false, //是否全选
                checkedDepts: [], //选中的项
                depts: [], //所有选项
                deptsId: [], //所有选项编码
                deptIndeterminate:false, //是否部分选中
                sceneId:'', //操作的场景id
            }
        },
        created(){
            this.initCondition();
            this.initTable();
        },
        methods:{
            initCondition(){
                const that = this;
                this.$http
                    .post('/experience-service/managementCenter/queryStatus', {})
                    .then((res) => {
                        const a=[{
                            statusCode: "", statusName: "全部"
                        }]
                        that.statusOptions=a.concat( res.data);
                    }).catch((res) =>{
                })
                this.$http
                    .post('/experience-service/managementCenter/queryBusinessType', {})
                    .then((res) => {
                        that.businessTypeOptions=[{
                            code: "", name: "全部"
                        }]
                        res.data.forEach(function (e,i) {
                            that.businessTypeOptions.push({
                                code: e, name: e
                            })
                        })
                        console.log(that.businessTypeOptions);
                    }).catch((res) =>{
                })
            },
            initTable(){
                const that = this;
                const params={
                    "businessType": this.businessType,
                    "createTimeBegin": this.sDate?this.sDate[0]:"",
                    "createTimeEnd": this.sDate?this.sDate[1]:"",
                    /*"modifyContent": this.editType,*/
                    "pageNum": this.currentPage,
                    "pageSize": this.pageSize,
                    "searchValue": this.keyWords,
                    "status": this.status,
                }
                that.$http
                    .post('/experience-service/managementCenter/queryInfo', params)
                    .then((res) => {
                        console.log('___1111',res);
                        that.tableData=res.data.managementCenterVoList;
                        that.userType = res.data.userType;
                        that.totalNum = res.data.totalSize;
                    }).catch((res) =>{
                })
            },
            setIndex(row, column, cellValue, index){
                return index+1;
            },
            handleCurrentChange(currentPage){
                this.currentPage = currentPage;
                this.initTable();
            },
            change(){
                this.currentPage = 1;
                this.initTable();
            },
            //撤回
            recall(row){
                const that = this;
                that.$http
                    .post('/experience-service/managementCenter/deleteInfo', {id:row.id})
                    .then((res) => {
                        if(res.success){
                            Notification({
                                title: '成功',
                                message: '撤回成功！',
                                type: 'success'
                            });
                        }else{
                            Notification.error({
                                title: '失败',
                                message: '撤回失败！',
                            });
                        }
                    }).catch((res) =>{
                        Notification.error({
                            title: '失败',
                            message: '撤回失败！',
                        });
                })
            },
            //提交
            commit(row){
                const that = this;
                that.$http
                    .post('/experience-service/managementCenter/xxx', {id:row.id})
                    .then((res) => {

                    }).catch((res) => {
                })

            },
            //下载
            downLoad(row){
                console.log(row);
                var origin = window.location.protocol + "//" + window.location.hostname + (window.location.port ? ':' + window.location.port: '');
                /* var origin = window.location.origin;*/
                console.log(origin);
                // 测试环境
                //const url = `${origin}/proxy-satisfy/experience-service/managementCenter/downloadBySceneId?sceneId=${row.sceneId}&date=${row.createTime.substring(0,10).replace(/\//g, '-')}`;
                // 正式环境${window.location.origin}
                const url = `${origin}/bjcem/experience-service/managementCenter/downloadBySceneId?sceneId=${row.sceneId}&date=${row.createTime.substring(0,10).replace(/\//g, '-')}`;
                window.open(url,'_blank');
            },
            //上传
            upLoad(){

            },
            //权限配置查询
            configPermission(row) {
                const that=this;
                that.sceneId = row.sceneId;
                const params = {
                    sceneId:row.sceneId,
                   /* "sceneId":"2",*/
                }
                that.$http
                    .post('/experience-service/managementCenter/queryPermissionBySceneId',params)
                    .then((res) => {
                        that.depts=[];
                        that.checkedDepts = [];
                        res.data.listMap['1'].forEach(function (e,i) {
                            that.depts.push(e.companyDept);
                            that.deptsId[e.companyDept]=e.companyId;
                            if(e.flag){
                                that.checkedDepts.push(e.companyDept);
                            }
                        });
                        if(that.checkedDepts.length == that.depts.length ){
                            that.deptCheckAll = true;
                            that.deptIndeterminate = false;
                        }else {
                            that.deptCheckAll = false;
                            that.deptIndeterminate = that.checkedDepts.length == 0?false:true;
                        }
                        that.cities=[];
                        that.checkedCities = [];
                        res.data.listMap['2'].forEach(function (e,i) {
                            that.cities.push(e.companyDept);
                            that.citiesId[e.companyDept]=e.companyId;
                            if(e.flag){
                                that.checkedCities.push(e.companyDept);
                            }
                        });
                        if(that.checkedCities.length == that.cities.length ){
                            that.cityCheckAll = true;
                            that.isIndeterminate = false;
                        }else {
                            that.cityCheckAll = false;
                            that.isIndeterminate = that.checkedCities.length == 0?false:true;
                        }
                    }).catch((res) =>{
                })
                this.dialogVisible = true;
            },
            //权限配置提交
            permissionCommit(){
                const that=this;
                const listMap = {
                    1:[],
                    2:[],
                };
                const deptsVal={},citiesVal={};
                if(that.deptCheckAll){
                    that.depts.forEach(function (e,i) {
                        deptsVal[e]=true;
                    });
                }else{
                    that.depts.forEach(function (e,i) {
                        deptsVal[e]=false;
                    });
                    that.checkedDepts.forEach(function (e,i) {
                        deptsVal[e] = true;
                    })
                }
                for(var key in deptsVal){
                    listMap['1'].push({
                        "companyId": that.deptsId[key],
                        "flag": deptsVal[key],
                    })
                }

                if(that.cityCheckAll){
                    that.cities.forEach(function (e,i) {
                        citiesVal[e]=true;
                    });
                }else{
                    that.cities.forEach(function (e,i) {
                        citiesVal[e]=false;
                    });
                    that.checkedCities.forEach(function (e,i) {
                        citiesVal[e] = true;
                    })
                }
                for(var key in citiesVal){
                    listMap['2'].push({
                        "companyId": that.citiesId[key],
                        "flag": citiesVal[key],
                    })
                }

                console.log(listMap);
                that.$http
                    .post('/experience-service/managementCenter/modifyPermissionBySceneId', {listMap:listMap,sceneId:that.sceneId})
                    .then((res) => {
                        if(res.success){
                            Notification({
                                title: '成功',
                                message: '权限修改成功！',
                                type: 'success'
                            });
                            this.dialogVisible = false;
                        }else{
                            Notification.error({
                                title: '失败',
                                message: '权限修改失败！',
                            });
                        }
                    }).catch((res) =>{
                        Notification.error({
                            title: '失败',
                            message: '权限修改失败！',
                        });
                    })
            },
            //分公司全选
            handleCheckAllChange(val) {
                this.checkedCities = val ? this.cities : [];
                this.isIndeterminate = false;
            },
            //分公司勾选
            handleCheckedCitiesChange(value) {
                let checkedCount = value.length;
                this.cityCheckAll = checkedCount === this.cities.length;
                this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;
            },
            //职能部门全选
            deptHandleCheckAllChange(val) {
                this.checkedDepts = val ? this.depts : [];
                this.deptIndeterminate = false;
            },
            //职能部门勾选
            deptHandleCheckedCitiesChange(value) {
                let checkedCount = value.length;
                this.deptCheckAll = checkedCount === this.depts.length;
                this.deptIndeterminate = checkedCount > 0 && checkedCount < this.depts.length;
            },
            //退回
            sendBack(row){
                const that = this;
                const html  = '<textarea id="backInfo" style="background: #F4F7F9;border: none;padding: 10px;margin-top: 10px;" rows="6" cols="47" placeholder="请输入...">' +
                    '</textarea>';
                MessageBox.alert(html, {
                    dangerouslyUseHTMLString: true,
                    title:'原因说明',
                    customClass:'def-mgbox',
                    cancelButtonClass:'mgbox-cancel',
                    confirmButtonClass:'mgbox-confirm',
                    center: true,
                    showCancelButton:true,
                    confirmButtonText: '提交',
                    beforeClose(action, instance, done){
                        if(action=='close'||action == 'cancel'){
                            console.log('qx');
                            done();
                        }
                        if(action == 'confirm'){
                            console.log('qd');
                            const  params={
                                "id": row.id,
                                "returnReason": document.getElementById('backInfo').value,
                                "status": "8"
                            }
                            console.log(params);
                            if(!params.returnReason){
                                Notification({
                                    title: '提示',
                                    message: '请填写退回原因！',
                                    type: 'warning'
                                });
                            }else{
                                that.$http
                                    .post('/experience-service/managementCenter/updateInfo', params)
                                    .then((res) => {
                                        if(res.success){
                                            Notification({
                                                title: '成功',
                                                message: '退回成功！',
                                                type: 'success'
                                            });
                                            that.initTable();
                                        }else{
                                            Notification.error({
                                                title: '失败',
                                                message: '退回失败！',
                                            });
                                        }
                                        done();
                                    }).catch((res) =>{
                                        Notification.error({
                                            title: '失败',
                                            message: '退回失败！',
                                        });
                                        done();
                                })
                            }
                        }
                    }
                });
            }
        }
    };
</script>
<style scoped lang="less">
.download{
    cursor: pointer;
    color: #f90;
}
.banner {
      height: 250px;
      background: url(../assets/img/banner-bg.png) right no-repeat;
      background-size: contain;
      box-shadow: 0 3px 5px rgba(0,0,0,0.03);
      position: relative;
      z-index:1;
      -display: flex;
      padding-left:80px;
      box-sizing: border-box;
      -justify-content: center;
      -flex-direction: column;
      background-color: #f7f7f7;
      h1 {
          width: 100%;
          font-family: SourceHanSansSC-Bold;
          font-size: 22px;
          color: #262626;
          letter-spacing: -0.43px;
          margin:0px;
          float: left;
          margin-top:80px;
      }

      p {
        font-size: 16px;
        font-family: SourceHanSansSC-Medium;
        color: #999;
        margin: 0px;
        margin-top:10px;
        display: inline-block;
        width: auto;
        float: left;
        margin-top:10px;
        position: relative;
        &::after {
          content:"";
          display: block;
          width: 100%;
          height:5px;
          position: absolute;
          bottom:-2px;
          background-image: linear-gradient(90deg, rgba(241,151,51,0.8) 0%, rgba(241,151,51,0) 100%);
        };
      }
    

      button {
          margin-left: 144px;
          margin-top: 24px;
          background: #F09C32;
          border-radius: 4px;
          width: 117px;
          height: 32px;
          border: none;
          font-family: SourceHanSansSC-Regular;
          font-size: 14px;
          color: #FFFFFF;
          letter-spacing: -0.27px;
          cursor: pointer;
      }
    }
.page{
    justify-content: flex-end;
    /* flex-direction: row-reverse; */
    margin-top: 30px;
    /* position: relative; */
    display: flex;
    align-items: center;
    font-family: SourceHanSansSC-Regular;
    .totNum{
        margin-right: 1.1%;
        font-family: SourceHanSansSC-Regular;
        font-size: 14px;
        color: #595959;
    }

    .btn-prev {
        background: #fff;
        border: 1px solid #E1E1E1;
        border-radius: 4px;
    }
}
.condition-btn{
    width: 76px;
    height: 32px;
    margin-right: 24px;
    float: left;
    top: 50%;
    transform: translateY(50%);
    .condition-1-btn{
        .el-button--medium{
            background: #F19733;
            border-radius: 4px;
            font-family: SourceHanSansSC-Normal;
            font-size: 14px;
            color: #262626;
            letter-spacing: -0.27px;
            padding: 9px 24px;
        }
    }    
}
    .main-con {
        height: auto;
        padding: 0 40px 40px 40px; 
        background-color: #f5f5f5;
        .main-title{
            position: relative;
            height: 77px;
            line-height: 77px;
            text-align: left;
            width: 100%;
            /*position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;*/
            // span:before{
                // content: "";
                // position: absolute;
                // left: 5px;
                // top: 6px;
                // margin: auto;
                // width: 4px;
                // height: 24px;
                // background: #2F9CFA;
            // }
            span{
                font-family: SourceHanSansSC-Medium;
                font-size: 20px;
                color: #4E4E4E;
                letter-spacing: -0.39px;
                // font-size: 18px;
                // color: #575B61;
                // margin-left: 18px;
            }
        }
        .mg-con{
            padding: 0 20px;
            padding-top:7px;
            background-color: white;
            // margin-bottom: 78px;
            // padding-bottom: -78px;
            .mg-condition{
                height: 70px;
                width: 100%;
                margin-top:6px;
                /*display: flex;
                align-items: center;
                flex-direction: row;*/
                .condition-label{
                    font-family: SourceHanSansSC-Regular;
                    font-size: 14px;
                    color: #262626;
                    letter-spacing: -0.27px;
                }
                .condition-1,.condition-2,.condition-3,.condition-4,.condition-5{
                   /* margin-right: 16px;*/
                    margin-right: 16px;
                    float: left;
                    top: 50%;
                    transform: translateY(50%);
                }
            }
        }
    }
    .mg-condition .condition-1{
        width: 199px;
        height: 32px;
        // background: #fff;
        // border-radius: 42px;
        border: 1px solid #e6e6e6;
        line-height: 32px;
        /*display: flex;
        justify-content: center;*/
        border-radius: 4px;

        div{
            height: 100%;
        }
        .condition-1-input{
            width: 85%;
            float: left;

            /deep/ .el-input{
                display: flex;
                justify-content: flex-end;
            }
            /deep/ .el-input__inner{
                height: 100%;
                width: 95%;
                border: none;
                background: none;
                padding: 0 10px;
                line-height: 0px;
                font-family: SourceHanSansSC-Regular;
                font-size: 14px;
                color: #595959;
                letter-spacing: -0.27px;
            }
        }
        .condition-1-icon{
            width: 15%;
            border-top-right-radius: 42px;
            border-bottom-right-radius: 42px;
            background-color: #4993FF;
            /*display: flex;
            align-items: center;
            justify-content: center;*/
            float: left;
            color: white;
            font-size: 16px;
        }
    }
    .condition-1 /deep/ .el-input__inner{
        color: #45494D;
    }
    // .condition-2{
    //     margin-top: 3px;
    // }
    .condition-2 /deep/ .el-range-editor.el-input__inner {
        padding: 0px 10px;
    }
    .condition-2 /deep/ .el-date-editor--daterange.el-input__inner{
        width: 260px;
        color: #45494D;
        font-weight: bold;
        vertical-align: middle;
        margin-left: 23px;
    }
    .condition-2 /deep/ .el-range__close-icon{
        width: 10px;
    }
    .condition-opt{
        height: 32px;
        width: 100px;
        background: #fff;
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        vertical-align: middle;
    }
    .condition-opt /deep/ .el-input .el-input__inner{
        height: 30px;
        background: #fff;
        border: none;
        border-radius: 2px;
        line-height:0px;
    }
    .condition-opt /deep/ .el-range-input{
        background: #fff;
        vertical-align: top;
    }
    .condition-opt /deep/ .el-input__icon{
        line-height: 28px;
        color: #45494D;
    }
    .mg-table{
        padding-bottom: 10px;
        /*/deep/ .el-table td,/deep/ .el-table th{
            text-align: center;
        }*/

        /deep/ .el-table--border th,/deep/ .el-table--border td{
            /*border-right: unset;*/
            border-right: none;
        }
        /deep/ .el-pagination{
            /*display: flex;
            justify-content: flex-end;*/
            text-align: right;
            padding: 15px 0;
        }
        /deep/ thead tr th{
            font-size: 14px;
            color: #262626;
        }
        /deep/ tbody tr td{
            font-size: 12px;
            color: #434951;
            padding: 3px 0;
            height: 40px;
            font-family: SourceHanSansSC-Regular;
            font-size: 14px;
            color: #595959;

            img{
                cursor: pointer;
            }
        }
        /*/deep/ tbody tr:nth-child(even) td{
            background-color: #FAFCFD;
        }*/
        /deep/ .el-pagination.is-background .btn-next,
        /deep/ .el-pagination.is-background .btn-prev,
        /deep/ .el-pagination.is-background .el-pager li{
            margin: 0 2px;
            background: #fff;
            border: 1px solid #e1e1e1;
            font-family: SourceHanSansSC-Light;
            color: #8c8c8c;
        }

        /deep/ .el-pagination.is-background .el-pager li:not(.disabled).active {
            color: #262626;
            font-family: SourceHanSansSC-Bold;
        }
    }
    .exp-mg {
        /deep/ .exp-header .header-s{
            display: none;
        }
    }
    /*弹框样式 start */
    .manTitle{
        padding: 10px 0 0 10px;
        font-size: 12px;
    }
    .title-f{
        text-align: left;
    }
    .title-f:before{
        content: "";
        position: absolute;
        left: 0px;
        margin: auto;
        width:4px;
        height:16px;
        background: #2F9CFA;
    }
    .title-s{
        text-align: left;
        margin-top: 15px;
    }
    .title-s:before{
        content: "";
        position: absolute;
        left: 0px;
        margin: auto;
        width:4px;
        height:16px;
        background: #FFB44E;
    }
    .rule-dialog /deep/ .el-dialog--center .el-dialog__body{
        padding: 0px 25px 10px 25px;
        text-align: left;
    }
    .rule-dialog .el-divider--horizontal{
        margin: 10px 0;
    }
    .rule-dialog .el-button--primary{
        width: 80px;
        height: 30px;
        background: #4993FF;
        border-radius: 3px;
        color: white;
        padding: 0;
    }
    .rule-dialog .el-button--default{
        width: 80px;
        height: 30px;
        background: #FFFFFF;
        border-radius: 3px;
        color: #45494D;
        padding: 0;
    }
    .rule-dialog{
        border: none;
        border-radius: 6px;
    }
    .rule-dialog /deep/ .el-dialog__header{
        padding-top: 10px;
        background-image: linear-gradient(-90deg, #1FBAFF 2%, #5F80FF 100%);
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=1, startColorstr=#5F80FF, endColorstr=#1FBAFF);;
    }
    .rule-dialog /deep/ .el-dialog__header .el-dialog__title{
        font-size: 14px;
        color: #FFFFFF;
    }
    .rule-dialog /deep/ .el-dialog__headerbtn{
        top:10px;
        .el-dialog__close{
            color: white;
        }
    }
    .rule-dialog /deep/ .el-checkbox__input.is-checked .el-checkbox__inner,
    .rule-dialog /deep/ .el-checkbox__input.is-indeterminate .el-checkbox__inner{
        background-color: #4CCE8B;
        border-color: #4CCE8B;
    }
    .rule-dialog /deep/ .el-checkbox__input.is-checked+.el-checkbox__label{
        color: #606266;
    }
    .rule-dialog .el-checkbox{
        margin-right: 20px;
        width: 140px;
        /*text-align: left;*/
    }
    .condition-opt /deep/ .el-range-separator{
        vertical-align: top;
    }
    /*弹框样式 end */
</style>

<style lang="less">
  .el-button--primary,.el-button--primary:hover, .el-button--primary:focus {
    background-color: #F19733;
    border-color: #F19733;
    color: #262626;
  }

  .el-button--small {
    padding: 0 16px;
    height: 32px;
  }

    /*.el-input input::-webkit-input-placeholder {
        font-family: SourceHanSansSC-Regular;
        font-size: 14px;
        color: #595959;
        letter-spacing: -0.27px;
    }

    .el-input input::-moz-input-placeholder {
        color: #AEABAB;
        font-size: 14px;
        color: #595959;
        letter-spacing: -0.27px;
    }

    .el-input input::-ms-input-placeholder {
        color:#AEABAB;
        font-size: 14px;
        color: #595959;
        letter-spacing: -0.27px;
    }*/


</style>