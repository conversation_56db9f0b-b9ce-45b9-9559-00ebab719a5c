<template>
  <div style="background:#fff;">
    <!-- 查询条件 -->
    <el-row>
      <el-col v-if="conditionsArr.length" :span="20" style="text-align:left">
        <el-form ref="queryForm" :inline="true" class="demo-form-inline" size="small" :model="form">
          <el-form-item v-for="(i, index) in conditionsArr" :key="index" :prop="i.key" :label="i.label" class="my-el-form-item">
            <el-input v-if="i.type=='input'" v-model="form[i.key]" :placeholder="i.placeholder?i.placeholder:'请输入'" :style="{width:i.width?i.width:'unset'}" />
            <el-date-picker
              v-if="i.type=='daterange'"
              v-model="form[i.key]"
              :style="{width:i.width?i.width:'unset'}"
              type="daterange"
              format="yyyy - MM - dd "
              range-separator="至"
              clearable
              :picker-options="i.pickerOptions"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
            <el-select v-if="i.type=='select'" v-model="form[i.key]" :multiple="i.multiple" clearable :style="{width:i.width?i.width:'unset'}">
              <el-option
                v-for="item in i.options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-date-picker
              v-if="i.type=='date'"
              v-model="form[i.key]"
              clearable
              format="yyyy - MM - dd "
              placeholder="请选择"
            />
            <el-cascader
              v-if="i.type=='cascader'"
              v-model="form[i.key]"
              clearable
              :style="{width:i.width?i.width:'unset'}"
              collapse-tags
              popper-class="treeLabelDataCascader"
              :props="i.props"
              :options="i.options"
              :show-all-levels="false"
            />
          </el-form-item>
          <el-form-item v-if="conditionsArr.length">
            <el-button type="primary" :loading="btnLoading" @click="query">查询</el-button>
            <el-button type="default" @click="()=>reset('queryForm')">重置</el-button>

          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="4" class="btn-box">
        <slot name="btn-group" />
      </el-col>
    </el-row>
    <!-- 表格数据 -->
    <div class="tablebox">
      <el-table
        v-loading="tableLoading"
        :data="tableData"

        v-bind="$attrs"
        v-on="$listeners"
      >
        <!-- 自定义列的遍历-->
        <el-table-column v-for="(item, index) in columnsNames" :key="index" align="center" v-bind="isObject(item.dataObj)?item.dataObj:''" :label="isObject(item.dataObj)?item.dataObj.label:item.label">
          <!-- 数据的遍历  scope.row就代表数据的每一个对象-->
          <template slot-scope="scope">
            <slot v-if="item.dataObj&&isSlot(item.column)" :name="item.column" :data="{row:scope.row,key:item.column}" />
            <span v-else>{{ scope.row[item.column] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <slot name="additionBtn" />

    </div>
    <!-- 分页 -->
    <div v-if="pagination" class="pagination-box">
      <el-pagination
        :current-page="form.pagination.currentPage"
        :page-sizes="[2,5,10,20]"
        :page-size="form.pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="form.pagination.totalCount"
        @size-change="pageSizeChange"
        @current-change="currentPageChange"
      />
    </div>

  </div>
</template>
<script>

export default {
  name: 'TableList',
  props: {
    // 查询条件 配置
    conditions: {
      type: Array,
      required: false,
      default: () => []
    },
    // 字段名-中文显示
    columns: {
      type: Object,
      required: true,
      default: () => {}
    },
    // pagination 分页组件
    pagination: {
      type: Boolean,
      required: false,
      default: true
    }
  },

  data() {
    return {

      tableData: [],
      tableLoading: false,
      btnLoading: false,
      form: {
        pagination: {
          pageSize: 10,
          currentPage: 1,
          totalCount: 0
        }
      }
    }
  },

  computed: {
    conditionsArr() {
      if (this.conditions.length) {
        return this.conditions.map((con) => {
          switch (con.type) {
            case 'input' :
            case 'daterange':
            case 'date':
            case 'cascader':
              return con
            case 'select': // 是否有函数 函数优先
              if (typeof con.getOpts === 'function') {
                con.getOpts({}).then((res) => {
                  if (Array.isArray(res.data)) {
                    con.options = res.data.map((i) => {
                      i.id = i.type || i.labelId || i.id
                      i.label = i.value || i.labelName || i.labelName
                      i.value = i.type || i.labelId || i.id
                      return i
                    })
                  }
                })
              } else if (con.options && Array.isArray(con.options) && con.options.length) {
                con.options = con.options.map((i) => {
                  i.id = i.value

                  return i
                })
              }

              return con

            default:
              break
          }
        })
      }
      return []
    },
    columnsNames() {
      let cols = []
      if (Object.prototype.toString.call(this.columns) === '[object Object]') {
        cols = Object.keys(this.columns).map((i) => {
          let tempObj = null
          tempObj = { column: i, label: this.columns[i] }
          if (Object.prototype.toString.call(this.columns[i]) === '[object Object]') {
            tempObj = { column: i, dataObj: this.columns[i] }
          } else {
            tempObj = { column: i, label: this.columns[i] }
          }
          return tempObj
        })
      }
      console.log('cols=>', cols)
      return cols
    }

  },
  created() {

  },
  mounted() {
    this.getList()
  },
  methods: {

    reset(formName) {
      const keys = Object.keys(this.form)
      keys.forEach(i => {
        if (i == 'pagination') {
          this.form.pagination.currentPage = 1
          this.form.pagination.pageSize = 10
          this.form.pagination.totalCount = 0
        } else {
          this.form[i] = undefined
        }
      })
      this.getList()
      // this.$refs[formName].resetFields()
      // this.$nextTick(this.$refs[formName].resetFields())
    },
    isSlot(key) {
      if (key && this.$scopedSlots[key]) return true
      return false
    },
    // 判断是否是对象
    isObject(i) {
      return Object.prototype.toString.call(i) === '[object Object]'
    },
    // 处理查询数据的参数 根据不同的参数类型来处理
    handleParamsByCondition() {
      const temp = JSON.parse(JSON.stringify(this.form))
      console.log('temp==>', temp)
      const conditions = this.conditions
      delete temp.pagination.totalCount
      conditions.forEach((i) => {
        if (i.type == 'daterange' && Array.isArray(i.parmsName) && i.parmsName.length && Array.isArray(temp[i.key]) && temp[i.key].length) {
          temp[i.parmsName[0]] = this.$moment(temp[i.key][0]).format('YYYY-MM-DD')
          temp[i.parmsName[1]] = this.$moment(temp[i.key][1]).format('YYYY-MM-DD')
          delete temp[i.key]
        }
        if (i.type == 'date') {
          temp[i.key] = this.$moment(temp[i.key]).format('YYYY-MM-DD')
        }
      })
      return temp
    },
    query() {
      this.btnLoading = true
      this.form.pagination.pageSize = 10
      this.form.pagination.currentPage = 1
      this.getList()
    },
    // 查询列表数据
    getList(p) {
      p = p || {}
      this.tableLoading = true
      let params = this.handleParamsByCondition() || {}
      params = Object.assign({}, params, p)
      this.$emit('queryData', params)
      return
    },
    conditionsChange() {
      this.getList()
    },
    init() {
      const conditions = this.conditions
      if (conditions.length) {
        conditions.forEach((i) => {
          this.form[i] = ''
        })
      }
    },

    // 分页相关参数改变
    pageSizeChange(v) {
      this.form.pagination.pageSize = v
      this.getList()
    },
    currentPageChange(v) {
      this.form.pagination.currentPage = v
      this.getList()
    }

  }

}
</script>

<style   lang="scss" scoped>

  .list-box{
    margin: 50px 20px;
  }

  .pagination-box {
    text-align: right;
    padding:10px;
    background:#fff;
  }

  .my-el-form-item {
    margin-left: 10px;
  }
  .btn-box {
    text-align: right;
    margin-bottom: 10px;
  }
  .el-cascader {
        /deep/.el-icon-arrow-down:before {
          content: "\E6E1";
        }
       /deep/.el-icon-arrow-down{
         transform: rotate(180deg);
       }
       /deep/.is-reverse.el-icon-arrow-down{
         transform: rotate(0deg);
       }

  }
  .tablebox{
    position: relative;
    margin:10px;
    border:1px solid rgba(225,225,225,0.8);
    border-bottom: none;
  }
</style>
