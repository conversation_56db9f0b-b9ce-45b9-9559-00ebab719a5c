<template>
<div :class="['area-picker',`area-picker__${size}`]">
  <span v-show="showLabel">选择地市：</span>
   <el-cascader v-model="area" ref="areaCascader" :size="size"
    clearable
                placeholder="请选择地市" :options="options" :props="props" @change="onChange" />
</div>
</template>

<script>
import { AreaByUser } from '@/api/mobilePhoneTariff'

export default {
  name: 'AreaPicker',
  inject: ['mapPermission'],
  props:{
    value: {
     type: Array,
     default: ()=>[]
    },
    showLabel:{
      type: Boolean,
      default: true
    },
    size:{
      type: String,
      default: 'medium'
    },
    // 设置最多到几层，不设置，就显示全部层级
    maxAreaLevel: {
      type: Number,
      default: 0
    },
    /**
     * 添加选择选项
     * {
     *  name: '',
     *  id: '',
     *  data:{
     *   name: '',
     *   id: '',
     *   level: ''
     *  }
     * }
     */
    extOptions: {
      type: Array,
      default: ()=>[]
    },
    /**
     * 选中的地市的id，不赋值，用用户权限的地市id
     */
    selectedArea: {
      type: String | Number
    },
    /**
     * 层级数据，如果设置了就不取6MujCDCs的数据
     */
    optionData: {
     type: Array
    }
  },
  data() {
    const nxAreaStore = sessionStorage.getItem('nx-area');
    return {
      nxArea: nxAreaStore ? JSON.parse(nxAreaStore) : null,
      area: this.value,
      options: [],
      props: {
        label: 'name',
        value: 'id',
        checkStrictly: true
      },
    }
  },
  methods: {
    // 获取地市层级数据
    async getAreaList() {
      try{
        // 如果有其他的数据来源，就用其他的数据
        if(this.optionData) {
           this.setAreaOption(this.optionData);
           return;
        }
        /**如果sessionStorage有数据，就直接使用 */
        if(this.nxArea) {
           this.setAreaOption(this.nxArea);
           return;
        }

        const { data, code } = await AreaByUser({
          restSerialNo: '6MujCDCs'
        })
        if(code == 200 && data) {
          const nxAreaStore = sessionStorage.getItem('nx-area');
          if(data[0] && !nxAreaStore) {
            sessionStorage.setItem('nx-area',JSON.stringify(data[0]))
          }
          this.setAreaOption(data[0] || []);
        }
      }catch(e){

      }
    },
    setAreaOption(data) {
      const options = this.getOption(data || []);
      const perOptions = this.getPermissionOption(options);
      this.options = this.extOptions.concat(perOptions);
    },
    // 筛选最大数据层级
    getOption(ary) {
      if(!this.maxAreaLevel) return ary;
      return ary.filter((item)=>{
        if(item.children) {
          const children = this.getOption(item.children)
          item.children = children.length ? children : null;
        }
        return item.data.level  <= this.maxAreaLevel;
      })
    },
    // 筛选用户权限数据
    getPermissionOption(ary) {
      const {mapLevel,cityId} = this.mapPermission;
      const [first] = ary;
      if(first.data.level == mapLevel){
        return ary.filter((item)=>{ return item.id == cityId; })
      }else if(first.data.level < mapLevel){
        return ary.reduce((pre,cur)=>{
          let nextMap = [];
          if(cur.children) {
            nextMap = this.getPermissionOption(cur.children);
          }
          return pre.concat(nextMap);
        },[])
      }else {
        return ary;
      }
    },
    onChange(values) {
      this.area = [].concat(values);
      const checkedNodes = this.$refs.areaCascader.getCheckedNodes();
      const checkedNodeData = checkedNodes.map(({data})=>{
         return data.data;
      })
      this.$emit('change',this.area,checkedNodeData);
      this.$emit('input',this.area);
    },
    initAreaValue() {
      if(this.selectedArea) {
        this.area = this.selectedArea;
      }else{
        const {cityId} = this.mapPermission;
        this.area = [cityId];
      }
    },
    handleChangeSelectedArea() {
      if(this.selectedArea){
        this.area = this.setCheckedAreaNode(this.options,this.selectedArea);
      }
    },
    setCheckedAreaNode(ary,areaId) {
      let areas = [];
      for(const item of ary){
        if(item.id == areaId){
          areas.push(areaId);
          break;
        }
        if(item.children){
         const childArea = this.setCheckedAreaNode(item.children,areaId);
         if(childArea.length){
          areas.push(item.id);
          areas = areas.concat(childArea);
         }
        }
      }
      return areas;
    }
  },
  created(){
    this.initAreaValue();
    this.getAreaList();
  },
  watch:{
    selectedArea() {
      this.handleChangeSelectedArea();
    },
    optionData() {
      this.getAreaList();
    },
    value(val) {
      this.area = val;
    }
  }
}
</script>

<style lang="scss" scoped>
.area-picker {
  display: inline-block;
  margin-right: 10px;
}
.area-picker__small{
  span{
    font-size: 14px;
  }
}
/deep/ {
  // .el-cascader .el-icon-arrow-down{
  //   transform: rotate(180deg);
  // }
  .el-cascader .el-icon-arrow-down:before {
      content: "\E6E1";
  }
}
</style>

