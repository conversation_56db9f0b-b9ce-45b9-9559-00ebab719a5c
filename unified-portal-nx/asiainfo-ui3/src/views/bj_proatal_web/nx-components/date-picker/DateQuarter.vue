<template>
  <el-popover :visible="visible" ref="popover" popper-class="el-picker-panel el-date-picker" append-to-body>
    <el-input slot="reference" size="small" class="el-date-editor" :value="dateFormat">
      <i slot="prefix" class="el-input__icon el-icon-date"></i>
    </el-input>
    <div class="el-date-picker__header el-date-picker__header--bordered">
      <button type="button" aria-label="前一年"
              class="el-picker-panel__icon-btn el-date-picker__prev-btn el-icon-d-arrow-left"
              @click="year = year -1"></button>
      <span role="button" class="el-date-picker__header-label">{{year}} 年</span>
      <button type="button" aria-label="后一年"
              class="el-picker-panel__icon-btn el-date-picker__next-btn el-icon-d-arrow-right"
              @click="year = year + 1"></button>
    </div>
    <table class="el-month-table" style="">
      <tbody>
        <tr>
          <td v-for="qr in quarterRow"
              :key="qr.value"
              :class="{'today': curQ == qr.value,'current': date == qr.value,'disabled' : qr.disabled}"
              @click="clickCell(qr.value,qr.disabled)">
              <div><a class="cell">{{qr.label}}</a></div>
          </td>
        </tr>
      </tbody>
    </table>
  </el-popover>
</template>

<script>
import Common from 'bj_src/lib/date';

export default {
  name:'DateQuarter',
  props:['value'],
  data() {
    const today = new Date();
    const curQ = new Date(today.setMonth(today.getMonth() / 3)); // 季度Date
    return {
      curQ: Common.formatDate(curQ, 'yyyy-MM'),// 当前季度
      year: today.getFullYear(),
      date: this.value || '',
      quarters:[
        {label:'第一季度',value:'01'},
        {label:'第二季度',value:'02'},
        {label:'第三季度',value:'03'},
        {label:'第四季度',value:'04'},
      ],
      visible: false
    }
  },
  computed:{
    quarterRow() {
     return this.quarters.map(({label,value})=>{
       const [curYear,curQ] = this.curQ.split('-');
       let disabled = false;
       if(Number(curYear) <  Number(this.year)) {
         disabled = true;
       }else if(Number(curYear) > Number(this.year)){
          disabled = false;
       }else{
         disabled = (Number(value) > Number(curQ)) ? true : false;
       }
       return {label,value:`${this.year}-${value}`,disabled}
     })
    },
    dateFormat() {
      if(!this.date) return '';
      const [year,quarter] = this.date.split('-');
      return `${year}年${Number(quarter)}季度`;
    }
  },
  methods:{
    clickCell(val,disabled) {
      if(disabled) return;
      this.date = val;
      this.visible = false;
      this.$emit('input',val);
      this.$emit('change');
      // todo 修改关闭的方式
      this.$refs.popover && this.$refs.popover.doClose && this.$refs.popover.doClose();
    }
  },
  watch:{
    value(val) {
      this.date = val;
    }
  },
  created() {
  }
}
</script>

<style lang="scss" scoped>

</style>
