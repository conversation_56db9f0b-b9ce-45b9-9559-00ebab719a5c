<template>
  <el-popover :visible="visible" ref="popover" popper-class="el-picker-panel el-date-picker" append-to-body>
    <el-input slot="reference" size="small" class="el-date-editor" :value="dateFormat">
      <i slot="prefix" class="el-input__icon el-icon-date"></i>
    </el-input>
    <div class="el-date-picker__header el-date-picker__header--bordered">
      <button type="button" aria-label="前一年"
              class="el-picker-panel__icon-btn el-date-picker__prev-btn el-icon-d-arrow-left"
              @click="year = year -1"></button>
      <button type="button" aria-label="上个月"
              class="el-picker-panel__icon-btn el-date-picker__prev-btn el-icon-arrow-left"
              @click="month = month -1">
        </button>
      <span role="button" class="el-date-picker__header-label">{{year}} 年</span>
      <span role="button" class="el-date-picker__header-label">{{month + 1}} 月</span>
      <button type="button" aria-label="后一年"
              class="el-picker-panel__icon-btn el-date-picker__next-btn el-icon-d-arrow-right"
              @click="year = year + 1"></button>
      <button type="button" aria-label="下个月"
              class="el-picker-panel__icon-btn el-date-picker__next-btn el-icon-arrow-right"
              @click="month = month + 1">
              </button>
    </div>
    <table class="el-month-table" style="">
      <tbody>
        <tr>
          <td v-for="qr in quarterRow"
              :key="qr.value"
              :class="{'today': curWeek == qr.value,'current': date == qr.value,'disabled' : qr.disabled}"
              @click="clickCell(qr.value,qr.disabled)">
              <div><a class="cell">{{qr.label}}</a></div>
          </td>
        </tr>
      </tbody>
    </table>
  </el-popover>
</template>

<script>
import Common from 'bj_src/lib/date';

export default {
  name:'DateMonthWeek',
  props:['value'],
  data() {
    const today = new Date();
    const date = today.getDate();
    const year = today.getFullYear();
    const month = today.getMonth();
    // 07 14 21 月最后一天
    const weekDate = date > 7 * 3  ? new Date(year, month + 1 ,0).getDate() : (date / 7 + 1 ) * 7;
    const curWeek = new Date(year, month, weekDate); //
    return {
      curWeek: Common.formatDate(curWeek, 'yyyy-MM-dd'),// 当前季度
      year,
      month,
      date: this.value || '',
      quarters:[
        {label:'第一周'},
        {label:'第二周'},
        {label:'第三周'},
        {label:'第四周'},
      ],
      visible: false
    }
  },
  computed:{
    quarterRow() {
     return this.quarters.map(({label},index)=>{
       const dateNum = index < 3 ? (index + 1) * 7 : new Date(this.year, this.month + 1, 0).getDate();
       const date = new Date(this.year, this.month, dateNum);
       const curDateTime = new Date(this.curWeek.replace(/-/g,'/')).getTime();
       const disabled = curDateTime < date.getTime() ? true : false;
       return {label,value:Common.formatDate(date, 'yyyy-MM-dd'),disabled}
     })
    },
    dateFormat() {
      if(!this.date) return '';
      const [year,month,date] = this.date.split('-');
      const {label} = this.quarters[Math.min(date / 7 - 1,3)] || {};
      return `${year}年${month}月${label}`;
    }
  },
  methods:{
    clickCell(val,disabled) {
      if(disabled) return;
      this.date = val;
      this.visible = false;
      this.$emit('input',val);
      this.$emit('change');
      // todo 修改关闭的方式
      this.$refs.popover && this.$refs.popover.doClose && this.$refs.popover.doClose();
    }
  },
   watch:{
    value(val) {
      this.date = val;
    }
  },
  created() {
  }
}
</script>

<style lang="scss" scoped>

</style>
