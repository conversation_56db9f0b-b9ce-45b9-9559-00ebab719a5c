import Vue from 'vue';
const permission = (el, binding, vnode) => {
  const perDom = el.querySelector('[data-permission]');
  if(perDom) {
    el.removeChild(perDom);
  }
  // 如果没有地图权限
  if(binding.value === false) {
    el.style.position = "relative";
    const watermarkDiv = document.createElement('div');
    watermarkDiv.innerHTML =`<img src="${require('@/assets/images/empty.png')}" style="width:10%"><br><span>抱歉，暂无权限</span>`;
    watermarkDiv.setAttribute('data-permission', binding.value);
    watermarkDiv.setAttribute('style',`
    position:absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background:#ffffff;
    display:flex;
    flex-direction: column;
    font-size: 16px;
    color:#898989;
    font-weight: bold;
    align-items:center;
    justify-content: center;
    z-index: 10;`
    )
    el.appendChild(watermarkDiv)
  }
}

Vue.directive('permission',{
  inserted: permission,
  update: permission
});
