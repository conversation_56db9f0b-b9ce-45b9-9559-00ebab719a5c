<template>
  <div class="row-center captcha_input_wrapper">
    <input
      v-for="(item,index) in captchas"
      :id="'captcha'+index"
      :key="index"
      v-model="item.num"
      class="captcha_input_box row-center"
      type="tel"
      maxlength="1"
      @input="inputFinash(index)"
      @focus="adjust(index)"
      @keydown="inputDirection(index)"
    >
  </div>
</template>
<script>
export default {
  data() {
    return {
      // 当前输入框
      activeInput: 0,
      captchas: [
        { num: '' },
        { num: '' },
        { num: '' },
        { num: '' },
        { num: '' },
        { num: '' }
      ]
    }
  },

  methods: {
    // 自动校准输入顺序
    adjust(index) {
      const dom = document.getElementById('captcha' + this.activeInput)
      if (index !== this.activeInput && dom) {
        dom.focus()
      }
    },
    // 控制前后方向
    inputDirection(index) {
      const val = this.captchas[index].num
      // 回退键处理
      if (event.keyCode == 8 && val == '') {
        // 重新校准
        const dom = document.getElementById('captcha' + (index - 1))
        this.activeInput = index - 1
        if (dom) dom.focus()
      }
      if (event.keyCode != 8 && val != '') {
        const dom = document.getElementById('captcha' + (index + 1))
        this.activeInput = index + 1
        if (dom) dom.focus()
      }
    },
    // 输入框相互联动
    inputFinash(index) {
      const val = this.captchas[index].num
      const code = this.captchas.map((x) => x.num).join('')
      this.$emit('finish', code)
      this.activeInput = val ? index + 1 : index - 1
      const dom = document.getElementById('captcha' + this.activeInput)
      if (dom) dom.focus()
      if (index == this.captchas.length - 1) {
        if (code.length == 6) {
          // const code = this.captchas.map((x) => x.num).join('')
          // this.$emit('finish', code)
        }
      }
    }
  }
}

</script>
<style lang='scss'>
.row-center {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.captcha_input_wrapper {
  width: 100%;
}
.captcha_input_box {
  width: 40px;
  height: 48px;
  margin-right: 12px;
  background: rgba(255, 255, 255, 1);
  border-radius: 6px;
  border: 1px solid #dddddd;
  font-size: 18px;
  text-align: center;
  color: #1e243a;
}
</style>

