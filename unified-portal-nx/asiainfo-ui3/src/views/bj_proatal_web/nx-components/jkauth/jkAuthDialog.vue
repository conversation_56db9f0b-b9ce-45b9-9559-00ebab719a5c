<template>
  <el-dialog title="" width="55%" class="msgdialog" :visible.sync="jkDialogVis" @close="closeDialog">
    <JK v-if="jkDialogVis" @closeJKdialog="closeDialog" />
  </el-dialog>
</template>

<script>
import J<PERSON> from './index.vue'
export default {
  name: 'JKDialog',
  components: {
    JK
  },
  props: {
    visibleKey: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      jkDialogVis: this.visibleKey
    }
  },
  watch: {
    visibleKey: {
      deep: true,
      handler(v) {
        this.jkDialogVis = v
      }
    }
  },
  methods: {
    closeDialog() {
      this.$emit('closeJKdialog')
    }
  }
}
</script>

<style>

</style>
