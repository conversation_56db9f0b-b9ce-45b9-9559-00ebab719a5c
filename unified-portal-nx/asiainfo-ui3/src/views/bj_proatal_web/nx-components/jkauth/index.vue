<template>
  <div>
    <div style="font-weight:bold;font-size:22px;text-align:center;margin-bottom:40px">金库认证</div>
    <el-form v-show="formshow" ref="form" :rules="rules" :model="form" label-width="100px">
      <el-form-item label="申请理由" prop="applyReason">
        <!-- <el-input style="width:192.5px" v-model="form.applyReason"></el-input> -->
        <!-- <el-input
          type="textarea"
          style="width:300px"
          :rows="2"
          placeholder="请输入申请理由"
          v-model="form.applyReason">
        </el-input> -->
        <el-select v-model="form.applyReason" placeholder="请选择申请原因名称">
          <el-option value="测试环境部署" label="测试环境部署" />
          <el-option value="数据稽核及提取" label="数据稽核及提取" />
          <el-option value="变更测试及验证" label="变更测试及验证" />
          <el-option value="割接测试配合" label="割接测试配合" />
          <el-option value="调账申请处理" label="调账申请处理" />
          <el-option value="应用优化变更" label="应用优化变更" />
          <el-option value="安全漏洞及基线加固" label="安全漏洞及基线加固" />
          <el-option value="系统故障定位及解決" label="系统故障定位及解決" />
          <el-option value="客户投诉查询及处理" label="客户投诉查询及处理" />
          <el-option value="系统割接及测试" label="系统割接及测试" />
          <el-option value="维护处理及巡检" label="维护处理及巡检" />
          <!-- <el-option value="其他" label="其他" /> -->
        </el-select>
        <!-- <el-select v-model="form.applyReason" placeholder="请选择活动区域">
          <el-option value="AR000" label="前台报表下载" />
          <el-option value="AR001" label="前台客户信息查询" />
          <el-option value="AR002" label="前台汇总信息查询" />
          <el-option value="AR003" label="前台关键指标查询" />
          <el-option value="AR004" label="后台敏感数据表查询" />
          <el-option value="AR005" label="后台非敏感数据表查询" />
          <el-option value="AR005" label="后台敏感数据表更新" />
          <el-option value="AR007" label="后台非敏感数据表更新" />
          <el-option value="AR008" label="后台敏感数据表删除" />
          <el-option value="AR009" label="后台非敏感数据表删除" />
        </el-select> -->
      </el-form-item>
      <el-form-item label="协同人" required prop="username">
        <el-select  style="width:300px" v-model="form.username" placeholder="请选择协同人">
          <el-option v-for="(i) in pArr" :key="i" :label="i" :value="i" />
        </el-select>
      </el-form-item>
      <el-form-item label="认证模式">
        <el-select  style="width:300px" v-model="form.authenMode" placeholder="请选择认证模式">
          <el-option value="password" label="静态密码"></el-option>
          <el-option value="smscha" label="短信"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="授权方式">
        <span :key="index" @click="tabsClick(el)" v-for="el,index in timeModeList" :class="form.timeMode === el.value? 'timeMode-selected': 'timeMode'">{{el.name}}</span>
      </el-form-item>
      <!-- <el-form-item label="授权方式">
        <el-radio-group v-model="form.method">
          <el-radio label="1">一次性授权</el-radio>
          <el-radio label="2" :disabled="authenIsAllowTime==false">时间段授权<span style="color:red">(时间段最大值1小时)</span></el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item v-show="form.method == '2'" label="授权时间段">
        <el-col :span="6">
          <el-form-item prop="startDate">
            <el-date-picker v-model="form.startDate" type="datetime" placeholder="开始时间" style="width: 100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="1" style="text-align:center">至</el-col>
        <el-col :span="6">
          <el-form-item prop="endDate">
            <el-date-picker v-model="form.endDate" type="datetime" placeholder="结束时间" style="width: 100%;" />
          </el-form-item>
        </el-col>

      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="sendVaultAuthSecond">提交</el-button>

      </el-form-item>

    </el-form>

    <!-- 金库第三次认证 本地 -->
    <el-form v-show="form3LocalShow" ref="form3Local" :model="form3Local" label-width="100px">
      <el-form-item v-show="arbLenShow" :label="chaTypeTxt">
        <el-input v-model="form3Local.password" style="width:400px" type="password" autocomplete="new-password" />
      </el-form-item>
      <el-form-item v-show="!arbLenShow" :label="chaTypeTxt">
        <INPUTPASSWORD v-if="form3LocalShow" ref="pwComponentL" @finish="inputPsdFinishLocal" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="onSubmitLocal">提交</el-button>

      </el-form-item>
    </el-form>

    <!-- 金库第三次认证 远程 -->
    <el-form v-show="form3RemoteShow" ref="form3Remote" :model="form3Remote"  label-position="top" >
      <el-form-item :label="remoteLabel">
                <INPUTPASSWORD v-if="form3RemoteShow" ref="pwComponentR" @finish="inputPsdFinishRemote" />
      </el-form-item>
      <el-form-item style="text-align:center">
        <el-button type="primary" size="small" @click="onSubmitRemote" >提交</el-button>

      </el-form-item>
    </el-form>
    <div v-show="successShow" style="color:green;font-size:22px;padding-left:100px;height:150px;font-weight:bold">
      金库认证成功！
      <span>{{this.downurl?'':'重新点击即可下载文件了！'}}</span>
    </div>

  </div>
</template>

<script>
import { vaultAuthFirst, vaultAuthThirdLocal, vaultAuthSecond, vaultAuthThirdRemote } from '@/api/jkAuth/index'
import INPUTPASSWORD from './psdInput.vue'
import { eventBus } from '@/main.js'

export default {
  components: { INPUTPASSWORD },
  data() {
    return {
      rules:{
        applyReason: { required: true, message: '请填写申请理由' },
        username:{required: true, message: '请选择协同人'}
        },

      // 协同人
      pArr: [],
      // 认证模式
      authArr: [],
      // 是否允许阶段性授权
      authenIsAllowTime: true, // false 不允许阶段授权 true 允许阶段授权
      form: {
        authenMode: 'smscha',
        username: '',
        applyReason: '',
        timeMode:'1',
        method: '1',
        startDate: (new Date()).getTime(),
        endDate: (new Date()).getTime() + Number(3600 * 1000)
      },
      chaTypeTxt: '',
      form3Local: {
        password: ''
      },
      arbLenShow: false,
      // 远程
      form3Remote: {
        password: ''
      },
      remoteLabel: '',
      // 认证成功显示文本
      successShow: false,
      formshow: true,
      form3LocalShow: false,
      form3RemoteShow: false,
      obj: null,
      cacheSecondAuthParam:{},//第二步验证的时候保存参数 第三步的时候要用到
      downurl:'',
      timeModeList:[{name:'此次有效',value:'0'},{name:'30分钟内有效',value:'1'}]
    }
  },
  watch:{
    successShow(v,olv){
      if(v){
        console.log('aaaaaa')
        const self=this;
        setTimeout(()=>{
        console.log('bbbbbb')
           self.$emit('closeJKdialog')
        },3000)


      }
    }
  },
  mounted() {


    //  const self=this;
    //     setTimeout(()=>{
    //     console.log('bbbbbb')
    //        self.$emit('closeJKdialog')
    //     },3000)




    console.log('url完整地址=》',window.location.hash)
    let hashstring = window.location.hash
     if (hashstring) {
      const temparr = hashstring.split('?')
       if (temparr && temparr[1]) {
        const pa = this.getRequest(temparr[1])
        console.log('pa==>',pa)
        // pa.token = 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImU5YmE0MzQ0LWFkOTctNDlkMS04NDNmLTk1MjYwNjE1NGZjOSJ9.o_-89eutaqDEwjFhdiZwym2_QwOF3QbqDvnDgjbR5VRHUWhzh40BeggSl_GPleQGufbo868dI2v7Sx1tbvzONg'
        this.downurl = pa.downurl
        console.log('downurl=>',this.downurl);
      }
     }
    this.firstAuth()
  },
  methods: {
     getRequest(strs) {
      const theRequest = {}
      const arr = strs.split('&')
      arr.forEach(i => {
        document.cookie = i
        const arr2 = i.split('=')
        theRequest[arr2[0]] = arr2[1]
      })
      return theRequest
    },
    inputPsdFinishLocal(code) {
      this.form3Local.password = code
    },
    inputPsdFinishRemote(code) {
      this.form3Remote.password = code
    },

    sendVaultAuthSecond() {
      const { form, obj } = this
      const { authenMode, username, applyReason,timeMode, startDate, endDate, method } = form
      if (!applyReason) {
        this.$message.error('请选择申请理由!')
        return
      }
      if (!username) {
        this.$message.error('请选择协同人!')
        return
      }
      if (!timeMode) {
        this.$message.error('请选择授权方式!')
        return
      }
      if (!authenMode) {
        this.$message.error('请选择认证模式!')
        return
      }
      const param = {
        applyReason,
        username,
        authenMode,
        sessionId: obj.sessionId
      }
      this.cacheSecondAuthParam = param;
      if (method == '2') { // 时间段授权
        if (!startDate) {
          this.$message.error('请选择开始时间!')
          return
        }
        if (!endDate) {
          this.$message.error('请选择结束时间!')
          return
        }
        if (startDate > endDate) {
          this.$message.error('开始时间大于结束时间!')
          return
        }
        if (endDate - startDate > 3600000) { // 授权时间超过1小时
          this.$message.error('时间段间隔超过1小时!')
          return
        }
        param.startDate = startDate
        param.endDate = endDate
      }



      vaultAuthSecond(param).then(data => {

        const { code } = data
        let {resultType, resultCode, resultMsg,authenMode} = data.data;
        let obj = data.data;

        if (code == 200) {
          switch (resultType) {
            case 3:
              if (resultCode != 'CAS000') {
                this.$message.error(resultMsg)
                return
              } else {
                this.formshow = false
                this.successShow = true

                //  金库认证成功开始下载文件
              }
              break
            case 7:
              if (obj.username && obj.username.length > 0) {
                this.pArr = obj.username.split('@@')
              }
              if (obj.authenMode && obj.authenMode.length > 0) {
                this.authArr = obj.authenMode.split(',')
              }
              if (obj.authenIsAllowTime != 'AllowTime') {
                // 不允许时间段授权
                this.authenIsAllowTime = false
              } else {
                this.authenIsAllowTime = true
              }
              this.formshow = true
              break
            case 8:
              this.formshow = false
              this.form3LocalShow = true
              this.arbLenShow = true
              if(authenMode == 'password') this.chaTypeTxt = '静态密码：'
              if(authenMode == 'smscha') this.chaTypeTxt = '短信验证码：'

              break
            case 9:
              this.formshow = false
              this.form3RemoteShow = true
              var pInfo = obj.username.split(',')
              this.remoteLabel = `正在等待协同人 ${pInfo[1]} 回复，手机号是：${pInfo[2]}`
              break
            default:
              break
          }
        } else {
          this.$message.error('金库认证异常，请刷新后重试！')
        }
      }).catch(err => {
        console.log(err)
        this.$message.error('金库认证异常，请刷新后重试！')
      })
    },
    // 第一次金库认证
    firstAuth() {
      vaultAuthFirst().then(res => {
        const { code, data, msg } = res
        if (code == 200) {
          this.checkVaultAuth(data)
          this.obj = data || null
        } else {
          this.$message.error('金库认证异常，请刷新后重试！')
        }
      }).catch(err => {
        this.$message.error(err)
      })
    },

    checkVaultAuth(obj) {
      switch (obj.resultType) {
        case -1:
          this.$message.error(obj.resultMsg)
          break
        case 3:
          if (obj.resultCode != 'CAS000') { // 认证失败
            this.$message.error(obj.resultMsg)
            return
          } else { // 认证成功 开始下载
            this.$message.success('金库认证成功！')
            this.formshow = false
            this.successShow = true
          }
          break
        case 7:

          if (obj.username && obj.username.length > 0) {
            this.pArr = obj.username.split('@@')
          }
          if (obj.authenMode && obj.authenMode.length > 0) {
            this.authArr = obj.authenMode.split(',')
          }
          if (obj.authenIsAllowTime != 'AllowTime') {
            // 不允许时间段授权
            this.authenIsAllowTime = false
          } else {
            this.authenIsAllowTime = true
          }
          this.formshow = true
          break
        case 8:
          this.formshow = false
          this.form3LocalShow = true
          this.chaTypeTxt = obj.chaType
          if (obj.authenMode == 'C200token') {
            // 动态令牌，显示6位置文本框
            this.arbLenShow = false
          } else {
            this.arbLenShow = true
          }
          break
        case 9:
          this.formshow = false
          this.form3RemoteShow = true
          var pInfo = obj.username.split(',')
          this.remoteLabel = `正在等待协同人 ${pInfo[1]} 回复，手机号是：${pInfo[2]}`
          break
        default:
          break
      }
    },
    // 本地认证
    onSubmitLocal() {
      const { form3Local, obj,cacheSecondAuthParam } = this
      const password = form3Local.password
      console.log('22222')
      // 验证
      if (obj && obj.authenMode == 'C200token' && form3Local.password.length == 6) { // 6个位置文本框
        this.$message.error(`请填写正确的${obj.chaType}!`)
        return
      } else if (!form3Local.password) {
        let txt = '';
        if(cacheSecondAuthParam.authenMode == 'password') txt = '静态密码'
         if(cacheSecondAuthParam.authenMode == 'smscha') txt = '短信挑战'
        this.$message.error(`请填写${txt}!`)
        return
      }
      const param = {
        username: cacheSecondAuthParam.username,
        password: password,
        authenMode: cacheSecondAuthParam.authenMode,
        sessionId: cacheSecondAuthParam.sessionId,
        timeMode: this.form.timeMode,
      }
      vaultAuthThirdLocal(param).then(data => {


        if (data.code == '200') {
          const objx = data.data
          if (objx.resultType == '3' && objx.resultCode != 'CAS000') {
            this.$message.error(objx.resultMsg)
            return
          } else { // 金库认证成功
            this.form3LocalShow = false
            this.successShow = true

            if(this.downurl){
              window.location.href = decodeURIComponent(this.downurl)
            }else{

            }

          }
        } else {
          this.$message.error('金库认证异常，请刷新后重试！')
        }
      }).catch(err => {
        this.$message.error(err)
      })
    },
    // 远程认证
    onSubmitRemote() {
      const { form3Remote, obj } = this
      const { password } = form3Remote
      console.log('1111')
      if (password == '' || password.length != 6) {
        // this.$message.error('请填写正确的' + obj.chaType)
        this.$message.error('请填写正确的验证码' )

        return
      }
      const param = {
        username: obj.username,
        password: password,
        authenMode: obj.authenMode,
        sessionId: obj.sessionId,
        onlyKey: obj.onlyKey,
        timeMode: this.form.timeMode,
      }
      vaultAuthThirdRemote(param).then(data => {
        const { code, resultCode, resultType, resultMsg } = data
        if (code == '200') {
          if (resultType == '3' && resultCode != 'CAS000') {
            this.$message.error(resultMsg)
            return
          } else {
            this.successShow = true
            // 认证成功 开始下载
          }
        } else {
          this.$message.error('金库认证异常')
        }
      }).catch(err => {
        console.log(err)
        this.$message.error('金库认证异常!')
      })
    },
    tabsClick(el){
      this.form.timeMode = el.value
    }

  }

}
</script>

<style lang="scss" scoped>
.timeMode{
    margin-right: 10px;
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #FFFFFF;
    border: 1px solid #DCDFE6;
    border-color: #DCDFE6;
    color: #606266;
    text-align: center;
    box-sizing: border-box;
    font-weight: 400;
    padding: 10px 20px;
    font-size: 14px;
    border-radius: 4px;
}
.timeMode-selected{
  @extend.timeMode;
    border-color: #F19733;
    color: #F19733;
}
</style>
