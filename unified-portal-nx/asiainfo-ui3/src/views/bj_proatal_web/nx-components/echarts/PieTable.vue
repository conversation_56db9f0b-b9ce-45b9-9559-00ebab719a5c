<template>
  <div class="pie-table">
    <div class="pie" />
    <table class="pie-legend">
      <tr class="pie-head">
        <td v-for="cl in legendColumn" :key="cl.field">{{ cl.label }}</td>
      </tr>
      <tr v-for="(dt,index) in data" :key="dt.targetid">
        <!-- 只有第一列才可以点击 -->
        <td
          v-for="(cl,idx) in legendColumn"
          :key="cl.field"
          :style="idx == 0 ? 'text-align:left; cursor: pointer;': ''"
          @click="idx == 0 ? handleLegendItem(dt) : null"
        >
          <i v-if="idx == 0" :style="{background: color[index]}" />
          {{ dt[cl.field] }}{{ dt[cl.field] == '--' ? '' : cl.unit||'' }}
        </td>
      </tr>
    </table>
    <!-- 空数据显示 -->
    <Blank2 v-if="!data.length" style="position:absolute;"/>
  </div>
</template>

<script>
export default {
  name: 'PieTable',
  props: {
    /**
     * 系列名称
     */
    seriesName: String,
    /**
     * 地图数据
     */
    data: {
      type: Array,
      default: () => ([
        { score: 1048, targetname: 'Search Engine', partrate: '65', momrate: '45' },
        { score: 735, targetname: 'Direct', partrate: '25', momrate: '5' },
        { score: 580, targetname: 'Email', partrate: '35', momrate: '4' },
        { score: 484, targetname: 'Union Ads', partrate: '34', momrate: '-5' },
        { score: 300, targetname: 'Video Ads', partrate: '3', momrate: '5' }
      ])
    },
    /**
     * 表格字段配置
     * [
     *  {label: table表头文字，field: 取值字段}
     * ]
     */
    legendColumn: {
      type: Array,
      default: () => ([
        { label: '公告类型', field: 'targetname' },
        { label: '公告次数', field: 'score' },
        { label: '占比', field: 'partrate', unit: '%' },
        { label: '环比', field: 'momrate', unit: '%' }
      ])
    },
    /**
     * 地图数据，name，value 字段对应
     */
    fieldMap: {
      type: Object,
      default: () => ({
        name: 'targetname',
        value: 'score'
      })
    }
  },
  data() {
    return {
      chart: null,
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
    }
  },
  watch: {
    data() {
      this.setOption()
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    handleLegendItem(dt) {
      this.$emit('click-legend', dt)
    },
    initChart() {
      this.chart = this.$echarts.init(this.$el.querySelector('.pie'))
      this.setOption()
    },
    setOption() {
      const option = {
        color: this.color,
        // tooltip: {
        //   trigger: 'item'
        // },
        series: [
          {
            name: this.seriesName,
            type: 'pie',
            radius: ['55%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            labelLine: {
              show: false
            },
            emphasis:{
              label:{
                show:true,
                // position:'center',
                formatter:({name,value})=>{
                  return `{a|${name}}\n{b|${value}}`
                },
                rich:{
                  a:{
                    color:'#595959',
                    fontSize: 14,
                    lineHeight: 18
                  },
                  b:{
                    color:'#262626',
                    fontSize: 24
                  }
                }
              }
            },
            data: this.data.map((item) => {
              item.name = item[this.fieldMap.name]
              item.value = Number(item[this.fieldMap.value])
              return item
            })
          }
        ]
      }
      this.chart && this.chart.setOption(option, true)
    }
  }
}
</script>

<style lang="scss" scoped>
.pie-table{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding-right: 20px;
  position: relative;

  .pie{
    width: 50%;
    height: 100%;
  }

  table{
    width: 50%;
    border-collapse: collapse;
    margin-top: -50px;
    td{
      font-size: 14px;
      padding: 10px;
      text-align: center;
      color: #606266;
    }
    i {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 12px;
      vertical-align: middle;
      margin-right: 5px;
    }
    .pie-head{
      td {
        color: #7F7F7F;
      }
    }
  }
}
</style>
