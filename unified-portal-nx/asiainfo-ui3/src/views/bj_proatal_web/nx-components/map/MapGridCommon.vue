<template>
  <div class="map">
    <div class="map-echarts-box">
      <div class="map-echarts-header">
        <!-- 地图名称 -->
        <div v-if="title" class="map-title">{{ title }}</div>
        <!-- 地图层级显示 -->
        <div class="map-echarts-group" :style="{ visibility: mapGroup.length > 1 ? 'visible' : 'hidden' }">
          <template v-for="(mp, index) in mapGroup">
            <span :key="mp.name" :class="{ active: mp.name === selectedMapItem.name }" @click="getMap(mp, index)">
              {{ mp.name }}
            </span>
            <template v-if="index < mapGroup.length - 1"> > </template>
          </template>
        </div>
      </div>
      <!-- 地图 -->
      <div class="map-echarts" />
    </div>
    <!-- 右侧地图名次显示 -->
    <div v-if="showMapList && showDisRank " class="map-list" :style="{'visibility': mapData.length ? 'visible' : 'hidden'}">
      <span>{{ rankMapItem.name }}总览<br>排名</span>
      <ul>
        <li v-for="(dt, index) in mapData" :key="dt.id">
          <!-- 前3名数据显示背景不一致，scatter显示的图标也不一致 -->
          <i
            :style="{ background: index < 1 ? rankColors[0] :index < 3 ? rankColors[1]:rankColors[2] }"
            :class="{'scatter': Object.keys(scatterGeo).includes(dt.name)}"
          >
            {{ index + 1 }}
          </i>
          {{ dt.name }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
/**
 * 空值数组
 */
const EMPTY_VALURE_ARRAY = [null, undefined, '', '-']

export default {
  name: 'Map',
  inject: ['mapPermission'],
  props: {
    /**
     * 地图名称
     */
    title: {
      type: String
    },
    /**
     * 数据系列名称
     */
    activeName: {
      type: String,
      default: () => '业务异常办理渠道量'
    },
    /**
     * 地图数据，格式：
     * [{
     *  name:'xxxxx',
     *  value: 'xxxxxx'
     * }]
     */
    mapData: {
      type: Array,
      default: () => [
        { name: '固原市', value: 20057.34, cityid: '640400' },
        { name: '石嘴山市', value: 15477.48, cityid: '640200' },
        { name: '银川市', value: 31686.1, cityid: '640100' },
        { name: '中卫市', value: 6992.6, cityid: '640500' },
        { name: '吴忠市', value: 44045.49, cityid: '640300' }
      ]
    },
    /**
     * 地图分级颜色，格式：
     * ['#ffffff'(前3名的颜色),'#000000'(后续名次的颜色)]
     */
    rankColors: {
      type: Array,
      default: () => (['#009C1B', '#D1ECD4'])
    },
    /**
     * 是否显示右侧名次列表
     */
    showMapList: {
      type: Boolean,
      default: true
    },
    /**
     * 地图的自定义配置，属性按照echarts的api来
     */
    mapOption: {
      type: Object,
      default: () => ({})
    },
    /**
     * 地图可到达的最大层级
     */
    mapMaxLevel: {
      type: Number,
      default: 3
    },
    /**
     * 地图tooltip配置，不配置默认取值name,seriesName,value,seriesType
     * [
     *  {
     *   label:'xxxxxx',  数据系列文字
     *   labelField:'xxxxx',  数字文字取值字段，若无值则取label显示
     *   field: '#####', 数据显示字段
     *   decimal: 2, 保留几位小数，没有改字段就不处理小数
     *   unit:'%' 数据单位
     *   },
     * ]
     *
     * 如果tooltip配置成布尔值：
     * false： 不显示tooltip
     * true： 默认取值name,seriesName,value,seriesType
     */
    tooltip: {
      type: [Array, Boolean],
      default: true
    },
    /**
     * 是否需要宁东县和三营区 默认true
     */
    ningdongAndSanyingVis: {
      type: Boolean,
      default: true
    },
    canXZ:{
      type:Boolean,
      default:true
    }
  },
  data() {
    return {
      chart: null,
      currentMapName: '宁夏', // 加载的地图名称
      mapGroup: [{ name: '宁夏', mapLevel: 1, cityId: '640000', id: '640000', cityid: '640000' }],
      mapGroup2: [{ name: '宁夏2', mapLevel: 1, cityId: '640000', id: '640000', cityid: '640000' }],
      mapJson: {
        宁夏: require('./json/ningxia.json'),
        宁夏2: require('./json/ningxia2.json'),
        固原市: require('./json/guyuan.json'),
        石嘴山市: require('./json/shizuishan.json'),
        银川市: require('./json/yinchuan.json'),
        中卫市: require('./json/zhongwei.json'),
        吴忠市: require('./json/wuzhong.json'),
        区重客中心: require('./json/jinfengqu.json'),
        // 石嘴山
        惠农区: require('./district/huinongqu.json'),
        大武口区: require('./district/dawukouqu.json'),
        平罗县: require('./district/pingluoxian.json'),
        // 银川
        贺兰县: require('./district/helanxian.json'),
        永宁县: require('./district/yonglinxian.json'),
        兴庆区: require('./district/xingqingqu.json'),
        西夏区: require('./district/xixiaqu.json'),
        灵武市: require('./district/linwushi.json'),
        金凤区: require('./district/jinfengqu.json'),
         宁东: require('./district/nidong.json'),
         宁东县: require('./district/nidong.json'),

        // 宁东:nidong.json
        // 吴忠
        红寺堡区: require('./district/hongshibaoqu.json'),
        青铜峡市: require('./district/qingtongxiashi.json'),
        同心县: require('./district/tongxinxian.json'),
        盐池县: require('./district/yanchixian.json'),
        利通区: require('./district/litongqu.json'),
        // 中卫
        海原县: require('./district/haiyuanxian.json'),
        中宁县: require('./district/zhongningxian.json'),
        沙坡头区: require('./district/shapotouqu.json'),

        // 固原
        泾源县: require('./district/jinyuanxian.json'),
        隆德县: require('./district/longdexian.json'),
        彭阳县: require('./district/penyangxian.json'),
        原州区: require('./district/yuanzhouqu.json'),
        西吉县: require('./district/xijixian.json'),
        // 西吉县:require('./district/西吉县.json'),
          三营: require('./district/sanyin.json'), 
          三营区: require('./district/sanyin.json')

        // 三营 sanyin.json

      },
      mapStyle: {
        区重客中心: {
          silent: true,
          itemStyle: {
            areaColor: '#009C1B',
            borderColor: '#ffffff'
          }
        }
      },
      scatterGeo: {
        // 政企满意度撒点
        重客: [106.228486, 38.477353],
        区重客中心: [106.228486, 38.477353],
        党政行业一部: [106.228486, 38.477353],
        交通能源行业部: [106.27868, 38.512791],
        金融农商行业部: [106.110707, 38.327034],
        教育医卫行业部: [106.238955, 38.416906],
        党政行业二部: [106.22746, 38.605865],
        // 固原市下级撒点
        三营区: [106.158378, 36.297156],
        // 银川市下级撒点
        宁东区: [106.583146, 38.165605],
        宁东县: [106.583146, 38.165605],
        宁东镇: [106.583146, 38.165605],
        宁东: [106.583146, 38.165605]
      },
      mapZoom: 1.1
    }
  },
  computed: {
    // 排名名称
    rankMapItem() {
      // 如果用户权限地图是区县，就直接显示区县的父级名称，不再group中取获取
      console.log('this.mapPermission=>', this.mapPermission)
      console.log('this.mapGroup=>', this.mapGroup)

      if (this.mapPermission.mapLevel >= 3) {
        return { name: this.mapPermission.parentCityName }
      }
      const reverseMap = [].concat(this.mapGroup)
      reverseMap.reverse()
      for (let i = 0; i < reverseMap.length; i++) {
        const { mapLevel } = reverseMap[i]
        // if(mapLevel < 3) {
        if (mapLevel < this.mapMaxLevel) {
          return reverseMap[i]
        }
        return reverseMap[i + 1] ? reverseMap[i + 1] : {}
      }
      return {}
    },
    rankMapItemIncludeGrid() {

    },
    // 选中的数据
    selectedMapItem() {
      const [last] = [].concat(this.mapGroup).reverse()
      return last
    },
    // 筛选地图权限数据
    mapPermissionData() {
      // 筛选地图权限数据
      console.log(this.mapPermission.mapLevel, '********')
      if (this.mapPermission.mapLevel >= this.mapMaxLevel) {
        return this.mapData.filter((item) => {
          return item.cityId == this.mapPermission.cityId ||
                 item.cityid == this.mapPermission.cityid ||
                 item.cityid == this.mapPermission.cityId
        })
      }

      return this.mapData
    },
    // 区县用户不展示地图排名
    showDisRank() {
      return this.mapPermission.mapLevel < 3
    }
  },
  watch: {
    mapPermissionData() {
      this.loadMap(this.currentMapName)
    },
    canXZ(v){
      console.log('canXZ==>',v)
      if(!v){//不能下转的时候
       this.getMap(this.mapGroup2[0],0) //加载宁夏地图
      }else{
        this.getMap(this.mapGroup[0],0) 
      }
    },
    mapData(v){
       console.log('mapData==>',v)
    }
  },
  created() {
    this.setMapPermission()
  },
  mounted() {
    this.initChart()
  },
  methods: {
    setMapPermission() {
      const { mapLevel, name, parentCityName } = this.mapPermission
      // 省级和地市层级
      if (mapLevel < this.mapMaxLevel) {
        this.currentMapName = name
      } else {
        this.currentMapName = parentCityName
      }
      this.mapGroup = [{ ...this.mapPermission }]
    },
    initChart() {
      const self = this;
      const dom = this.$el.querySelector('.map-echarts')
      this.chart = this.$echarts.init(dom)
      this.chart.on('click', (params) => {
        console.log('*******',self.canXZ)
        if(!self.canXZ) return;
        /**
         * 网格不能点击
         */
        try {
          console.log('params=>', params)
          const mapLevel = params.data.mapLevel
          if (mapLevel && mapLevel == 4) {
            this.$emit('gridClick',params.data)
            return
          }
        } catch (error) {

        }

        //* ****** */

        let data = params.data || { name: params.name }
        data = { ...data, seriesType: params.seriesType }
        // 获取下级地图数据

        this.getNextMap(data)
      })
      this.loadMap(this.currentMapName)
    },
    // 获取散点数据
    getScatterData(dataLevel, parentCityId) {
      const scatterData = {}
      const scatterNames = Object.keys(this.scatterGeo)
      const mapLevel = Math.min(dataLevel, this.mapMaxLevel) // 数据层级

      this.mapPermissionData.forEach((item) => {
        if (scatterNames.includes(item.name)) {
          const scatterPoint = {
            parentCityId, // 父级节点id
            ...item,
            value: [...this.scatterGeo[item.name], Number(item.value)],
            mapLevel // 数据层级
          }
          scatterData[item.name] = scatterPoint
        }
      })
      // 银川，固原判断下级的点位是否绘制
      if (this.ningdongAndSanyingVis) {
        this.getScatterPoint(scatterData, mapLevel, parentCityId)
      }

      if (Object.keys(scatterData).length) {
        return {
          name: this.activeName,
          type: 'scatter',
          coordinateSystem: 'geo',
          symbol: 'pin',
          symbolSize: 40,
          label: {
            show: true,
            formatter: '{b}',
            position: 'top',
            textStyle: {
              color: '#FFC413',
              fontSize: 12,
              fontWeight: 'bold'
            }
          },
          itemStyle: {
            opacity: 1,
            color: '#FFC413' // 标志颜色
          },
          data: Object.values(scatterData)
        }
      }
      return null
    },
    // 判断银川下的宁东，固原市下的三营区是否撒点
    getScatterPoint(scatters, mapLevel, parentCityId) {
      if (this.currentMapName == '银川市' || this.currentMapName == '固原市') {
        const names = Object.keys(scatters)
        const pointName = this.currentMapName == '银川市' ? ['宁东县', '宁东'] : ['三营区']
        // 如果当前不包含该点位
        let point = names.find((item) => pointName.includes(item))
        if (!point) {
          [point] = pointName
          scatters[point] = {
            name: point,
            value: [...this.scatterGeo[point], null],
            mapLevel,
            parentCityId,
            itemStyle: {
              opacity: 1,
              color: '#ABC5D2' // 标志颜色
            },
            label: {
              color: '#333333',
              fontSize: 12,
              fontWeight: 'bold'
            }
          }
        }
      }
    },
    // 加载地图
    loadMap(name) {
      if (this.mapJson[name]) {
        console.log('name==>', name)
        this.$echarts.registerMap(name, this.mapJson[name])

        this.currentMapName = name
      }
      // 获取数据层级
      const [currentMap] = this.mapGroup.filter(({ name }) => name === this.currentMapName)
      console.log('currentMap==>', currentMap)
      const dataLevel = currentMap ? currentMap.mapLevel + 1 : (this.selectedMapItem ? this.selectedMapItem.mapLevel + 1 : null)
      const parentCityId = currentMap ? (currentMap.cityId || currentMap.cityid) : null
      console.log('this.mapGroup=>', this.mapGroup)
      // 注册地图
      const scatterOption = this.getScatterData(dataLevel, parentCityId)
      const option = {
        tooltip: this.tooltip === false ? null : {
          trigger: 'item',
          formatter: this.formatterToolTip
        },
        geo: {
          map: this.currentMapName,
          zoom: this.mapZoom,
          silent: true
        },

        series: [
          {
            name: this.activeName,
            type: 'map',
            map: this.currentMapName,
            zoom: this.mapZoom,
            label: {
              show: !(this.mapGroup && this.mapGroup.length == 3) // 显示区县地图时 地图上的网格中文名不显示 网格中文名堆积在一起了
            },
            silent: !this.mapPermissionData.length,
            // 自定义地图的样式
            ...(this.mapStyle[this.currentMapName] ? this.mapStyle[this.currentMapName] : {}),
            data: this.mapPermissionData.map((item, index) => {
              return {
                parentCityId, // 父级节点id
                ...item,
                mapLevel: Math.min(dataLevel, this.mapMaxLevel), // 数据层级
                itemStyle: {
                  // 前三名颜色不一致
                  areaColor: index < 1 ? this.rankColors[0] : index < 3 ? this.rankColors[1] : this.rankColors[2],
                  borderColor: '#ABC5D2'
                },
                // 判断是否是选中的数据
                selected: item.name === this.selectedMapItem.name,
                // 有数据就可以选中
                select: {
                  disabled: false
                },
                emphasis: {
                  disabled: false,
                  label: {
                    show: !((this.mapGroup && this.mapGroup.length == 3))
                  }
                }
              }
            }),
            // 默认不可以被选中，有数据的才可以被选中
            select: {
              disabled: true
            },
            emphasis: {
              disabled: true
            }
          },
          // 散点图
          (scatterOption || null)
        ],
        // 自定义echarts选项
        ...this.mapOption
      }
      this.chart && this.chart.setOption(option, true)
    },
    toFixDecimal(number, decimal = 2) {
      if (!number) return
      return isNaN(Number(number)) ? number : Number(number).toFixed(decimal)
    },
    // 设置地图tooltip
    formatterToolTip(params) {
      if (this.tooltip && Array.isArray(this.tooltip)) {
        const { name, data } = params
        if (!data) return null
        const str = [name]
        this.tooltip.forEach((item) => {
          const label = item.hasOwnProperty('labelField') ? (data[item.labelField] || item.label) : item.label
          const value = item.hasOwnProperty('decimal') ? this.toFixDecimal(data[item.field], item.decimal) : data[item.field]
          const valueUnit = value ? (EMPTY_VALURE_ARRAY.includes(value) ? '-' : `${value}${item.unit || ''}`) : '-'
          label && str.push(`${label} ${valueUnit}`)
        })
        return str.join('<br/>')
      } else {
        const { name, seriesName, value, seriesType } = params
        const val = seriesType === 'scatter' ? [...value].pop() : value
        return `${name}<br/>${seriesName} ${val || '-'}`
      }
    },
    // 获取下级地图
    getNextMap(data) {
      // 如果用户地图层级为3级区县，不用于用户点击选择其他的区县
      if (this.mapPermission.mapLevel == 3 && data.mapLevel >= this.mapPermission.mapLevel) return
      // 判断改点位是否id，没有id表明不是用户数据
      if (data.name === this.currentMapName || ((!data.hasOwnProperty('cityId')) && (!data.hasOwnProperty('cityid')))) return
      // 同级数据覆盖
      if (data.mapLevel === this.selectedMapItem.mapLevel || data.name === this.selectedMapItem.name) {
        this.mapGroup.splice(this.mapGroup.length - 1, 1, data)
      // 点击的数据小于当前的选中地图数据
      } else if (data.mapMaxLevel < this.selectedMapItem.mapLevel) {
        this.mapGroup = this.mapGroup.reduce((pre, cur) => {
          return cur.mapMaxLevel < data.mapLevel ? pre.push(cur) : (cur.mapMaxLevel == data.mapLevel ? pre.push(data) : [])
        }, [])
      } else {
        this.mapGroup.push(data)
      }
      this.$emit('map-click', data)
      console.log('data===>', data)
      if (data.mapLevel <= this.mapMaxLevel) this.loadMap(data.name)
    },
    // 点击面包屑获取地图
    getMap(mp, index) {
      console.log('mp==>',mp)
      console.log('index==>',index)



      if (mp.name === this.currentMap) return
      this.$emit('map-click', mp)
      this.loadMap(mp.name)
      this.mapGroup = this.mapGroup.slice(0, index + 1)
    },
    // 加载层级数据
    loadMapLevel(level) {
      this.mapGroup = this.mapGroup.filter(({ mapLevel }) => {
        return mapLevel <= level
      })
      const [last] = [...this.mapGroup].reverse()
      if (last) {
        this.loadMap(last.name)
        return last
      }
      return {}
    },
    // 通过名称加载下级地图
    loadNextMapByName(name) {
      const type = Object.keys(this.scatterGeo).includes(name) ? 'scatter' : 'map'
      const { series } = this.chart.getOption()
      const [{ data }] = series.filter((item) => {
        return item.type === type
      })
      const [nextMap] = data.filter((item) => {
        return item.name == name
      })
      if (nextMap) {
        this.getNextMap(nextMap)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.map {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
}
.map-echarts-box {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  .map-echarts {
    width: 100%;
    flex: 1;
    margin-top: -10px;
  }
}
.map-echarts-header{
  display: flex;
  align-items: center;
  .map-title{
    font-weight: bold;
    padding-left: 20px;
    color: #262626;
    font-size: 16px;
  }
}
.map-echarts-group {
  padding: 10px;
  font-size: 14px;
  color: rgba(127, 127, 127, 1);

  span {
    text-decoration: underline;
    cursor: pointer;

    &.active {
      color: #ffbd01;
      text-decoration: none;
      cursor: default;
    }
  }
}
.map-list {
  font-size: 14px;
  width: 160px;
  margin-top: 60px;
  > span {
    font-weight: bold;
    padding-bottom: 10px;
    display: block;
  }
  ul,
  li {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  li {
    white-space: nowrap;
    margin: 10px 0;

    i {
      display: inline-block;
      width: 20px;
      height: 20px;
      border-radius: 2px;
      text-align: center;
      line-height: 20px;
      font-style: normal;
      background: #d1ecd4;
      margin-right: 10px;
      color: #ffffff;

      &.scatter{
        position: relative;
        background: transparent!important;
        z-index: 1;
        &:after{
          position: absolute;
          width: 100%;
          height: 100%;
          content: ' ';
          left: 0;
          background: #FFC413;
          z-index: -1;
          border-radius: 50% 50% 0 50%;
          transform: rotate(45deg);
        }
      }
    }
  }
}
</style>
