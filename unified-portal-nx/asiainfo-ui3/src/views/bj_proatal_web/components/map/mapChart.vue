<!--  -->
<template>
  <div :style="{ height: height, width: width }">
    <!-- {{isChild}} -->
    <div
      :id="echartId"
      ref="lineChart"
      v-if="!isChild"
      :style="{ width: '100%', height: '100%' }"
    ></div>
    <template v-if="isChild">
      <span class="goDraw" @click="goDrawClick">返回</span>
      <div
        id="mapChildId"
        ref="mapChildChart"
        v-if="isChild"
        :style="{ width: '100%', height: '100%' }"
      ></div>
    </template>
  </div>
</template>

<script>
const childData = {
  舒兰市: [44.40586, 126.96532],
  龙潭区: [125.26665834684108, 43.88572451516208],
  昌邑区: [43.88187, 126.57436],
  船营区: [43.83395, 126.54113],
  永吉县: [43.67253, 126.49754],
  磐石市: [42.94662, 126.06046],
  桦甸市: [42.97208, 126.74627],
  丰满区: [43.82142, 126.56206],
  蛟河市: [43.72393, 127.34477],
};

import vPinYin from "./js/pinyin.js";
export default {
  props: [
    "echartId",
    "height",
    "width",
    "cData",
    "mapColor",
    "isCity",
    "isChilds",
  ],
  components: {},
  data() {
    return {
      isChild: false,
      childData,
      parentCode: "",
      worldJson: require("../../components/map/json/province/ningxia.json"), //地图json数据
      guyuan: require("../../components/map/json/city/guyuan.json"),
      shizuishan: require("../../components/map/json/city/shizuishan.json"),
      yinchuan: require("../../components/map/json/city/yinchuan.json"),
      zhongwei: require("../../components/map/json/city/zhongwei.json"),
      wuzhong: require("../../components/map/json/city/wuzhong.json"),
    };
  },
  watch: {
    isChilds(newVal, oldVal) {
      this.isChild = newVal;
      console.log("oldVal=", oldVal);
    },
    isChild() {
      // updataMapChild;
      console.log("isChild---oldVal=", this.isChild);
      if (this.isChild === false) {
        this.drawBar(this.mapColor);
      }
      this.$emit("updataMapChild", this.isChild, this.parentCode);
      // this.this.$emit(event, ${[…args]})
    },
    xData() {
      console.log(this.mapColor);
      this.drawBar(this.mapColor);
    },

    echartId(newValue, oldValue) {
      console.log(newValue, oldValue);
      if (newValue !== oldValue) {
        this.drawBar(this.mapColor);
      }
    },
  },
  computed: {},
  mounted() {
    this.drawBar(this.mapColor);
  },
  methods: {
    goDrawClick() {
      this.isChild = false;
      this.$emit("updateIsChild", "all");
      this.drawBar(this.mapColor);
      console.log("isChild=", this.isChild);
    },
    drawBar(baseColor) {
      let len = baseColor.length;

      this.$nextTick(() => {
        // 基于准备好的dom，初始化echarts实例
        const element = document.getElementById(this.echartId);
        this.$echarts.registerMap("js", this.worldJson);
        const myChart = this.$echarts.init(element);
        // 绘制图表
        var option = {
          visualMap: {
            show: false,
            max: 100,
            seriesIndex: [3],
            inRange: {
              color: baseColor,
            },
          },
          geo: [
            {
              map: "js",
              roam: false, //是否允许缩放
              zoom: 1.1, //默认显示级别
              scaleLimit: {
                min: 0,
                max: 3,
              },
            },
          ],
          series: [
            {
              type: "effectScatter",
              coordinateSystem: "geo",
              z: 5,
              data: [],
              symbolSize: 14,
              label: {
                normal: {
                  show: true,
                  position: "top",
                  backgroundColor: baseColor[0],
                  padding: [0, 0],
                  borderRadius: 3,
                  lineHeight: 32,
                  color: "#262626",
                },
                emphasis: {
                  show: true,
                },
              },
              itemStyle: {
                color: baseColor[0],
              },
            },
            {
              type: "effectScatter",
              coordinateSystem: "geo",
              z: 5,
              data: [],
              symbolSize: 14,
              label: {
                normal: {
                  show: true,
                  position: "top",
                  backgroundColor: baseColor[1],
                  padding: [0, 0],
                  borderRadius: 3,
                  lineHeight: 32,
                  color: "#262626",
                },
                emphasis: {
                  show: true,
                },
              },
              itemStyle: {
                color: baseColor[1],
              },
            },
            {
              type: "effectScatter",
              coordinateSystem: "geo",
              z: 5,
              data: [],
              symbolSize: 14,
              label: {
                normal: {
                  show: true,
                  position: "top",
                  backgroundColor: baseColor[2],
                  padding: [0, 0],
                  borderRadius: 3,
                  lineHeight: 32,
                  color: "#262626",
                },
                emphasis: {
                  show: true,
                },
              },
              itemStyle: {
                color: baseColor[2],
              },
            },
            //地图
            {
              type: "map",
              mapType: "js",
              geoIndex: -1,
              zoom: 1.1, //默认显示级别
              label: {
                show: true,
                color: "#262626",
                emphasis: {
                  color: "#262626", //移上去字体颜色
                  show: false,
                },
              },
              itemStyle: {
                normal: {
                  borderColor: "#bacaeb",
                  color: "#262626",
                  borderWidth: 0.5,
                  areaColor: baseColor[len - 1], //地图区域的颜色
                },
                emphasis: {
                  borderColor: "#bacaeb",
                  borderWidth: 0,
                  color: "#262626",
                  areaColor: baseColor[len - 1], //地图区域的颜色
                },
              },
              data: Object.keys(this.cData).map((name) => {
                return {
                  name: name,
                  parentCode: vPinYin.chineseToPinYin(name),
                  code: vPinYin.chineseToPinYin(name),
                  value: Math.random() * 50,
                };
              }),
            },
          ],
        };
        myChart.resize();
        myChart.setOption(option);
        window.addEventListener("resize", () => {
          myChart.resize();
        });
        myChart.off("click");

        let arrTemp = Object.keys(this.cData).map((name, index) => {
          return index;
        });
        let arrName = Object.keys(this.cData).map((name) => {
          return name;
        });
        // console.log(arrTemp);
        myChart.on("click", (params) => {
          // params点击事件
          console.log(params.data);
          if (this.isCity === 1) {
            //true,可以进入下级地图，false只能高亮
            this.isChild = true;
            this.parentCode = params.data.parentCode;
            this.$emit("updateIsChild", this.parentCode);
            this.selectJson(baseColor, params.name);
          } else {
            //点击高亮
            myChart.dispatchAction({
              type: "downplay",
              seriesIndex: arrTemp,
              seriesName: arrName,
            });
            myChart.dispatchAction({
              type: "highlight",
              seriesIndex: params.seriesIndex,
              name: params.name,
            });
            this.parentCode = params.data.parentCode;
            this.$emit("updateIsChild", this.parentCode);
          }
        });
      });
    },
    selectJson(baseColor, name) {
      let fileJson = null;
      if (name === "宁夏") {
        fileJson = this.worldJson;
      } else if (name === "固原") {
        fileJson = this.guyuan;
      } else if (name === "石嘴山") {
        fileJson = this.shizuishan;
      } else if (name === "吴忠") {
        fileJson = this.wuzhong;
      } else if (name === "中卫") {
        fileJson = this.zhongwei;
      } else if (name === "银川") {
        fileJson = this.yinchuan;
      }

      this.ChildDrawBar(baseColor, fileJson);
    },
    ChildDrawBar(baseColor, fileJson) {
      let len = baseColor.length;

      // let that = this;
      this.$nextTick(() => {
        // 基于准备好的dom，初始化echarts实例
        const element = document.getElementById("mapChildId");
        this.$echarts.registerMap("js", fileJson, {});
        const childMyChart = this.$echarts.init(element);
        // 绘制图表
        var option = {
          visualMap: {
            show: false,
            max: 100,
            seriesIndex: [3],
            inRange: {
              color: baseColor,
            },
          },
          geo: [
            {
              map: "js",
              roam: false, //是否允许缩放
              zoom: 1.1, //默认显示级别
              scaleLimit: {
                min: 0,
                max: 3,
              },
            },
          ],
          series: [
            {
              type: "effectScatter",
              coordinateSystem: "geo",
              z: 5,
              data: [],
              symbolSize: 14,
              label: {
                normal: {
                  show: true,
                  position: "top",
                  backgroundColor: baseColor[0],
                  padding: [0, 0],
                  borderRadius: 3,
                  lineHeight: 32,
                  color: "#262626",
                },
                emphasis: {
                  show: true,
                },
              },
              itemStyle: {
                color: baseColor[0],
              },
            },
            {
              type: "effectScatter",
              coordinateSystem: "geo",
              z: 5,
              data: [],
              symbolSize: 14,
              label: {
                normal: {
                  show: true,
                  position: "top",
                  backgroundColor: baseColor[1],
                  padding: [0, 0],
                  borderRadius: 3,
                  lineHeight: 32,
                  color: "#262626",
                },
                emphasis: {
                  show: true,
                },
              },
              itemStyle: {
                color: baseColor[1],
              },
            },
            {
              type: "effectScatter",
              coordinateSystem: "geo",
              z: 5,
              data: [],
              symbolSize: 14,
              label: {
                normal: {
                  show: true,
                  position: "top",
                  backgroundColor: baseColor[2],
                  padding: [0, 0],
                  borderRadius: 3,
                  lineHeight: 32,
                  color: "#262626",
                },
                emphasis: {
                  show: true,
                },
              },
              itemStyle: {
                color: baseColor[2],
              },
            },
            //地图
            {
              type: "map",
              mapType: "js",
              geoIndex: -1,
              zoom: 1.1, //默认显示级别
              label: {
                show: true,
                color: "#262626",
                emphasis: {
                  color: "#262626", //移上去字体颜色
                  show: false,
                },
              },
              itemStyle: {
                normal: {
                  borderColor: "#bacaeb",
                  color: "#262626",
                  borderWidth: 0.5,
                  areaColor: baseColor[len - 1], //地图区域的颜色
                },
                emphasis: {
                  borderColor: "#bacaeb",
                  borderWidth: 0,
                  color: "#262626",
                  areaColor: baseColor[len - 1], //地图区域的颜色
                },
              },
              data: Object.keys(this.childData).map((name) => {
                console.log("====nmae", name);
                return {
                  name: name,
                  parentCode: vPinYin.chineseToPinYin(
                    this.parentCode.replace("市", "")
                  ),
                  code: vPinYin.chineseToPinYin(name.replace("市", "")),
                  value: Math.random() * 50,
                };
              }),
            },
          ],
        };
        childMyChart.setOption(option);
        window.addEventListener("resize", () => {
          childMyChart.resize();
        });

        childMyChart.off("click");

        let arrTemp = Object.keys(this.childData).map((name, index) => {
          return index;
        });
        let arrName = Object.keys(this.childData).map((name) => {
          return name;
        });
        console.log(arrTemp);

        childMyChart.on("click", (params) => {
          // params点击事件
          console.log(params);
          this.$emit("updateIsChild", params.data.code);
          childMyChart.dispatchAction({
            type: "downplay",
            seriesIndex: arrTemp,
            seriesName: arrName,
          });

          childMyChart.dispatchAction({
            type: "highlight",
            seriesIndex: params.seriesIndex,
            name: params.name,
          });
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.goDraw {
  cursor: pointer;
}
</style>
