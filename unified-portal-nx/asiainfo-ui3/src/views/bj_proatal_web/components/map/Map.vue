<template>
  <div ref="chart" v-loading="loading" class="map" />
</template>

<script>
import Vue from "vue";
import * as echarts from "echarts";
import axios from "axios";
const fuhao = require("@/assets/images/fuhao.png");

/* const fuhao = require("@/assets/images/fuhao.png");
    const triagle = require("@/assets/images/triagle.png");*/

import province from "./json/province/ningxia.json";
import guyuan from "@/assets/map/guyuan.json";
import shizuishan from "@/assets/map/shizuishan.json";
import wuzhong from "@/assets/map/wuzhong.json";
import yinchuan from "@/assets/map/yinchuan.json";
import zhongwei from "@/assets/map/zhongwei.json";
import { mapData } from "./mapData.js";

import { areaData } from "@/assets/map/mapData.js";

export default {
  name: "Map",
  props: {
    drillLevel: Number,
    activeName: String,
    getData: Function,
    userInfo: Object,
    mapColor: Array,
    permissionSetting: Boolean,
    showDistrict: Boolean,
    titles: Array,
    rankList: Array,
    dealDatas: Function,
  },
  data() {
    return {
      loading: false,
      chart: null,
      thirdScore: 0,
      lastThirdScore: 0,
      upImg: require("@/assets/images/up-icon.png"),
      downImg: require("@/assets/images/down-icon.png"),
      mapJson: {
        宁夏: province,
        固原市: guyuan,
        银川市: yinchuan,
        石嘴山市: shizuishan,
        吴忠市: wuzhong,
        中卫市: zhongwei,
      },
      areaData,
      clickAreas: ["宁夏", "固原市", "银川市", "石嘴山市", "吴忠市", "中卫市"],
      mapInfo: {
        cityName: "",
        cityId: "",
        level: 1,
      },

      data: [],
    };
  },
  watch: {
    datas(val) {},
  },
  mounted() {
    this.mapInfo.cityName = !this.permissionSetting
      ? "宁夏"
      : this.userInfo.cityid == "640000"
      ? "宁夏"
      : this.userInfo.cityName;
    this.mapInfo.cityId = !this.permissionSetting
      ? "640000"
      : this.userInfo.cityid;

    // console.log(mapData);
  },
  methods: {
    // 修改title
    changeTitles() {
      this.$emit("changeTitles", {
        cityName: this.mapInfo.cityName,
        cityId: this.mapInfo.cityId,
      });
    },

    generateChart(name, datas, level) {
      const _this = this;
      if (!level || level != 3) {
        _this.loading = true;
        console.log(1345);
        // if (_this.chart && _this.clickAreas.includes(name)) {
        _this.chart && _this.chart.dispose();
        // }
        _this.chart && _this.chart.clear();
        // if (_this.clickAreas.includes(name)) {
        _this.chart = echarts.init(_this.$refs.chart, null, { render: "json" });
        window.addEventListener("resize", () => {
          _this.chart.resize();
        });
        _this.initChart(name, datas, level);
      }
      // }
    },

    initChart(name, datas, level) {
      console.log(2);
      var _this = this;
      if (level) {
        _this.mapInfo.level = level;
      }
      this.chart.on("click", (params) => {
        // console.log('this.showDistrict',this.showDistrict)
        console.log(_this.mapInfo.level, _this.drillLevel);
        if (
          _this.mapInfo.level < _this.drillLevel ||
          (_this.mapInfo.level == 3 && _this.drillLevel == 3) ||
          (_this.mapInfo.level == 2 && _this.drillLevel == 2)
        ) {
          if (this.clickAreas.includes(params.name) || this.showDistrict) {
            _this.mapInfo.cityName = params.name;
            _this.mapInfo.cityId = _this.areaData[params.name];
            if (_this.mapInfo.level == 1 || _this.mapInfo.level == 2) {
              _this.mapInfo.level = _this.mapInfo.level + 1;
            }
            _this.changeTitles();
            _this.getData(
              _this.mapInfo.cityName,
              _this.mapInfo.cityId,
              _this.mapInfo.level,
              3
            );
          }
        }
      });

      if (this.titles.length == 0) {
        this.changeTitles();
      }

      setTimeout(() => {
        if (_this.mapInfo.level != 3) {
          _this.updateMapjson(name, datas);
        }
        this.loading = false;
      }, 200);
    },

    // 更新地图json数据
    updateMapjson(name, datas) {
      console.log(3);
      echarts.registerMap("map", this.mapJson[name]);
      this.updateOption(name, datas);
    },

    updateOption(name, datas) {
      const _this = this;
      const mapDatas = JSON.parse(JSON.stringify(mapData));
      let rankList = [];
      if (datas && datas.length) {
        rankList = JSON.parse(JSON.stringify(datas));
      }
      rankList.forEach((el, index) => {
        if (rankList.length <= 3) {
          this.$set(el, "color", this.mapColor[0]);
        } else if (rankList.length > 3 && rankList.length <= 5) {
          if (index < 3) {
            this.$set(el, "color", this.mapColor[0]);
          } else {
            this.$set(el, "color", this.mapColor[2]);
          }
        } else if (rankList.length > 5) {
          if (index < 3) {
            this.$set(el, "color", this.mapColor[0]);
          } else if (index >= rankList.length - 2) {
            this.$set(el, "color", this.mapColor[2]);
          } else {
            this.$set(el, "color", this.mapColor[1]);
          }
        }
      });
      mapDatas.forEach((el) => {
        this.$set(el, "start", el.value);
        this.$set(el, "end", el.value);
        this.$set(el, "label", el.name);
        this.$set(el, "color", this.mapColor[1]);
        rankList.forEach((item) => {
          if (el.name == item.cityname) {
            this.$set(item, "start", el.value);
            this.$set(item, "end", el.value);
            this.$set(item, "value", el.value);
            this.$set(item, "label", el.name);
            this.$set(el, "color", item.color || this.mapColor[1]);
          }
        });
      });
      console.log(rankList);
      const option = {
        geo: [
          {
            map: "map",
            name: "map",
            zoom: 1.19,
            roam: false,
            silent: !this.clickAreas.includes(name),
            selectedMode: "single",
            select: {
              label: {
                show: true,
              },
            },
            itemStyle: {
              normal: {
                // areaColor: this.mapColor[1],
                borderColor: "#ABC5D2",
                borderWidth: 1,
              },

              emphasis: {
                areaColor: "#d8ffd8",
              },
            },
            zlevel: 8,
            label: {
              show: true,
              textStyle: {
                color: "#333",
                fontSize: 10,
              },
              position: "insideLeft",
              emphasis: {
                show: true,
              },
            },
          },
        ],
        dataRange: {
          x: "-1000 px", // 图例横轴位置
          y: "-1000 px", // 图例纵轴位置
          selectedMode: false,
          splitList: mapDatas,
        },
        series: [
          {
            type: "map",
            nameProperty: "map",
            geoIndex: 0,
            selectedMode: "single",
            roam: true,
            data: mapDatas,
          },
        ],
      };
      //   if (datas && datas.length && this.dealDatas) {
      //     option = this.dealDatas(option, datas);
      //   }

      this.chart&&this.chart.setOption(option);
      this.loading = false;
    },
  },
};
</script>

<style lang="less" scope>
.map {
  width: 99%;
  height: 95%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  font-family: SourceHanSansSC-Regular;
}
</style>
