<template>
  <div class="company1Cnt">
    <div class="titleCnt">
      <div>
<!--        <img src="https://ss0.bdstatic.com/94oJfD_bAAcT8t7mm9GUKT-xh_/timg?image&quality=100&size=b4000_4000&sec=1578404903&di=f08fe4745524ff74b256e7de2ce57816&src=http://pic.51yuansu.com/pic3/cover/01/45/93/593a0c224cd59_610.jpg" alt="">-->
        <span>全网对比图</span>
      </div>
    </div>
    <div id="main"></div>
    <div class="tableCnt">
      <div class="header">
        <div class="left">指标</div>
        <div class="right">
          <div v-for="(item,index) in allData" :key="index">
            {{item.indicateName}}
          </div>
          <!-- <el-col :span="2"><div class="grid-content bg-purple-dark">4G客户满意度</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">整体满意度</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">网络质量</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">语音通话质量</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">手机上网质量</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">资费质量</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">业务宣传办理</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">业务宣传</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">业务办理</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">服务水平</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">提醒服务</div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-dark">咨询投诉</div></el-col> -->
        </div>
      </div>
      <div class="body" v-for="n in 1" :key="n">
        <div class="left">排名</div>
        <div class="right">
          <div v-for="(item,index) in allData" :key="index">
            {{item.rank}}名
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import Vue from "vue";
import * as echarts from "echarts";

export default {
  data() {
    return {
      isShowNumber: true,
      indicateNameList: [],
      mobileValueList: [],
      highestValueList: [],
      allData: {},
    };
  },
  props: ['barnumIsShow', 'data'],
  watch: {
    barnumIsShow(newData) {
      this.isShowNumber = newData;
      this.initChart();
      // console.log(newData)
    },
  },
  mounted() {
    this.allData = this.data;
    const indicateNameList = [];
    const mobileValueList = [];
    const highestValueList = [];
    this.data.forEach((item) => {
      indicateNameList.push(item.indicateName);
      mobileValueList.push(item.mobileValue);
      highestValueList.push(item.highestValue);
    });

    this.indicateNameList = indicateNameList;
    this.mobileValueList = mobileValueList;
    this.highestValueList = highestValueList;
    this.initChart();

    console.log(this.allData, 'fsafsdfdfsaf');
  },
  methods: {
    initChart() {
      const myChart = echarts.init(document.getElementById('main'));

      myChart.setOption({
        legend: {
          data: ['移动表现值', '全网表现最高值'],
          selectedMode: false, // 是否允许点击
          top: 0,
          right: '4%'
        },
        color: ['#3398DB'],
        tooltip: {
          trigger: 'axis',
          axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },

        xAxis: {
          axisLine: {
            lineStyle: {
              color: '#E0E0E0',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#8c8c8c',
            interval: 0
          },
          data: this.indicateNameList,
        },
        yAxis: {
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#ccc',
              type: 'dashed',
            },
          },


          //scale: true,
        },

        series: [
          {
            name: '移动表现值',
            type: 'bar',
            label: {
              show: this.isShowNumber,
            },
            barWidth: '30',
            // barMaxWidth: '20%',
            smooth: true,
            data: this.mobileValueList,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(71, 184, 255, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(97, 135, 255, 1)',
                  },
                ]),
                label: {
                  show: this.barnumIsShow,
                  position: 'top',
                  formatter(p) {
                    return p.value;
                  },
                },
              },
            },
          },
          {
            name: '全网表现最高值',
            type: 'line',
            color: '#FFAE54',
            label: {
              show: this.isShowNumber,
              position: 'top',
            },
            smooth: true,
            data: this.highestValueList,

          },
        ],
      });


      window.addEventListener("resize", ()=>{
          myChart.resize();
      });



    },
  },
};
</script>

<style lang='less'>
  .company1Cnt{
    background:#fff;

    padding-bottom: 20px;
    .titleCnt{
      background:#fff;
      font-family: SourceHanSansSC-Regular;
      font-size: 16px;
      color: #262626;
      height:40px;
      padding: 12px 20px;
      height: auto;
      text-align: left;
      position: relative;\
      img{
        width:20px;
        margin-top:10px;
        position: absolute;
        left:20px;
      }
    }
    #main{
      width:100%;
      height:300px;
    }
    .tableCnt{
      margin-top:20px;
      padding:0 40px 15px 40px;
      box-sizing: border-box;
      .header,.body{
        // width:100%;
        height:40px;
        line-height:40px;
        background:#f7f7f7;
        border-bottom:1px solid #e6e6e6;
        text-align: center;
        font-size: 13px;
        >.left{
          width:5%;
          float: left;
          border-right:1px solid #e6e6e6;
          border-left:1px solid #e6e6e6;
          box-sizing: border-box;
        }
        >.right{
          width:95%;
          float: right;
          display: flex;
          >div{
            flex: 1;
            border-right:1px solid #e6e6e6;
          }
        }
      }
      .header{
        border-top:1px solid #e6e6e6;
      }
      .body{
        background:#fff;
        color:#8c8c8c;
      }
    }


  }
</style>
