<template>
    <div class="exp-axis">
        <el-scrollbar>
        <!--<div class="exp-steps" :class="{'exp-steps-active':data[0].active==1}">
            <div class="step-title">发现</div>
            <div class="step-axis">
                <div class="step-icon"></div>
                <div class="step-line"></div>
            </div>
        </div>
       <div class="exp-steps-active" :class="{'exp-steps-active':data[1].active==1}">
            <div class="step-title">评估</div>
           <div class="step-axis">
               <div class="step-icon"></div>
               <div class="step-line"></div>
           </div>
       </div>-->
        <template v-for="(item,i) in expTitle" v-if="!!data">
            <div class="exp-con" :class="{'exp-steps-active':data[i].active==1,'exp-steps':data[i].active==0}">
                <!--历程名-->
                <div class="step-title"
                     :class="{'step-title-active':data[i].active==1,'step-title-deactive':data[i].active==0}">{{item}}</div>
                <!--历程轴-->
                <div class="step-axis">
                    <div :class="{'step-icon-active':data[i].active==1,'step-icon-deactive':data[i].active==0}"></div>
                    <template v-if="i+1<expTitle.length">
                        <div :class="{
                        'step-line-active':data[i+1].active==1 && data[i].active==1,
                        'step-line-deactive':data[i+1].active==0 || data[i].active==0}"></div>
                    </template>
                </div>
                <!--历程指标-->
                <div class="step-index" v-if="data[i].active == 1">
                    <template v-for="item in data[i].children">
                        <div class="step-index-card">
                            <div class="index-title">{{item.title}}</div>
                            <!--<el-row>
                                <el-col :span="24">
                                    <template v-for="(idx) in item.index">
                                        <el-button size="mini"
                                                   :class="{'el-button-active':activeIdx==idx.indicateId}"
                                                   @click="handleClickIdx(idx)">{{idx.indicateName}}</el-button>
                                    </template>
                                </el-col>
                            </el-row>-->
                            <!--<div class="f-level-idx" :style="'width:'+120*Math.ceil(item.index.length/3)+'px'">-->
                            <div class="f-level-idx" :style="'width:'+140*Math.ceil(item.index.length/3)+'px'">
                                <el-row>
                                    <template v-for="(colNum) in Math.ceil(item.index.length/3)">
                                        <el-col :span="Math.ceil(24/Math.ceil(item.index.length/3))">
                                            <template v-for="(idx,j) in item.index">
                                                <el-tooltip :content="idx.indicateName + (idx.firstLevelValue ? idx.firstLevelValue : '')" placement="left">
                                                    <el-button v-if="j<colNum*3&&j>=(colNum-1)*3 "
                                                               size="mini"
                                                               :class="{'el-button-active':activeIdx==idx.indicateId}"
                                                               @click="handleClickIdx(idx)">{{idx.indicateName}}&nbsp;{{idx.firstLevelValue}}</el-button>
                                                </el-tooltip>
                                            </template>
                                        </el-col>
                                    </template>
                                </el-row>
                            <!--<template v-for="(idx) in item.index">
                                    <el-button size="mini"
                                               :class="{'el-button-active':activeIdx==idx.indicateId}"
                                               @click="handleClickIdx(idx)">{{idx.indicateName}}</el-button>
                                </template>-->
                            </div>
                        </div>
                    </template>
                   <!-- <div style="width: 40px;float: left"></div>-->
                </div>
            </div>
        </template>

        </el-scrollbar>
    </div>
</template>
<script>
    import Vue from "vue";
    import {Steps, Step,Tooltip} from "element-ui";
    Vue.use(Steps).use(Step).use(Tooltip);
    export default {
        name:'expAxis',
        data(){
            return {
                expTitle:['发现','评估','购买','接触','使用','缴费','支持'],
                //data:[],
                //activeIdx:'',
            }
        },
        props:['sceneName','activeIdx','data'],
        created(){
            /*this.initAxisData();*/
        },
        methods:{
            /*//请求历程数据
            initAxisData(){
                this.data = [
                    {
                        active:0,
                    },{
                        active:1,
                        children:[{
                            title:'礼品选择',
                            index:[{
                                name:'礼品种类和数量',
                                indexCode:'1'
                            },{
                                name:'礼品的性价比',
                                indexCode:'2'
                            },{
                                name:'促销优惠活动',
                                indexCode:'3'
                            }]
                        },{
                            title:'礼品选择',
                            index:[{
                                name:'礼品种类和数量',
                                indexCode:'11'
                            },{
                                name:'礼品的性价比',
                                indexCode:'21'
                            },{
                                name:'促销优惠活动',
                                indexCode:'31'
                            }]
                        }]
                    },{
                        active:1,
                        children:[{
                            title:'兑换',
                            index:[{
                                name:'兑换便捷性',
                                indexCode:'77'
                            }]
                        }]
                    },{
                        active:1,
                        children:[{
                            title:'配送',
                            index:[{
                                name:'配送服务满意度',
                                indexCode:'78'
                            }]
                        }]
                    },{
                        active:0,
                    },{
                        active:0,
                    },{
                        active:1,
                        children:[{
                            title:'售后',
                            index:[{
                                name:'售后服务满意度',
                                indexCode:'100'
                            }]
                        }]
                    }];
            },*/
            handleClickIdx(idx){
                //this.activeIdx = idx.indexCode;
                this.$emit('clickIndex',this.sceneName,{
                    id:idx.indicateId,
                    name:idx.indicateName
                });
            }
        }
    };
</script>
<style lang="less">
    .exp-axis{
       /* width: 930px;*/
        width: 100%;
        /*min-height: 170px;*/
        height: 100%;
        
        // overflow-x: auto;
        display: flex;
        justify-content: space-between;

        .el-scrollbar {
            width: 100%;
        }

        .el-scrollbar__view {
            display: flex;
            flex-direction: row;
            padding-left: 30px;
            padding-right: 60px;
        }

        .exp-con{
           float: left;
        }
        .step-title{
            height: 30px;
            /*display: flex;*/
            margin-left: -8px;
            text-align: left;
        }
        .step-axis{
            height: 25px;
        }
        .exp-steps-active{
            .step-index{
                margin-right: 20px;
                min-width: 190px;
            }
            .step-title-active{
           /*     align-items: center;*/
                display: table-cell;
                vertical-align: bottom;
                /*font-size: 20px;
                color: #575B61;
                font-weight: 600;*/

                font-family: SourceHanSansSC-Medium;
                font-size: 14px;
                color: #252631;
                line-height: 22px;
                transform: translateX(-25%)
            }
        }
        .step-axis{
            position: relative;
            /*display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;*/
            .step-title-deactive,.step-icon-deactive{
                transform: translateY(50%);
            }
        }
        .step-icon-active{
            position: relative;
            transform: translateY(10%);
            width: 16px;
            height: 16px;
            border-radius: 18px;
            background: #4993FF;
            border: 2px solid #E5ECFF;
            z-index: 12;
            top:4px;
        }
        .step-line-active{
            height: 1px;
            top: 13px;
            position: absolute;
            width: 100%;
            border-color: #1AA0FD;
            background-color: #1AA0FD;
            z-index: 11;
        }
        .exp-steps{
            // min-width:150px;
            flex-grow: 1;
            
        }
        .step-title-deactive{
            transform: translateY(40%);
            /*align-items: flex-end;*/

            font-family: SourceHanSansSC-Regular;
            font-size: 14px;
            color: #8C8C8C;
            line-height: 22px;
        }
        .step-icon-deactive{
            position: relative;
            width: 10px;
            height: 10px;
            border-radius: 8px;
            background: #D1D9DE;
            border: 1px solid #D1D9DE;
            z-index: 12;
            top:4px;
        }
        .step-line-deactive{
            height: 1px;
            top: 13px;
            position: absolute;
            width: 100%;
            border-color: #C0C4CC;
            background-color: #C0C4CC;
            z-index: 11;
            margin-left: 13px;
        }
        .exp-con:last-child{
            width: 50px !important;
        }
        .step-index{
            /*display: flex;*/
            margin-left: -40px;
            .step-index-card{
               /* width: 110px;*/
                float: left;
                width: auto;
                padding-right: 10px;
                .index-title{
                    font-size: 14px;
                    text-align: center;
                    width: 96px;

                    font-family: SourceHanSansSC-Medium;
                    color: #252631;
                    line-height: 22px;
                }
            }
            .f-level-idx{
                height: 105px;
                text-align: center;
                /*display: flex;
                flex-direction: column;
                flex-wrap: wrap*/
                // transform: translateX(-50%);
            }
            .el-button{
                padding: 6px 5px;
                margin:8px 10px 0 0;
                width:auto;
                min-width: 120px;
                text-align: center;
                background: #F6F9FA;
                border: 1px solid #D5DADE;
                border-radius: 4px;
                font-size: 12px;
                color: #575B61;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                background: #fff;
                font-family: SourceHanSansSC-Regular;
                font-size: 12px;
                color: #262626;
            }
            .el-button-active{
                color: #F09C32;
                border: 1px solid #F09C32;
            }
        }
    }

    
</style>