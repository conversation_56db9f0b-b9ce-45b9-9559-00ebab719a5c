<template>
  <div class="TrendPage" v-loading="loading">
    <el-row :gutter="20" >
      <el-col :span="8" v-for="(item,index) in filtData" :key="(new Date().getTime().toString()).substring(6,11)+item.title">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>{{item.title}}</span>
          </div>
          <div class="box-card-content">
            <trend-bar
              :xAxis="item.xAxis"
              :barData="item.barData"
              :lineData="item.lineData"
              :targetData="item.targetData"
              :barnumIsShow="barnumIsShow"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Vue from 'vue';
import { Card, Row, Col } from 'element-ui';
import TrendBar from '../components/common/TrendBar.vue';

Vue.use(Card)
  .use(Row)
  .use(Col);
export default {
  name: 'Trend',
  components: {
    TrendBar,
  },
  props: {
    data: {
      type: Array,
      default: () => [
        {
          indicatName: '移动装机/移机满意度',
          competitiveFigureId: '11',
          idOperator: {
            127: '电信',
            18: '移动',
            128: '联通',
          },
          nameList: ['201905', '201904', '201903'],
          mobileList: ['37141', '108731', '22466'],
          unicomList: ['78925', '49357', '52759'],
          telecomList: ['12952', '73485', '24141'],
        },
      ],
    },
    barnumIsShow: {
      type: Boolean,
      default: () => false,
    },
  },
  computed: {
    filtData() {
      const resultList = [];
      this.data.forEach((it) => {
        /*var nameList = it.nameList.map(name=>{
          name = name.substring(0,4)+'/'+name.substring(4,6);
          return name;
        })*/
        resultList.push({
          title: it.indicatName,
          id: it.indicatId,
          xAxis: it.nameList,
          barData: {
            name: '完成值',
            data: it.newVList,
          },
          lineData: {
            name: '去年值',
            data: it.oldVList,
          },
          targetData: {
            // name: '目标值',
            name: '指标阈值',
            data: it.targetList,
          },
        });
      });
      return resultList;
    },
  },
  data() {
    return {
      value: '',
      loading: false,
    };
  },
  mounted() {},
  methods: {},
  watch: {},
};
</script>

<style lang='less'>
.TrendPage {
  position: relative;
  .el-card {
    border: 0px;
    border-radius: 0px;
  }

  .box-card {
    text-align: left;
    margin-bottom: 20px;
    .el-card__header {
      background-color: #fff;
      padding: 12px 20px;
      font-family: SourceHanSansSC-Regular;
      font-size: 16px;
      color: #262626;
      border-bottom: 0px;
    }
    .el-card__body {
      padding: 10px;
    }
    .box-card-content {
      position: relative;
      height: 15vw;
    }
  }
}
</style>
