<template>
    <div class="exp-header">
        <!-- <el-row class="header-f"> -->
            <!-- <el-col :span="22"> -->
                <!--<template v-for="(item,i) in headData1">
                    <div class="f-step"
                    :class="{'header-back1':i==0,'header-back2':i>0&&i<headData1.length-1,'header-back3':i==(headData1.length-1)}">{{item.name}}</div>
                </template>-->
                <!-- <div class="f-step header-back1" @click="goToHome">体验历程类</div>
                <div class="f-step header-back2" @click="backTo">{{experienceTypeName}}</div>
                <div class="f-step header-back3">{{productName}}</div>
            </el-col>
            <el-col :span="2">
                <el-button type="primary" size="mini" @click="toManage"><i class="el-icon-s-tools"></i>管理中心</el-button>
            </el-col>
        </el-row> -->
        <el-row class="header-s">
            <template v-for = "(data,id) in headData2" >
                <div class="s-step" :class="{'s-step-active':data.minorServiceType==satisfy.productName}" @click="changeProduct(data)">{{data.minorServiceType}}</div>
                <!-- <el-divider direction="vertical"></el-divider> -->
            </template>
        </el-row>
    </div>
</template>
<script>
    import Vue from 'vue';
    import {Row, Col, Button, Divider} from 'element-ui';
    import { mapState, mapMutations } from 'vuex';
    Vue.use(Row).use(Col).use(Button).use(Divider)
    export default {
        name:"expHeader",
        data(){
            return {
               /*headData1:[{
                   name:'体验历程类',
                   code:'',
               },{
                   name:'个人业务',
                   code:'1',
               }],*/
                headData2:[],
               /* headData2Acive:'1-1',*/
            }
        },
        computed:{
            // ...mapState(['experienceTypeName','productCode','productName'])
            ...mapState(['satisfy'])
            
        },
        created(){
            this.initProductTabs();
        },

        methods:{
            initProductTabs(){
                /*this.headData2 = [{
                    name:'营业厅',
                    code:'1-1'
                },{
                    name:'热线',
                    code:'1-2'
                },{
                    name:'微厅',
                    code:'1-3'
                }];*/
                this.$http
                .post('/experience-service/experience/getMinor', {mainServiceName:sessionStorage.getItem('menuName')})
                .then((res) => {
                    this.headData2 = res.data;  
                    this.$store.commit('setProductInfo',{
                        productCode:this.headData2[0].indicateId,
                        productName:this.headData2[0].minorServiceType,
                    })
                    
                }).catch((res) =>{
                })

            },
            changeProduct(data){
                this.$store.commit('setProductInfo',{
                    productCode:data.indicateId,
                    productName:data.minorServiceType
                })
                this.$emit('changeTab');
            },
            toManage(){
                this.$store.commit('setProductInfo',{
                    productCode:'',
                    productName:'管理中心'
                })
                this.$router.push({
                    name:'experienceManage'
                })
            },
            backTo(){
                this.$router.push({
                    name:'experienceCourse'
                })
            },
            goToHome(){
                this.$router.replace({
                    name: 'home',
                    params: {
                        id: 0,
                    },
                });
            }
        }
    };
</script>
<style scoped lang='less'>
    .exp-header{
        width: 100%;
        margin-top: 35px;
        // margin-bottom: 24px;
    }
    .header-f{
        height: 50px;
        width: 100%;
        padding: 0 76px;
        .el-col{
            height: 100%;
            line-height: 50px;
            /*display: flex;
            align-items: center;*/
            div{
                top: 50%;
                transform: translateY(50%);
                /*display: flex;
                align-items: center;
                justify-content: center;*/
                float: left;
                font-size: 12px;
            }
            .f-step{
                min-width: 80px;
                height: 24px;
                line-height: 24px;
                background-size: contain;
            }
            .header-back1{
                background-image: url("../assets/img/expCourse/first.png");
                cursor: pointer;
                background-repeat: no-repeat;
            }
            .header-back2{
                background-image: url("../assets/img/expCourse/middle.png");
                cursor: pointer;
                background-repeat: no-repeat;
            }
            .header-back3{
                background-image: url("../assets/img/expCourse/last.png");
                cursor: pointer;
                background-repeat: no-repeat;
            }
            .el-icon-s-tools{
                margin-right: 6px;
            }
            .el-button--mini{
                padding: 7px 11px;
                font-size: 14px;
                background-image: linear-gradient(0deg, #4796FF 15%, #28B1FF 97%);
                box-shadow: 0 2px 3px 0 rgba(0,0,0,0.08);
                background-color: unset;
            }
        }

    }
    .header-s{
        height: 40px;
        width: 100%;
        background-color: #FFFFFF;
        padding: 0 80px;
        /*display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: row;*/
        .s-step{
            /*display: flex;
            align-items: center;
            justify-content:center;*/
            /*min-width: 120px;*/
            float: left;
            width: auto;
            height: 100%;
            line-height: 40px;
            font-size: 12px;
            font-weight: bold;
            // color: #575B61;
            color: #262626;
            cursor: pointer;
            text-align: center;
            font-family: SourceHanSansSC-Bold;
            margin-right: 64px;
        }
            
            // &>div:hover{
                
            // }
        .el-divider--vertical{
            margin: 0;
            float: left;
            margin-top:12px;
        }
        .s-step-active{
            // background-color: #E5F5FF;
            // color: #2D9CFA;
            border-bottom: 3px solid #262626;
                &::after {
                    content:' ';
                    width:100%;
                    height: 3px;
                    background: #262626;
                }
        }
    }

</style>