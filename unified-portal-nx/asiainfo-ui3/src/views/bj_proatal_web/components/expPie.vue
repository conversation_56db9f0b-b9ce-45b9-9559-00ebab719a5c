<template>
    <!-- <v-chart lang="echarts" :options="pie"/> -->
    <div class="echarts" ref="chart"></div>
</template>
<script>
    /*import ECharts from 'vue-echarts';
    import 'echarts/lib/component/legend';
    import 'echarts/lib/component/title';
    import 'echarts/lib/component/tooltip';
    import 'echarts/lib/chart/pie';*/

    import * as echarts from "echarts";

    export default {
        name:'expPie',
        props:{
            data:{
                type:Array,
                default:[
                    {name: '南京a', value: 100},
                    {name: '南京b', value: 101},
                    {name: '南京c', value: 102}
                ]
            }
        },
        data() {
            return {
                chart:null
            }
        },
        computed:{
            pie(){
                /*const colorList = ['#47A2FF ', '#53C8D1', '#59CB74', '#FBD444', '#7F6AAD', '#585247']*/
                const  colorList = ['#FFE768','#6583E9','#50DBA1'];
                let dataObj = {};
                this.data.forEach(function (e,i) {
                    dataObj[e.name]=e.value;

                });
                return  {
                    tooltip: {
                        trigger: 'item',
                    },
                    legend: {
                        orient: 'vertical',
                        right: '0%',
                        top:'25%',
                        icon:'circle',
                        itemWidth:10,
                        itemHeight:10,
                        itemGap:15,
                        textStyle:{
                            fontSize: 14,
                            color: '#5E6267',
                        },
                        formatter:function (p) {
                            return p+'：'+dataObj[p];
                        }
                    },
                    color: colorList,
                    series: [
                        {
                            name: '',
                            type: 'pie',
                            radius: [40, 85],
                            center: ['30%', '50%'],
                            label: {
                                show: true,
                                position:'inside',
                                /*formatter:function (value) {
                                    return value.data.value;
                                }*/
                            },
                            labelLine: {
                                show: false
                            },
                            data: this.data,
                        }
                    ]
                };
            }

        },

        mounted(){
              this.initChart();

          },
          methods:{
            initChart(){
                this.chart = echarts.init(this.$refs.chart, null, {
                    render: "svg"
                });

                if(chart.isBarClick){
                    this.chart.on('click', (params) => {
                        this.$emit('barClick',{
                            'countyId':that.this[params.name],
                            'countyName':params.name})
                    })
                }

                this.updateOption();

                window.addEventListener("resize", ()=>{
                    this.chart.resize();
                });
            },

            updateOption(){
              let _this = this;
              this.chart.setOption(this.pie);
            }
          }
    };
</script>
<style scoped lang="less">
    .echarts {
        width: 100%;
        height: 100%;
    }
</style>