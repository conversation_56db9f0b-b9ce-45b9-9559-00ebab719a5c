<template>
	<div class="line-card">
        <div class="title-wrap">
            <a class="title">历程{{order+1}}：{{data.targetName}}</a>
            <a class="score">
                <span class="text">{{Number(data.courseScore).toFixed(1)}}</span>
                <Arrow :num="Number(data.courseOpp)" fontFamily="SourceHanSansSC-Light" style="font-size:12px"/>
            </a>
        </div>
        <div ref="chart" class="chart" style="height:120px;margin-top:30px" ></div>
        <!-- <div class="score-wrap">
            <p v-for="(item,idx) in data.index" class="score-list">
                <a class="title">{{item.title}}</a>
                <a class="score">
                    <span class="text">{{item.score.toFixed(1)}}</span>
                    <Arrow :num="item.ratio" fontFamily="SourceHanSansSC-Light" style="font-size:12px"/>
                </a>
            </p>
        </div> -->
    </div>
</template>

<script>
    import Vue from 'vue';
    import * as echarts from "echarts";
    import Arrow from '../../common/arrow.vue';

    export default {
        name:"CompanyRank",
        props:{
        	data:Object,
            order:Number
        },
        components:{
            Arrow
        },
        data(){
            return {
                chart:null
            }
        },
        watch: {
            data(){
                this.initChart(this.data.trendLists)
            }
           
        },
        computed:{


        },
        created(){
        },
        mounted(){
            setTimeout(() => {
                this.initChart(this.data.trendLists);
            }, 1200);
            
        },
        methods:{
        	initChart(trendList){
                this.chart = echarts.init(this.$refs.chart, null, {
                    render: "svg"
                  });
                this.updateOption(trendList);

                window.addEventListener("resize", ()=>{
                    this.chart.resize();
                });
			},

			updateOption(trendList){
				  let maxVal= Number(trendList[0]);

                  const xData = trendList.map(item=>{
				    return item.name;
				  })
				  const dataList = trendList.map(item=>{
				    maxVal = Number(item.value)>maxVal ? Number(item.value) : maxVal;
				    return Number(item.value);
				  })

                  

				  let option = {
				      color: ['#FF9900', '#000000'],
				      grid:{
                        left:0,
                        right:0,
				        top:24,
				        bottom:24
				      },
                      tooltip: {
                        show:true,
                        // padding: 0,
                        trigger:'axis',
                        transitionDuration: 1,
                        textStyle: {
                            color: '#000',
                            decoration: 'none'
                        },
                        borderColor:"rgba(255,255,255,0)",
                        backgroundColor:'rgba(255,255,255,0)',
                        position: ['0px', '50%'],
                        renderMode:'html',
                        confine:false,
                        appendToBody:true,
                        formatter: function(params){
                            // console.log(params,'555555555');
                            // var tipHtml = '';
				      	    // tipHtml = `<div style="color:black;font-size:12px">
                            //   ${params[0].name}
                            //   <br/>
                            //   客户满意指数：<span style="font-size:20px">${params[0].value}</span>  <span style="color:grey;font-size:12px">  分</span>
                            // </div>`
                            // return tipHtml
                            return  params[0].name + '<br />' + params[0].marker  + params[0].value + '分'
                        }
                      },
				      xAxis: {
                        data:xData,
				        show:false,
				        type: 'category',
				        boundaryGap:false,
				      },
				      yAxis: {
				        show:false,
				        type: 'value',
				        max: Number(maxVal),
		
				      },
				      series: [
				          {
				            data: dataList,
				            type: 'line',
				            smooth: true,
				            symbol: 'none',
				            lineWidth:1,
				            areaStyle: {
				              color: new echarts.graphic.LinearGradient(
				                0,
				                0,
				                0,
				                1,
				                [
				                  {
				                    offset: 0,
				                    color: 'rgba(255, 236, 207, 1)',
				                  },
				                  {
				                    offset: 1,
				                    color: 'rgba(255, 236, 207, 0.2)',
				                  },
				                ],
				                false
				              ),
				            }
				          }           
				      ]
				  };
				 this.chart && this.chart.setOption(option);
			},
        }
    };
</script>

<style lang="less" scoped>
    .line-card {
        width: 100%; 
        height: 100%; 
        display: flex; 
        flex-direction: column;
        background: rgba(245, 245, 245, 1);
        padding: 20px;
        box-sizing: border-box;

        .title-wrap {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .title {
                font-size: 14px;
                color: rgb(89, 89, 89);
                display: flex;
                align-items: center;

                &::before {
                    content:"";
                    display: inline-block;
                    width: 4px;
                    height:18px;
                    background-color: #ff9900;
                    margin-right: 10px;
                };
            }

            .score {
                display: flex;
                align-items: baseline;
                width: auto;

                .text {
                    font-size:20px;
                    margin-right:10px;
                    color: rgb(89, 89, 89);
                }
            }
        }

        .score-wrap {
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            font-size:12px;
            color: rgb(89, 89, 89);
            .score-list {
                display:flex; 
                justify-content:space-between; 
                height: 12px;
                min-height: 12px; 
                line-height:12px;
            }
            .score {
                display: flex;
                align-items: baseline;
                width: auto;

                .text {
                    margin-right:10px;
                    color: rgb(89, 89, 89);
                }
            }
        }
    }

</style>

