<template>
	<div style="width:100%; height:675px; display:flex;">
        <div class='map-wrap' style="width:50%">
            <div style="display:flex;justify-content:space-between; padding-right:20px;box-sizing:border-box;align-items:center;">
                <h2>客户满意指数{{info.name}}排名</h2>
                <el-button type='text' plain size='mini' @click='backToBj' v-if='isSecondLevel'>全市数据</el-button>
            </div>
            <Map 
            :datas="tableData" 
            :getCompanyData="getCompanyData" 
            :showBtn='showBtn' 
            ref='map'/>
            <div class="map-legend">
                <span class="legend-item" style="background:#339933"></span>前三分公司
                <span class="legend-item" style="background:#D6EBD6"></span>后三分公司
            </div>
            <!-- <p class="map-legend"><span class="legend-item" style="background:#D6EBD6"></span>客户满意指数后3分公司</p> -->
        </div>

        <div class="map-wrap"  style="width:50%; padding-left:5%; box-sizing:border-box;">
            <el-table
            :data="tableData"
            :header-cell-style = "{background:'#fff',borderBottom:'1px solid #dfe6ec',fontWeight:'normal',color:'#262626'}"
            :cell-style = "{borderBottom:'none',color:'#8c8c8c'}"
            @sort-change="onSortChange"
            style="width: 100%; margin-top:10px;">
                <el-table-column
                prop="cityname"
                label="分公司"
                width='auto'
                min-width="120">
               	 	<template slot-scope="scope">
                        {{scope.row.cityname}}分公司
                    </template>
                </el-table-column>
                <el-table-column
                prop="indexvalue"
                align='center'
                label="满意度指数"
                sortable
                width="120">
                    <template slot-scope="scope">
                        {{scope.row.indexvalue}}分
                    </template>
                </el-table-column>
                <el-table-column
                prop="proportion"
                align='center'
                label="环比"
                sortable
                width="auto"
                min-width='100'>
                    <template slot-scope="scope">
                        <Arrow :num='Number(scope.row.proportion)' style="font-size:14px;" fontFamily='SourceHanSansSC-Regular'/>
                    </template>
                </el-table-column>
                <el-table-column
                prop="rank"
                align='center'
                label="排名"
                sortable
                width="auto">
                </el-table-column>
                <el-table-column 
                label="近6月客户满意指数趋势"
                align='center' 
                width="auto"
                min-width='160'
                max-width='200' >
                    <template slot-scope="scope">
                        <div 
                        :ref="`chart${scope.row.id}`" 
                        style="width:100%; height:30px;"
                        :key="scope.row.id"></div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
    import Vue from 'vue';
    import * as echarts from "echarts";
    const fuhao = require("../../../assets/img/fuhao.png");
    const triagle = require("../../../assets/img/triagle.png");
    import Map from '../../../components/satisfy/panoramaIndex/map';
    import Arrow from '../../../components/common/arrow';

    export default {
        name:"CompanyRank",
        props:{
        	tableData:Array,
            info:Object
        },
        components:{
        	Map,
            Arrow
        },
        data(){
            return {
               triagle:triagle,
               isSecondLevel: false
            }
        },
        watch: {
            tableData(){
            	setTimeout(()=>{
	                this.tableData.forEach(item=>{
                        this.initChart(item.id,item.trendLists);  
	                })
	            },100)
            }
        },
        computed:{


        },
        created(){
        },
        mounted(){
        },
        methods:{
        	initChart(id,trendList){
                // console.log('ID',id,trendList);
				if(this.$refs['chart'+id]!=undefined){
				    let chart = null;
				    chart = echarts.init(this.$refs['chart'+id], null, {
                        render: "svg"
                      });
				    this.updateOption(chart,trendList);

                    window.addEventListener("resize", ()=>{
                        chart.resize();
                    });
				}
			},

			updateOption(chart,trendList){
				  let maxVal= Number(trendList[0].value);

                  const xData = trendList.map(item=>{
				    return item.optime;
				  })

				  trendList = trendList.map(item=>{
				    maxVal = Number(item.value)>maxVal ? Number(item.value) : maxVal;
				    return Number(item.value);
				  })

				  let option = {
				      color: ['#FF9900', '#000000'],
				      grid:{
				        top:2,
				        bottom:2
				      },
                      tooltip: {
                        show:true,
                        // padding: 0,
                        trigger:'axis',
                        transitionDuration: 1,
                        textStyle: {
                            color: '#000',
                            decoration: 'none'
                        },
                        borderColor:"rgba(255,255,255,0)",
                        backgroundColor:'rgba(255,255,255,0)',
                        position: ['0px', '50%'],
                        renderMode:'html',
                        confine:false,
                        appendToBody:true,
                        formatter: function(params){
                            return  params[0].name + '<br />' + params[0].marker  + params[0].value + '分'
                        }
                      },
				      xAxis: {
                        data:xData,
				        show:false,
				        type: 'category',
				        boundaryGap:false,
				      },
				      yAxis: {
				        show:false,
				        type: 'value',
				        max: Number(maxVal)+10,
		
				      },
				      series: [
				          {
				            data: trendList,
				            type: 'line',
				            smooth: true,
                            symbol:'none',
				            lineWidth:1,
				            areaStyle: {
				              color: new echarts.graphic.LinearGradient(
				                0,
				                0,
				                0,
				                1,
				                [
				                  {
				                    offset: 0,
				                    color: 'rgba(255, 236, 207, 1)',
				                  },
				                  {
				                    offset: 1,
				                    color: 'rgba(255, 236, 207, 0.2)',
				                  },
				                ],
				                false
				              ),
				            }
				          }           
				      ]
				  };
				  chart.setOption(option);
			},

            backToBj(){
                // this.$refs.map.initChart('北京');
                this.isSecondLevel = false;
                this.$refs.map.loading = true;
                this.$refs.map.currentCity = '北京';
                this.$emit('getCompanyData','999');
            },

            showBtn(){
                this.isSecondLevel = true;
            },

            onSortChange({ column, prop, order }){
                this.tableData.forEach(item=>{
                    if(echarts.getInstanceByDom(this.$refs['chart'+item.id])){
                        echarts.getInstanceByDom(this.$refs['chart'+item.id]).clear();
                    }
                    setTimeout(()=>{
                        this.initChart(item.id,item.trendLists);    
                    },100)
                })
            },

            getCompanyData(name){
                this.$emit('getCompanyData',name)
            }
        }
    };
</script>

<style lang="less" scope>
 	h2,h4 {
      font-weight: normal;
      font-size: 20px;
      margin:0px;
    }

	.map-legend {
        color:#8c8c8c;
        font-size: 12px;
        width: 100%;
        text-align: center;
        // display: flex;
        // align-items: center;

        .legend-item {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #ddd;
            margin-right: 6px;
            margin-left: 20px;
        }
    }

    .map-wrap {
        width:50%;
        height:100%;
        display:flex; 
        flex-direction:column;
    }

</style>

