<template>
	<div style="width:100%; height:100%;" class="exp">
        <h5 class="title">客户满意指数历程</h5>
        <div class="exp-box-wrap">
            <div v-for='(item,index) in expLists' 
            class="exp-box" :key='index'>
              <img :src="item.icon" width="90px" class="img" />
              <span class="dashed-line-vertical"></span>
              <p class="label">{{'历程'+(index+1)+item.targetName}}</p>
              <p class="score">{{item.courseScore}}</p>
            </div>
            <span class="dashed-line-horizontal"></span>
        </div>

        <h5 class="title">历程得分</h5>
        <div class="card-wrap">
            <div
            class="card"
            v-for="(item,index) in expLists"
            :key="index">
                <LineCard
                :data="item" 
                :order="index"/>
            </div>
        </div>
</div>
</template>

<script>
    import LineCard from '../common/LineCard.vue';
    export default {
      name:"ExpList",
      props:['expLists'],
      components:{
        LineCard
      },
      mounted() {

      }
    };
</script>

<style lang="less" scope>

.exp {
  .title {
      width: 100%;
      font-size: 20px;
      font-weight: 400;
      margin-bottom: 20px;
      font-family: 'syhtMedium';
  }

  .exp-box-wrap {
    width: 100%;
    height: 186px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 20px;
    position: relative;

    .dashed-line-horizontal {
      width: 100%; 
      min-width: 100%;
      border-bottom: 1px dashed #ddd;
      position: absolute;
      top: 100px;
    }

    .exp-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .dashed-line-vertical {
        height: 20px; 
        border-right: 1px dashed #ddd;
        position: absolute;
        top: 80px;
      }

      
      .label {
        margin-top: 24px;
        font-size: 14px;
        color: #898989;
      }

      .score {
        font-size: 24px;
        line-height: 40px;
      }
    }
    
  }

  .card-wrap {
    width:100%; 
    height:420px; 
    display:flex; 
    column-gap:20px; 
    row-gap:20px; 
    flex-wrap:wrap;

    .card {
      width:20%; 
      flex-grow:2; 
      height:48%;
    }
  }
}


</style>



