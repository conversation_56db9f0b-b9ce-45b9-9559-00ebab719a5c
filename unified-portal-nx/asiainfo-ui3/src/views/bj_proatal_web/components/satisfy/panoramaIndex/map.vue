<template>
	<div ref="chart" class="map" v-loading='loading'></div>
	<!-- <div v-else v-loading='loading'>暂无数据</div> -->
</template>

<script>
    import Vue from 'vue';
    import * as echarts from "echarts";

    const beijingSvg = require("../../../assets/beijing.svg");
    const changping = require("../../../assets/changping.svg");
    const huairou = require("../../../assets/huairou.svg");
    const miyun = require("../../../assets/miyun.svg");
    const pinggu = require("../../../assets/pingu.svg");
    const shunyi = require("../../../assets/shunyi.svg");
    const yanqing = require("../../../assets/yanqing.svg");
    const chengsan = require("../../../assets/chengsan.svg");
    const chenger = require("../../../assets/chenger.svg");
    const chengyi = require("../../../assets/chengyi.svg");
    const tongzhou = require("../../../assets/tongzhou.svg");
    const daxing = require("../../../assets/daxing.svg");
    const fangshan = require("../../../assets/fangshan.svg");

    const fuhao = require("../../../assets/img/fuhao.png");
    const triagle = require("../../../assets/img/triagle.png");

    export default {
        name:"Map",
        props:{
        	date:String,
        	title:String,
        	legendTop:String,
        	datas:Array,
        	getCompanyData:Function,
        	showBtn:Function
        },
        data(){
            return {
                chart:null,
                thirdScore:0,
                lastThirdScore:0,
                svg:beijingSvg,
				currentCity:'北京',
				allCities:['北京','怀柔','密云','平谷','顺义','延庆','昌平','城三','城二','城一','通州','房山','大兴'],
				svgs:{
                	'北京': {
                		svg:beijingSvg,
                		viewBox: "*********** 470"
                	},
                	'怀柔': {
                		svg:huairou,
                		viewBox: "*********** 468"
                	}, 
                    '密云': {
                		svg:miyun,
                		viewBox: "20 ***********"
                	}, 
                    '平谷': {
                		svg: pinggu,
                		viewBox: "150 50 880 800"
                	},
                    '顺义': {
                		svg: shunyi,
                		viewBox: "*********** 700"
                	},
                    '延庆': {
                		svg: yanqing,
                		viewBox: "120 180 650 680"
                	},
                    '昌平': {
                		svg: changping,
                		viewBox: "550 120 650 450"
                	}, 
                    '城三': {
                		svg: chengsan,
                		viewBox: "-3450 1200 578 819"
                	}, 
                    '城二': {
                		svg: chenger,
                		viewBox: "00 150 490 700"
                	}, 
                    '城一': {
                		svg: chengyi,
                		viewBox: "350 100 480 670"
                	},
                    '通州': {
                		svg: tongzhou,
                		viewBox: "400 210 480 1010"
                	},
                    '房山': {
                		svg: fangshan,
                		viewBox: "0 50 480 720"
                	},
                    '大兴': {
                		svg: daxing,
                		viewBox: "350 00 580 1020"
                	}
                },

                loading: false
            }
        },
        watch: {

        },
        computed:{


        },
        created(){
        },
        mounted(){
        	// this.generateChart();
            // 城一 通州
        },
        methods:{
        	generateChart(){
        		
        	},

        	initChart(){
				let name = this.currentCity;
        		var _this = this;
        		this.loading = true;
        		this.svg = this.svgs[name].svg;

				if(this.chart){
        			this.chart.dispose();
        		}

				if(this.datas.length != 0) {
					if(this.datas.length<=4) {
						this.lastThirdScore = this.datas[2].value;
						this.thirdScore = this.datas[1].value;
					} else if(this.datas.length<=6) {
						this.lastThirdScore = this.datas[4].value;
						this.thirdScore = this.datas[2].value;
					} else {
						this.lastThirdScore = this.datas[this.datas.length-4].value
						this.thirdScore = this.datas[3].value
					}
				}


				
        		if(name != '北京'){
        			this.showBtn();	
        		}
        		this.chart = echarts.init(this.$refs.chart, null, {
	                render: "svg"
	            }); 
	            this.chart.on('click',(params)=>{
					if(params.seriesType='map' && this.allCities.includes(params.data.cityname)) {
						this.currentCity = params.data.cityname
						this.getCompanyData(params.data.cityid);
					}	
	            })
        		
	            $.get(this.svg,  (svg)=> {
	            	
	            	svg.getElementsByTagName('svg')[0].setAttribute("width", _this.$refs.chart.clientWidth+'px');
	            	svg.getElementsByTagName('svg')[0].setAttribute("height", _this.$refs.chart.clientHeight+'px');
	            	svg.getElementsByTagName('svg')[0].setAttribute("viewBox", _this.svgs[name].viewBox);
                    // svg.getElementsByTagName('svg')[0].setAttribute("viewBox", "*********** 350");

	            	setTimeout(()=>{
						console.error(1)
	            		echarts.registerMap('map', {svg: svg});
					    // let svg = document.getElementsByTagName('svg');
					    this.updateOption(name);
					    this.loading = false;
					    window.addEventListener("resize", ()=>{
		                    svg.getElementsByTagName('svg')[0].setAttribute("width", _this.$refs.chart.clientWidth+'px');
		                    svg.getElementsByTagName('svg')[0].setAttribute("height", _this.$refs.chart.clientHeight+20+'px');
		                    svg.getElementsByTagName('svg')[0].setAttribute("viewBox", "950 400 "+480+" "+470);;
		                    // echarts.registerMap('map', {svg: svg});
			                this.chart.resize();
			            });
			            
	            	},1000)
				    
				});
	            
	        },

	        updateOption(name){
	        	let _this = this;

	        	let option = {
	        		grid: [
			            {
			               left: '0',
			                right: 0,
			                top: 10,
			            }
			        ],
	        		tooltip: {
						show:true,
				        padding: 0,
				        enterable: true,
				        transitionDuration: 1,
				        textStyle: {
				            color: '#000',
				            decoration: 'none'
				        },
				        borderColor:"rgba(255,255,255,0)",
				        backgroundColor:'rgba(255,255,255,0.3)',
				        formatter: function(params) {
				        	if(params.componentSubType=="map"){
				        		var tipHtml = '';
					            tipHtml = `<div style="height:auto;width:416px;border-radius:4px;background:rgba(255,255,255,0.7);box-shadow:0 4px 11px 1px rgba(99,99,99,0.19); padding:10px 14px 0px 14px;  box-sizing:border-box; display:flex; flex-direction:column; justify-content: space-around;font-family: SourceHanSansSC-Regular;">

					            		<div style='margin:0px; font-size:16px; display:flex; align-items:center;'>
					            			<img style='margin-right:12px;' src="${fuhao}" />${params.name}
					            		</div>
					            		<div style='display:flex; align-items:center; justify-content:space-between; font-size:14px;'>

					            			<div style=' display:flex; align-items:center;'>
					            				<p style='position:relative'>
						            				<i style='display:inline-block; width:2px; height: 12px; background:#FF9900; position:absolute; top:9px;'></i>
						            				<span style='margin-left:16px'>客户满意指数：</span>
						            				<span style='font-size:24px;'>${params.data.indexvalue}</span>&nbsp;&nbsp;分
					            				</p>
			            					</div>

			            					<div style=" display:flex;align-items:center;width:120px;">
												<span style="margin-right:10px;">排名：${params.data.rank}</span>
											</div>
				            			</div>

				            			<div style='display:flex; align-items:center; justify-content:space-between; font-size:14px;'>

					            			<div style=' display:flex; align-items:center;'>
					            				<p style='position:relative'>
						            				<i style='display:inline-block; width:2px; height: 12px; background:#FF9900; position:absolute; top:9px;'></i>
						            				<span style='margin-left:16px'>覆盖客户数：</span>
						            				<span style='font-size:24px;'>${params.data.covernum}</span>&nbsp;&nbsp;人
					            				</p>
			            					</div>

			            					<div style=" display:flex;align-items:center;width:120px;">
												<span style="display:flex; align-items:center;">环比：
													<img src='${triagle}'/><span style="color:green">${params.data.proportion}0pp</span>
												</span>
											</div>
				            			</div>
					                </div>`;
					            return tipHtml;
				        	}
				        }
				    },


				    visualMap: {
		                type: 'piecewise',
		                show: false,
		                pieces: [
		                    {
		                        lte: this.lastThirdScore,
		                        label: '少',
		                        color: '#85C285'
		                    },{
		                        gt: this.lastThirdScore,
		                        lt: this.thirdScore,
		                        label: '中等',
		                        color: '#D6EBD6'
		                    },{
		                        gte: this.thirdScore,
		                        label: '多',
		                        color: '#339933'
		                    }
		                ],
		                color: '#fff',
		                textStyle: {
		                    color: '#000',
		                },
		                visibility: 'off'
		            },
        		
					geo: [{
						map: "map",
						name:"map",
						zoom: 1,
						roam: false,
						// silent: name=='北京'?false:true,
						silent:false,
                        selectedMode: 'single',
                        select:{
                            // label:{
                            //     show: false
                            // },
							itemStyle: {
								normal: {
									areaColor: '#CCE4FD',
									borderColor: '#FFFFFF',
									borderWidth: 1
								}
							}
                        },
						itemStyle: {
							normal: {
								areaColor: '#CCE4FD',
								borderColor: '#FFFFFF',
								borderWidth: 1
							}
						},
						zlevel:8,
						label: {
							show: true,
							textStyle: {
								color: "#333",
								fontSize:10
							},
							// position: [10, 10],
							emphasis:{
								show: true
							}
						}			
					}],
					series: [
						{
							type: "map",
							nameProperty:"map",
							geoIndex:0,
                            selectedMode: 'single',
                            roam: true,
							/*label: {
								show: true,
								textStyle: {
									color: "#1DE9B6",
								},
							},
							itemStyle: {
								borderColor: "#8dc0e8",
								borderWidth: 1,
								areaColor: "#97ddfa"
							},
							zoom: 1,
							map: "beijing", //使用
							
							silent: false,
							zlevel: 1,*/
							data: _this.datas,
						}
					]
	            }
	        	this.chart.setOption(option);
	        }
        }
    };
</script>

<style lang="less" scope>
	.map {
		width:100%; 
		height:100%;
		display: flex; 
		flex-direction: column;
		padding-bottom: 20px;
		box-sizing: border-box;
		font-family: SourceHanSansSC-Regular;

	}

</style>

