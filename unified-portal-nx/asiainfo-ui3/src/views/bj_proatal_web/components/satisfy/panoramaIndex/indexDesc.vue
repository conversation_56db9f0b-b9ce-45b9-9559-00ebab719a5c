<template>
  <div class="index-desc">
    <h2 v-if="type == 1">{{info.name}}客户满意指数</h2>
    <h2 v-else>{{month}}月{{info.name}}客户满意指数历程</h2>


    <div class="score-wrap">
      <div style="display:flex; align-items:center; height:100%;">
        <img src="@/assets/images/u37.png">
        <div style="margin-left: 24px;">
          <h4>{{info.name}}客户满意指数</h4>
          <p>
            <span class="score">{{info.indexvalue}}</span>分  
            <span class="ratio">
              环比：<Arrow :num='Number(info.proportion)' style='font-size:16px;display:inline-block'/>
            </span>
          </p>
        </div>
      </div>
      
    </div>

    <slot name='desc'></slot>

  </div>
</template>

<script>
const fuhao = require("../../../assets/img/fuhao.png");
const triagle = require("../../../assets/img/triagle.png");
import Arrow from '../../../components/common/arrow'
export default {
  name:'IndexDesc',
  props:{
    info:Object,
    title: String,
    companyName: String,
    score:'',
    type:String,
    month:String,
  },
  components:{
    Arrow
  },
  data(){
    return {
      triagle: triagle
    }
  }
};
</script>

<style lang='less'>
  .index-desc {
    display: flex;
    flex-direction: column;
    width:100%;
    height: 260px;
    justify-content: space-between;

    h2,h4 {
      font-weight: normal;
      font-size: 20px;
      margin:0px;
    }

    h4 {
      font-size: 14px;
    }

    .score-wrap {
      width: 100%;
      height: 100px;
      margin-bottom: 10px;
      display: flex;
      margin-top:10px;

      p {
        margin:0px;
        margin-top:10px;
      }
      .score {
        font-size: 30px;
      }
      .ratio {
        font-size: 12px;
        margin-left: 100px;
        position:relative;
      }
    }

    .desc-wrap {
      height: auto;
      font-size: 12px;
      color: rgb(140, 140, 140);
      line-height: 28px;
      margin-top:20px;
    }
  }
  
</style>
