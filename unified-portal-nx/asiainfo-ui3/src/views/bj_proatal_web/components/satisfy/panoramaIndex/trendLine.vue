<template>
    <div style="width:100%; height:260px;" ref="chart"></div>
</template>
<script>
import Vue from 'vue';
import * as echarts from "echarts";


export default {
    name:"Others",
    components:{

    },
    props: {
        datas: Array,
        title: String,
        subtext: String
    },
    data(){
        return {
            chart: null
        }
    },
    watch: {
        datas(){
            this.initChart()
        }
    },
    computed:{

    },
    created(){
        
    },
    mounted(){
        this.initChart();
    },
    methods:{

        initChart(){
            this.chart = echarts.init(this.$refs.chart, null, {
                render: "svg"
            });
            window.addEventListener("resize", ()=>{
                this.chart.resize();
            });
            this.updateOption();
        },

        updateOption(){
            let xData = this.datas.map((item)=>{
                return item.name
            });

            let option = {
                title:{
                    text:this.title,
                    subtext: this.subtext,
                    textStyle:{
                        fontSize:14,
                        fontWeight:'normal',
                        color:'#262626'
                    },
                    subtextStyle: {
                        color:'#8C8C8C',
                        lineHeight:36,
                    },
                    top:60,
                },
                tooltip: {
                  trigger: 'axis',
                  symbol: 2,
                  borderColor:"rgba(255,255,255,0)",
                  backgroundColor:'rgba(255,255,255,0)',
                  formatter: function(params){
                      var tipHtml = '';
					  tipHtml = `<div style="color:black;font-size:12px">
                        客户满意指数：<span style="font-size:20px">${params[0].value}</span>  <span style="color:grey;font-size:12px">  分</span>
                      </div>`
                      return tipHtml
                  }
                },
                color: ['#FF9900', '#000000'],
                grid:{
                    top:140,
                    bottom:0,
                    containLabel: true,
                    left:0
                },
                xAxis: {
                    show:true,
                    type: 'category',
                    boundaryGap:false,
                    data: xData,
                    axisLabel:{
                        color:'#8C8C8C'
                    },
                    axisLine:{
                        lineStyle: {
                            color: '#ddd'
                        }
                    },

                },

                yAxis: {
                    show:true,
                    type: 'value',
                    axisLabel:{
                        color:'#8C8C8C'
                    },
                    splitLine:{
                        show:true,
                        lineStyle:{
                            type:'dashed'
                        }
                    },
                    min:0,
                    max:100,
                    splitNumber:4,
                    interval:25,
                },

                series: [
                    {
                        data: this.datas,
                        type: 'line',
                        smooth: true,
                        showSymbol: false,
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0,0,0,1,
                                [
                                    {
                                        offset: 0,
                                        color: 'rgba(255, 236, 207, 1)',
                                    },
                                    {
                                        offset: 1,
                                        color: 'rgba(255, 236, 207, 0)',
                                    },
                                ],
                                false
                            ),
                        }
                    }           
                ]
            };
            this.chart&&this.chart.setOption(option);
        }
    }
};
</script>
<style scoped lang="less">
    


</style>

<style lang="less">

    
</style>
