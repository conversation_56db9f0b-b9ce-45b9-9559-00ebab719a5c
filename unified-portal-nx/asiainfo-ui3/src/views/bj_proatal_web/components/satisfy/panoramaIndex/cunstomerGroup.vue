<template>
    <div class="customer-card">
        <!-- <div class="customer-title">我的客群自定义名称A</div> -->
        <div class="customer-title">{{ data.title }}</div>
        <div>
            <div class="cities" v-for="item in data.citytag" :key="item.name">
                <div class="city">{{ item.name }}</div>
            </div>
            <div class="date">{{ data.date }}</div>
            
            
            
        </div>
        <div v-if="data.score" style="margin-top:12px">
            <div class="scores"><span class="score">{{ data.score }}</span> 分</div>
            <div class="ratio">
                环比：<Arrow class="arrow" :num='data.ratio' style='font-size:14px;font-weight: 100'/>
            </div>
        </div>
        <div v-else class="none-data" style="margin-top:25px">
            数据加工中，稍后查看...
        </div>
    </div>
</template>

<script>
import Arrow from '../../../components/common/arrow'
export default {
  name:'customerGroup',
  props:{
    data:Object
  },
  components:{
    Arrow
  },
  data(){
    return {
        increaseScore:10
    }
  }
};
</script>

<style lang='less'>
.customer-card{
    margin-top: 15px;
    width: 340px;
    height: 150px;
    background-color: white;
    border-radius: 5px;
    padding: 25px;
    .customer-title{
        margin-bottom: 7px;
    }
    .cities,.city,.date,.scores,.ratio{
        display: inline-block;
    }
    .city{
        font-size: 12px;
        color: rgb(89, 89, 89);
        padding: 3px 15px;
        border-radius: 3px;
        background-color: rgb(242, 242, 242);
        margin-right: 8px;
    }
    .date{
        float: right;
        color: rgb(191, 191, 191);
        font-size: 14px;
        line-height: 30px;
    }
    .scores{
        font-size: 13px;
        font-weight: 300;
        color: rgb(119, 116, 117);
        .score{
            font-size:25px;
            color: black;
            font-weight: 500;
        }
    }
    .ratio{
        // float: right;
        margin-left: 30px;
        line-height: 48px;
        font-size: 13px;
        font-weight: 300;
        color: rgb(119, 116, 117);
        .arrow{
            display: inline-block;
            margin-left: 10px;    
        }  
    }
    .none-data{
        font-size: 13px;
        color: rgb(119, 116, 117);
    }
    
}
  
</style>
