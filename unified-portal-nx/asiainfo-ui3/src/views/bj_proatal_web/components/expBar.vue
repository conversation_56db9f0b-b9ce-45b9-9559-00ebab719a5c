<template>
    <!-- <v-chart class="echarts" ref="barCharts" :options="bar"/> -->
    <div class="echarts" ref="chart"></div>
</template>
<script>
/*import ECharts from 'vue-echarts';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/title';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/chart/bar';*/
import * as echarts from "echarts";

export default {
    name:'expBar',
    data() {
        return{}
    },
    props:{
        info:{
            type:Object,
            default:()=>{
                return {'1':1};
            }
        },
        xAxis:{
            type:Array,
            default:()=> {
                return [18,28,15,6,17,8,8]
            },
        },
        yAxis:{
            type:Array,
            default:()=> {
                return ['阳光新城幼儿园', '汤沐景苑幼儿园', '张寨镇新星幼儿园','朱寨镇新思路幼儿园','朱寨镇燕牌坊幼儿园','龙固镇春龙佳源幼儿园','任庄幼儿园']
            },
        },
        isBarClick:{
            type:Boolean
        },
        showLabel:{
            type:Boolean
        }
    },
    computed:{
        bar(){
            const that = this;
            return {
                tooltip:{
                    trigger: 'axis',
                        axisPointer: {
                        type: 'shadow',
                            shadowStyle:{
                            color:'rgb(215,242,255,0.4)',
                        }
                    }
                },
                grid: {
                    left: '5%',
                        right: '10%',
                        bottom: '5%',
                        top: '5%',
                        containLabel: true
                },
                xAxis: {
                    show: true,
                        type:'value',
                        splitLine:{
                        lineStyle:{
                            color:'#B1B7C2',
                                type:'dashed',
                        }
                    },
                    axisLabel: {
                        show:false
                    },
                    axisLine:{
                        show:true,
                            lineStyle: {
                            color: '#B1B7C2'
                        }
                    },
                    axisTick:{
                        show:false
                    },
                },
                yAxis: {
                    type: 'category',
                        inverse: true,
                        show: true,
                        data:that.yAxis,
                        axisLabel: {
                        textStyle: {
                            color: '#8F939C'
                        }
                    },
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                },
                series: [
                    {
                        show: true,
                        type: 'bar',
                        barWidth: '25%',
                        z: 2,
                        itemStyle: {
                            normal: {
                                color: function (params) {
                                    return new ECharts.graphic.LinearGradient(0, 0, 1, 0, [{
                                        offset: 0,
                                        color: '#5D82FF' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color:  '#1FB9FF'// 100% 处的颜色
                                    }], false)
                                },
                                barBorderRadius: [0,5,5,0]
                            }
                        },
                        label: {
                            normal: {
                                show: that.showLabel,
                                textStyle: {
                                    color: ' #808790',
                                    fontSize: 12,
                                },
                                position: 'right',
                            }
                        },
                        data: that.xAxis,
                    },
                ]
            }
        }
    },
    mounted(){
        // const that = this;
        // const chartsDom = that.$refs.chart.chart;
        // 绑定点击事件
        
    },

    mounted(){
          this.initChart();

      },
      methods:{
        initChart(){
            this.chart = echarts.init(this.$refs.chart, null, {
                render: "svg"
            });

            if(this.isBarClick){
                this.chart.on('click', (params) => {
                    this.$emit('barClick',{
                        'countyId':this.this[params.name],
                        'countyName':params.name})
                })
            }

            this.updateOption();

            window.addEventListener("resize", ()=>{
                this.chart.resize();
            });
        },

        updateOption(){
          let _this = this;
          this.chart.setOption(this.bar);
        }
      }
};
</script>
<style scoped lang="less">
    .echarts {
        width: 100%;
        height: 100%;
    }
</style>