<template>
  <div ref="spehunsty" class="hunsty" style="position: relative;">
    <div :id="t" class="hunstychild" :style="{ opacity: (allData.xData&&allData.xData.length) ? '1' : '0' }" />
    <div
      :style="{
        position: 'absolute',
        left: 0,
        top: 0,
        width: '100%',
        height: '100%',
        display: (allData.xData&&allData.xData.length) ? 'none' : 'block',
      }"
    >
      <Blank2 />
    </div>
  </div>
</template>

<script>

export default {
  name: 'GLineChart',
  props: {
    allData: {
      type: Object,
      default: () => {
        return {
          xData: [],
          chartData: {}
        }
      }
    },
    option: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 控制echart图显示多少
    barlineControl: {
      type: Array,
      default: () => {
        return ['bar', 'bar', 'bar', 'bar']
      }
    },
    // 横坐标出现多少个数据时 文字斜放
    minRotateXaxis: {
      type: String,
      require: false,
      default: () => '3'
    }
  },
  data() {
    return {
      t: '',
      chart: null,
      valueList: [],
      showChart: true
    }
  },
  computed: {
    legendData() {
      const tempObj = JSON.parse(JSON.stringify(this.allData.chartData))
      const temp = Object.keys(tempObj)
      return temp
    },
    seriesData() {
      var series = []
      Object.keys(this.allData.chartData).forEach(i => {
        series.push(this.allData.chartData[i])
      })
      return series
    }
  },
  watch: {
    allData(v, oldv) {
      this.chart && this.chart.dispose()
      this.initChart()
    }
  },
  created() {
    this.t = new Date().getTime() + (Math.random() * 100).toFixed(0)
  },
  mounted() {
    // var _this = this
    // this.$nextTick(() => {
    // this.initChart()
    // })
    // window.addEventListener('resize', function() {
    //   _this.chart.resize()
    // })
  },

  methods: {
    initChart() {
      this.chart && this.chart.dispose()
      var dom = document.getElementById(this.t)
      this.chart = this.$echarts.init(dom)
      this.renderChart()
      const _self = this
      this.chart.on('click', params => {
        // 逻辑处理放在组件外面
        _self.$emit('barclick', { xValue: params.name })
      })
    },
    renderChart() {
      const _self = this
      const option = {
        color: ['#7097f6', '#66d7cc', '#ffbe4d', '#f9906f'],
        grid: {
          top: '16%',
          left: '10%',
          right: '10%',
          bottom: '15%',
          containLabel: false
        },

        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255,255,255,1)',
          textStyle: {
            color: '#262626',
            align: 'left'
          },
          confine: true,
          extraCssText: 'box-shadow:0px 2px 8px 0px rgba(102, 61, 0, 0.16)'

        },
        legend: {
          data: this.legendData,
          selected: {
            '总计': true,
            '1-6分': false,
            '7-8分': false,
            '9-10分': false
          },
          top: '0%',
          textStyle: {
            color: '#747474'
          }
        },
        xAxis: {
          type: 'category',
          // data: ['2022-02', '2022-03'],
          data: this.allData.xData || [],
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            rotate: this.allData.xData.length > Number(this.minRotateXaxis) ? 25 : 0,
            textStyle: {
              color: '#393939'
            }
          }
        },
        yAxis: [
          {
            // max: 100,
            type: 'value',
            nameTextStyle: {
              color: '#393939',
              padding: [0, 0, 10, -40]
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#eeeeee'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#393939'
              },
              formatter:
                this.legendData[0] && this.legendData[0].indexOf('万投') != -1
                  ? '{value} ‱'
                  : '{value}'
            }
          }

        ],
        series: [
          {
            name: this.legendData[0],
            type: 'bar',
            barWidth: this.allData.xData.length > 3 ? 10 : 15,
            label: {
              show: true,
              position: 'top'
            },

            data: this.seriesData[0]
          },
          {

            name: this.legendData[1],
            type: 'bar',
            barWidth: this.allData.xData.length > 3 ? 10 : 15,
            label: {
              show: true,
              position: 'top'
            },

            data: this.seriesData[1]
          },
          {
            name: this.legendData[2],
            type: 'bar',
            barWidth: this.allData.xData.length > 3 ? 10 : 15,
            label: {
              show: true,
              position: 'top'
            },

            data: this.seriesData[2]
          },
          {
            name: this.legendData[3],
            type: 'bar',
            barWidth: this.allData.xData.length > 3 ? 10 : 15,
            label: {
              show: true,
              position: 'top'
            },
            // itemStyle: {
            //   normal: {
            //     color: '#0682FF',
            //     borderRadius: [1, 1, 0, 0]
            //   }
            // },
            data: this.seriesData[3]
          }
        ]
      }
      this.chart.setOption(option)
      this.chart.hideLoading()
    }
  }
}
</script>
<style lang="scss" scoped>
.hunsty {
  height: 100%;
  width: 100%;
  position: relative;
}
.hunstychild{
  height: 100%;
  width: 100%;
}
</style>
