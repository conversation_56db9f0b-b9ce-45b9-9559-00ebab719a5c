<template>
  <div class="date-picker">
    <div class="date-picker-types">
      <span>选择时间：</span>
      <el-select v-model="dateType" size="small" value-key="label" @change="handleDateChange">
        <el-option v-for="dt in dateTypes" :key="dt.value" :label="dt.label" :value="dt" />
      </el-select>
    </div>
    <!-- 季度组件 -->
    <date-quarter
      v-if="dateType.value=='quarter'"
      v-model="dates.quarter__date"
      @change="handleDateChange"
    />
    <!-- < 年月日组件 -->
    <el-date-picker
      v-else-if="dateType.value=='month'"
      :key="dateType.label"
      v-model="dates[`${dateType.value}__date`]"
      size="small"
      :type="dateType.value"
      :format="format[dateType.value]"
      :clearable="clearble"
      :picker-options="pickerOptions"
      @change="handleDateChange"
    />
    <el-date-picker
      v-else-if="dateType.value=='daterange'"
      :key="dateType.label"
      v-model="selectDateDay"
      size="small"
      :format="format[dateType.value]"
      :clearable="clearble"
      :picker-options="pickerOptions"
      @change="handleDateChange"
    />

  </div>
</template>

<script>
import DateQuarter from './DateQuarter.vue'
import Common from 'bj_src/lib/date'
import tool from '@/views/bj_proatal_web/utils/utils'

export default {
  name: 'DatePicker',
  components: {
    DateQuarter
  },
  props: {
    defaultType: {
      type: String,
      default: '月'
    },
    defaultDate: {
      type: Object,
      default: () => ({}) // {'月': new Date(),'日': new Date()}
    },
    dateTypes: {
      type: Array,
      default: () => [
        // date daterange month monthrange year
        { label: '日', value: 'daterange' }, // date
        { label: '月', value: 'month' }, // monthrange
        { label: '季度', value: 'quarter' }
        // { label: '年', value: 'year' },
      ]
    },
    format: {
      type: Object,
      default: () => ({
        date: 'yyyy-MM-dd',
        month: 'yyyy年MM月',
        year: 'yyyy年',
        quarter: 'yyyy年Q季度',
        daterange: 'yyyy-MM-dd'
      })
    },
    valueFormat: {
      type: Object,
      default: () => ({
        date: 'yyyy-MM-dd',
        month: 'yyyy-MM',
        year: 'yyyy',
        quarter: 'yyyy-MM',
        daterange: 'yyyy-MM-dd'
      })
    },
    clearble: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const today = new Date()
    const curQ = new Date(today.setMonth(today.getMonth() / 3)) // 季度Date
    const [dateType] = this.dateTypes.filter(
      ({ label }) => label == this.defaultType
    )
    // 30天间隔
    const day30 = 30 * 24 * 3600 * 1000

    return {
      dateType: dateType || { value: 'date', label: '日' },
      dates: {
        quarter__date: Common.formatDate(curQ, 'yyyy-MM')
      },
      // daterange的pickerOptions，选择时间范围补超过30天
      pickerMinDate: null,
      pickerMaxdate: null,
      selectDateDay: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && this.pickerMinDate) {
            this.pickerMinDate = null
          } else if (minDate) {
            this.pickerMinDate = minDate.getTime()
          }
        },
        disabledDate: time => {
          if (this.pickerMinDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() > this.pickerMinDate + day30 ||
              time.getTime() < this.pickerMinDate - day30
            )
          }
          return time.getTime() > Date.now()
        }
      }
    }
  },
  created() {
    this.initDateValues()
    this.handleDateChange()
  },
  methods: {
    initDateValues() {
      this.dateTypes.forEach(({ value, label }) => {
        // 季度
        if (value === 'quarter') {
          const today = new Date()
          const curQ = new Date(today.setMonth(today.getMonth() / 3)) // 季度Date
          this.$set(
            this.dates,
            `${value}__date`,
            Common.formatDate(curQ, 'yyyy-MM')
          )
        } else if (value.indexOf('range') > -1) {
          const start = new Date()
          // 提前14天
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 14)
          this.$set(this.dates, `${value}__date`, [start, new Date()])
        } else {
          // 日月默认值
          const initValues = this.defaultDate[label] || new Date()
          this.$set(this.dates, `${value}__date`, initValues)
          this.selectDateDay = new Date()
        }
      })
    },
    handleDateChange(v) {
      const { value, label } = this.dateType
      if (value == 'quarter') {
        // 季度组件返回的值的格式就是yyyy-MM,不是日期对象
        this.$emit('change', [label, this.dates[`${value}__date`]])
      } else if (value.indexOf('range') > -1) {
        if (!(v instanceof Date)) {
          v = this.selectDateDay
        }
        this.selectDateDay = v
        let d = new Date(v)
        d = d.getTime()
        let startTime = new Date(d - 2 * 24 * 60 * 60 * 1000)
        startTime = tool.formatterDate(startTime, 'yyyy-MM-dd')
        const temp = [
          label,
          [startTime, tool.formatterDate(v, 'yyyy-MM-dd')]
        ]
        this.$emit('change', temp)
      } else {
        this.$emit('change', [
          label,
          Common.formatDate(
            this.dates[`${value}__date`],
            this.valueFormat[value]
          )
        ])
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.date-picker {
  display: flex;
  span {
    color: #262626;
    font-size: 14px;
  }
  .el-select {
    width: 100px;
    margin-right: 5px;
  }
  /deep/ .el-date-editor {
    &:not(.el-range-editor) {
      width: 160px;
    }
    &.el-range-editor {
      width: 240px;
    }
  }
}
</style>
