<template>
  <div class="CompanyPage" v-loading="loading">
    <el-row :gutter="20">
      <el-col :span="24" v-for="item in filtData" :key="item.id">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>{{item.title}}</span>
          </div>
          <div class="box-card-content">
            <el-row :gutter="20">
              <el-col :span="item.branchExplainTextPojo!=null?19:24">
                <div class="echarts-content">
                  <company-bar
                  :barnumIsShow='barnumIsShow'
                    :xAxis="item.xAxis"
                    :currData="item.currData"
                    :lastData="item.lastData"
                    :targetData="item.targetData"
                    :scoreList="item.scoreList"
                  />
                </div>
              </el-col>
              <el-col :span="item.branchExplainTextPojo!=null?5:0" class="desc-area"
                      v-if='item.branchExplainTextPojo!=null'>
                <div class="title">说明</div>
                <div class="subtitle">{{item.branchExplainTextPojo.date}}</div>
                <div>
                  <div class="col1">
                    <span class="bread-head el-icon-xiajiangqushi1"></span>
                    {{item.branchExplainTextPojo.topText}}
                  </div>
                  <div class="col2">
                    <span class="bread-head el-icon-xiajiangqushi"></span>
                    {{item.branchExplainTextPojo.bottomText}}
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Vue from 'vue';
import { Card, Row, Col } from 'element-ui';
import CompanyBar from '../components/common/CompanyBar.vue';

Vue.use(Card)
  .use(Row)
  .use(Col);
export default {
  name: 'Company',
  components: {
    CompanyBar,
  },
  props: {
    data: {
      type: Array,
      default: () => [
        {
          indicatName: '移动装机/移机满意度',
          competitiveFigureId: '11',
          idOperator: {
            127: '电信',
            18: '移动',
            128: '联通',
          },
          nameList: ['201905', '201904', '201903'],
          mobileList: ['37141', '108731', '22466'],
          unicomList: ['78925', '49357', '52759'],
          telecomList: ['12952', '73485', '24141'],
        },
      ],
    },
    barnumIsShow: {
      type: Boolean,
      default: () => false,
    },
  },
  computed: {
    filtData() {
      console.log(this.data);
      const resultList = [];
      this.data.forEach((it) => {
        resultList.push({
          title: it.indicateName,
          id: it.branchFigureId,
          xAxis: it.singleBranchOfficeColumnPojo.branchNameList,
          currData: {
            name: '当前值',
            data: it.singleBranchOfficeColumnPojo.currentValueList,
          },
          lastData: {
            name: '上周期',
            data: it.singleBranchOfficeColumnPojo.lastValueList,
          },
          targetData: {
            name: '目标值',
            data: it.singleBranchOfficeColumnPojo.targetValueList,
          },
          branchExplainTextPojo: it.branchExplainTextPojo,
          scoreList: {
            name: '得分率',
            data: it.singleBranchOfficeColumnPojo.scoreList,
          },
        });
      });
      return resultList;
    },
  },
  data() {
    return {
      value: '',
      loading: false,
    };
  },
  mounted() {},
  methods: {},
  watch: {},
};
</script>

<style lang='less'>
.CompanyPage {
  position: relative;
  .el-card {
    border: 0px;
    border-radius: 0px;
  }
  .box-card {
    text-align: left;
    margin-bottom: 20px;
    .el-card__header {
      background-color: #fff;
      padding: 12px 20px;
      font-family: SourceHanSansSC-Regular;
      font-size: 16px;
      color: #262626;
      border-bottom: 0px;
    }
    .el-card__body {
      padding: 10px;
    }
    .echarts-content {
      position: relative;
      height: 24vw;
    }
    .desc-area {
      max-height: 23vw;
      overflow: auto;
      padding-right: 20px !important;
      word-break: break-all;
      line-height: 30px;
      font-size: 14px;
      .title {
        font-size: 16px;
        text-align: center;
        line-height: 50px;
      }
      .subtitle {
        font-size: 14px;
        line-height: 30px;
        margin-bottom: 10px;
      }
      .col1,
      .col2 {
        position: relative;
        padding-left: 35px;
        span {
          position: absolute;
          font-size: 24px;
          left: 0;
          top: 3px;
        }
      }
      .col1 {
        span {
          color: #047AFB;
        }
      }
      .col2 {
        color: #ED5D00;
      }
      // overflow-y: auto;
    }
  }
}
</style>
