<template>
	<el-table
		ref="myForm"
    :data="tableData"
    :span-method="objectSpanMethod"
    :key="code"
    height="600"
    :cell-class-name="formatterCellClass" 
    style="width: 100%; margin-top:20px;">
      <el-table-column
      prop="keyLink"
      :label="tableTitle.keyLink"
      width="160">
          <template slot-scope="scope">
            <h5>
              <span style="display:inline-block; width:8px; height:8px; border-radius:8px; background-color:#f90;margin-right:8px;"></span>
              {{scope.row.keyLink}}
            </h5>
          </template>
      </el-table-column>
      <el-table-column
      prop="targetName"
      :label="tableTitle.targetName"
      width="240">
      </el-table-column>
      <el-table-column
          :label="tableTitle.thresholdValue"
          width="auto">
          <template slot-scope="scope">
            <span style="font-size:20px; font-weight:700" v-if="Number(scope.row.thresholdValue)">
              {{scope.row.thresholdValue}}<span style="font-size:14px; font-weight:400">{{scope.row.unit}}</span>
            </span>
            <span v-else-if="scope.row.instruction">{{scope.row.instruction}}</span>
            <span v-else>-</span>
          </template>
      </el-table-column>
      <el-table-column
      :label="tableTitle.targetValue"
      width="auto">
          <template slot-scope="scope">
            <span style="font-size:20px; font-weight:700" >{{scope.row.targetValue}}</span>
            <span style="font-size:12px;" v-show="Number(scope.row.targetValue)">{{scope.row.unit}}</span>
          </template>
      </el-table-column>
      <el-table-column
      :label="tableTitle.sequential"
      width="auto">
          <template slot-scope="scope">
            <Arrow :num='Number(scope.row.sequential)'/>
            <!-- <span style="font-size:12px;" v-show="Number(scope.row.sequential)">pp</span> -->
          </template>
      </el-table-column>
      <el-table-column :label="tableTitle.trend" width="200" min-width='200'>
          <template slot-scope="scope" >
              <div :ref="`chart${scope.row.parentCode.toString()+scope.row.id}`" style="width:100%; height:80px;"></div>
          </template>
      </el-table-column>
    </el-table>
</template>


<script>

	import * as echarts from "echarts";
	import Arrow from '../common/arrow.vue';
	const fuhao = require("../../assets/img/fuhao.png");

	export default {
	  name: 'Table',
	  components: {
	    Arrow
	  },
	  props:['tableData','code','tableTitle'],
	  data() {
	    return {};
	  },
	  computed: {
	  },
	  watch:{
	  	tableData(val) {
	  		setTimeout(()=>{
          // console.warn(tab.tableData)
          if(val.length!=0){
            val.forEach(item=>{
              this.initChart(item.parentCode.toString()+item.id,item.trendList,item.targetName);
            })
          }
        },100);
	  	}
	  },
	  mounted() {
	  	this.$nextTick( () => {
        if(this.tableData) {
          this.tableData.forEach(item=>{
            this.initChart(item.parentCode.toString()+item.id,item.trendList,item.targetName);
          })
        }
	  		
	  	})
	  },
	  methods: {
		  formatterCellClass({row, column, rowIndex, columnIndex}){
		    // console.log(columnIndex);
		    if(columnIndex==0){
		      return 'border-right-dashed'
		    }
		  },

		  objectSpanMethod({ row, column, rowIndex, columnIndex }) {
		    if (columnIndex === 0) {
		      if (row.colNum) {
		        return {
		          rowspan: row.colNum,
		          colspan: 1
		        };
		      } else {
		        return {
		          rowspan: 1,
		          colspan: 0
		        };
		      }
		    }
		  },

		  initChart(id,trendList,name){
        // console.warn("this.$refs['chart'+id]",this.$refs['chart'+id]);
        if(this.$refs['chart'+id]!=undefined && trendList.length) {
          // console.log('chart'+id,this.$refs['chart'+id],trendList)
            let chart = echarts.init(this.$refs['chart'+id], null, {
              render: "svg"
          	});
            this.updateOption(chart,trendList,name);
        }  else {
            this.clearChart(id);
         }
      },

      clearChart(id){
        let chart = echarts.getInstanceByDom(this.$refs['chart'+id]) ;
        if(chart){
          chart.clear();  
        }
      },

      updateOption(chart,trendList,idxName){
          let maxVal= trendList[0].value;

          trendList.forEach(item=>{
            maxVal = Number(item.value)>maxVal ? Number(item.value) : maxVal;
          })
          // console.log('trendList-----------',trendList)

          let option = {
              color: ['#FF9900', '#000000'],
              grid:{
                top:10,
                bottom:10
              },
              xAxis: {
                show:false,
                type: 'category',
                boundaryGap:false,
              },
              yAxis: {
                show:false,
                type: 'value',
                max: Number(maxVal)>20?Number(maxVal)+10:20,
                min:-10,
              },

              tooltip: {
                show:true,
                padding: 0,
                // enterable: true,
                trigger:'axis',
                transitionDuration: 1,
                textStyle: {
                    color: '#000',
                    decoration: 'none'
                },
                borderColor:"rgba(255,255,255,0)",
                backgroundColor:'rgba(255,255,255,0)',
                position: ['0px', '50%'],
                renderMode:'html',
                confine:false,
                appendToBody:true,
                axisPointer:{
                  lineStyle:{
                    color:'#ff9900',
                    width:2,
                    type:'solid'
                  }
                },
                formatter: function(params) {
                  // console.log(params[0].data)
                  var tipHtml = '';
                  var name = (params[0].data.name).split('-');
                  var value = params[0].data.value;
                  var unit = params[0].data.unit;
                  tipHtml = 
                  `<div style="max-height:56px;width:auto;border-radius:4px;background:rgba(255,255,255,0.8);box-shadow:0 4px 4px 1px rgba(99,99,99,0.19); padding:4px 12px;  box-sizing:border-box; display:flex; flex-direction:column; justify-content: space-around;font-family: SourceHanSansSC-Light; font-size:12px; color:#8c8c8c">
                          <div style='margin:0px; display:flex; align-items:center; height:24px;'>
                            <img style='margin-right:4px;' src="${fuhao}" />${name[0]}年${name[1]}月
                          </div>
                          <div style='height:24px; display:flex; align-items:center;'>
                            <a style='display:inline-block; width:2px; height: 10px; background:#FF9900;margin-left:2px'></a>
                            <span style='margin-left:16px'>${idxName}&nbsp;&nbsp;&nbsp;</span>
                            <span>${value}${unit}</span>
                          </div>
                    </div>`;
                  return tipHtml;
                }
              },

              series: [
                  {
                    data: trendList,
                    type: 'line',
                    smooth: true,
                    symbol: 'emptyCircle',
                    areaStyle: {
                      color: new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: 'rgba(255, 236, 207, 1)',
                          },
                          {
                            offset: 1,
                            color: 'rgba(255, 236, 207, 0)',
                          },
                        ],
                        false
                      ),
                    }
                  }           
              ]
          };
          chart.setOption(option);
      },
	  }
	};
</script>

<style lang="less" scope>
	.el-table__body-wrapper::-webkit-scrollbar {
    width: 4px;
  }

  .el-table__body-wrapper::-webkit-scrollbar-thumb  {
    background-color: #ff9900;
    border-radius: 4px;
  }
</style>