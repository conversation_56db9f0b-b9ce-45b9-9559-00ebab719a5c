<template>
  <div class="TrendPage" v-loading="loading">
    <el-row :gutter="20">
      <el-col :span="24" v-for="item in filtData" :key="item.id">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>{{item.title}}</span>
          </div>
          <div class="box-card-content">
            <trend-bar
              :xAxis="item.xAxis"
              :barData="item.barData"
              :lineData="item.lineData"
              :targetData="item.targetData"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Vue from 'vue';
import { Card, Row, Col } from 'element-ui';
import TrendBar from '../components/common/TrendBar.vue';

Vue.use(Card)
  .use(Row)
  .use(Col);
export default {
  name: 'Tag',
  components: {
    TrendBar,
  },
  props: {
    data: {
      type: Array,
      default: () => [
        {
          indicatName: '移动装机/移机满意度',
          competitiveFigureId: '11',
          idOperator: {
            127: '电信',
            18: '移动',
            128: '联通',
          },
          nameList: ['201905', '201904', '201903'],
          mobileList: ['37141', '108731', '22466'],
          unicomList: ['78925', '49357', '52759'],
          telecomList: ['12952', '73485', '24141'],
        },
      ],
    },
  },
  computed: {
    filtData() {
      console.log(this.data);
      const resultList = [];
      this.data.forEach((it) => {
        resultList.push({
          title: it.indicateName,
          id: it.labelFigureId,
          xAxis: it.singleLabelProportionColumnPojo.labelNameList,
          barData: {
            name: '当前值',
            data: it.singleLabelProportionColumnPojo.currentValueList,
          },
          lineData: {
            name: '去年',
            data: it.singleLabelProportionColumnPojo.lastValueList,
          },
          targetData: {
            name: '目标值',
            data: it.singleLabelProportionColumnPojo.targetValueList,
          },
        });
      });
      return resultList;
    },
  },
  data() {
    return {
      value: '',
      loading: false,
    };
  },
  mounted() {
    const a = 1;
  },
  methods: {},
  watch: {},
};
</script>

<style lang='less'>
.TrendPage {
  position: relative;
  .box-card {
    text-align: left;
    margin-bottom: 20px;
    .el-card__header {
      background-color: rgba(87, 156, 255, 0.05);
      padding: 10px 20px;
      font-size: 15px;
    }
    .el-card__body {
      padding: 10px;
    }
    .box-card-content {
      position: relative;
      height: 15vw;
    }
  }
}
</style>
