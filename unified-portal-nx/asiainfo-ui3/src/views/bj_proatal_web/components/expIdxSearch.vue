<template>
    <div class="idx-head">
        <el-row class="def-row">
            <el-col class="def-col" :span="10">
                <span>用户体验指标</span>
                <!--<div class="l-oper" @click="addScene"><i class="el-icon-question"></i>新增业务体验历程</div>-->
            </el-col>
            <el-col class="def-col" :span="14">
                <el-cascader v-if="filialeActive==1"
                    v-model="filiale"
                     :props="filialeProps"
                    :options="filialeOptions"
                    placeholder="请选择公司"
                    class="partment-select cascader"
                    popper-class="cascaderPopper"
                    @change="filialeChange"
                    size="small"
                ></el-cascader>
                <DateChooseQi
                    v-if="defaultDate"
                    :defaultDate="defaultDate"
                    :defaultDateTypeList="defaultDateTypeList"
                    @dateChange="dateChange"
                ></DateChooseQi>
                <el-button type="primary" size="mini" @click="toManage">
                    <!-- <i class="el-icon-s-tools"></i> -->
                    管理中心</el-button>
            </el-col>
        </el-row>
    </div>
</template>
<script>
    import Vue from 'vue';
    import DateChooseQi from '../components/common/DateChooseQi.vue';
    import utils from '../utils/utils';
    import {Cascader, MessageBox} from 'element-ui';
    Vue.use(Cascader)
export default {
    components:{
        DateChooseQi
    },
    data(){
        return {
            filiale: ['0'],
            filialeActive:'1',
            filialeProps:{ expandTrigger: 'hover', checkStrictly: false },
            filialeOptions:[],
            /*filialeOptions:[{"value":"999","label":"集团公司","disabled":null,"children":null},
                {"value":"0","label":"北京公司","disabled":null,"children":null},
                {"value":"BRANCH_SELECT","label":"分公司","disabled":0,
                    "children":[
                        {"value":"105","label":"城区一分公司","disabled":null,"children":null},
                        {"value":"106","label":"城区二分公司","disabled":null,"children":null},
                        {"value":"107","label":"城区三分公司","disabled":null,"children":null},
                        {"value":"108","label":"昌平分公司","disabled":null,"children":null},
                        {"value":"109","label":"顺义分公司","disabled":null,"children":null},
                        {"value":"110","label":"通州分公司","disabled":null,"children":null},
                        {"value":"111","label":"房山分公司","disabled":null,"children":null},
                        {"value":"112","label":"大兴分公司","disabled":null,"children":null},
                        {"value":"113","label":"怀柔分公司","disabled":null,"children":null},
                        {"value":"114","label":"平谷分公司","disabled":null,"children":null},
                        {"value":"115","label":"延庆分公司","disabled":null,"children":null},
                        {"value":"116","label":"密云分公司","disabled":null,"children":null}]},
                {
                    "value": "DEPT_SELECT", "label": "职能部门", "disabled": null,
                    "children": [{"value": "101", "label": "客户体验管理部", "disabled": null, "children": null},
                        {"value": "102", "label": "市场经营部", "disabled": null, "children": null},
                        {"value": "104", "label": "平台生态部", "disabled": null, "children": null},
                        {"value": "117", "label": "信息系统部", "disabled": null, "children": null},
                        {"value": "118", "label": "内审部", "disabled": null, "children": null},
                        {"value": "119", "label": "政企客户部", "disabled": null, "children": null},
                        {"value": "120", "label": "测试专用部门", "disabled": null, "children": null},
                        {"value": "121", "label": "全渠道运营中心", "disabled": null, "children": null}
                    ],
                }],*/
            /*defaultDate: this.$route.params.date !== '-1' ? this.$route.params.date : '',*/
            //defaultDate:this.$route.params.date !== '-1' ? utils.formatDate(new Date(), 'day', '-') : '',
            /*defaultDateTypeList:["月", "周","日"]*/
            defaultDateTypeList:this.$store.state.dateCycle
        }
    },
    props:['defaultDate'],
    created(){
      this.initFiliale();
    },
    methods:{
        toManage(){
                this.$store.commit('setProductInfo',{
                    productCode:'',
                    productName:'管理中心'
                })
                this.$router.push({
                    name:'experienceManage'
                })
            },
        filialeChange(val){
            this.$emit('areaChange',val);
        },
        initFiliale(){
            const that = this;
            that.$http
                .post('/experience-service/experienceAuth/getDeptBranchSelect', {})
                .then((res) => {
                    this.filialeActive = res.data.active;
                    this.filialeOptions = res.data.branchTreeSelectData;
                    if(res.data.defaultBranchId){
                        this.filiale = [res.data.defaultBranchId];
                    }
                    if(res.data.defaultDeptId){
                        this.filiale =res.data.defaultDeptId.split(',');
                    }
                    console.log()
                    this.$emit('filialeInit',this.filiale);
                }).catch((res) =>{
            })
        },
        // 日期组件数据变化回调
        dateChange(val) {
            this.$emit('timeChange',val);
        },
        //新增场景
        addScene(){
            const html='<div style="text-align: left;font-size: 12px;color: #6C7278;">补充场景请下载场景补充模板，按照模板格式填写以下内容：指标名称、\n' +
                '指标口径、计算公式及数据来源。编辑好后请点击上传按钮。可在管理\n' +
                '中心查看场景补充进度。</div>';
            MessageBox.alert(html, {
                dangerouslyUseHTMLString: true,
                title:'新增业务体验历程说明',
                confirmButtonText: ' 上传 ',
                cancelButtonText: '下载模板',
                showCancelButton:true,
                distinguishCancelAndClose:true,
                cancelButtonClass:'mgbox-cancel',
                confirmButtonClass:'mgbox-confirm',
                center: true,
                customClass:'def-mgbox',
                beforeClose(action, instance, done){
                    if(action=='close'){
                        console.log('qx');
                        done();
                    }
                    if(action == 'cancel'){
                        console.log('下载');
                    }
                    if(action == 'confirm'){
                        console.log('上传');
                        done();
                    }
                }
            });
        },
    }
};
</script>
<style scoped lang='less'>
.el-icon-s-tools{
    margin-right: 6px;
}
.el-button--primary{
     margin-left: 15px !important;
     background: #F19833;
    border-radius: 4px;
    font-family: SourceHanSansSC-Normal;
    font-size: 14px;
    color: #262626;
    letter-spacing: -0.27px;
}
 .idx-head{
     height: 102px;
     width: 100%;
    .def-row{
        height: 100%;
        .def-col{
            height: 100%;
            /*display: flex;
            align-items: center;*/
            // line-height: 60px;
        }
        .def-col:first-child{
            padding-top: 54px;
            text-align: left;
            span{
                
                font-family: SourceHanSansSC-Medium;
                font-size: 20px;
                color: #4E4E4E;
                letter-spacing: -0.39px;
            }
            // span:before{
            //     content: "";
            //     position: absolute;
            //     left: 5px;
            //     top: 18px;
            //     margin: auto;
            //     // width:4px;
            //     // height:24px;
            //     // background: #2F9CFA;
            // }
            .l-oper{
                width: 140px;
                height: 24px;
                background: #FFFAF3;
                border: 1px solid #FFD4B1;
                border-radius: 12px;
                margin-left: 15px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                cursor: pointer;
                .el-icon-question {
                    color: #FFA862;
                    margin-right: 5px;
                }
            }
        }
        /deep/ .el-col:last-child{
            /*justify-content: flex-end;*/
            margin-top: 30px;
            text-align: right;
            .cascader {
                span {
                    color: #909299;
                }
                .el-icon-arrow-down:before {
                    content: "\25BC";
                    color: #909299;
                    font-size: 12px;
                }
            }
        }
    }
     /deep/ .dateChoose {
         .radioArea{
             line-height: inherit;
         }
         .radioArea > label{
             font-size: 12px;
             color: #3F444A;;
         }
         .controlDom.showit{
             margin:0 0 0 10px !important;
         }
         .controlDom input{
             border-color: #E0E0E0 !important;
             background-color: white;
             color: #45494D;
         }
     }
     /deep/ .el-icon-date:before{
         color: #45494D;
     }
     /deep/ .el-cascader{
         margin-right: 20px;
        .el-input .el-input__inner{
            /*min-width: 150px;*/
            min-width: 236px;
            font-size: 14px;
            color: #45494D;
            border-radius: 4px;
            // padding-left: 5px;
        }
     }
     /deep/ .el-input__inner{
         border-color: #E0E0E0 !important;
         padding-right: 0px;
     }
     /deep/ .dateSelect .el-icon-arrow-up::before{
         content: "\25B2";
         color: #909299;
         font-size: 12px;
     }
 }

</style>