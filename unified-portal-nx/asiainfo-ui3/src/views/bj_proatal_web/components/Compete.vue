<template>
  <div class="CompetePage" v-loading="loading">
    <el-row :gutter="20">
      <el-col :span="8" v-for="item in filtData" :key="item.id">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>{{item.title}}</span>
          </div>
          <div class="box-card-content">
            <compare-line
              :barnumIsShow='barnumIsShow'
              :xAxis="item.xAxis"
              :data="item.data"
              :leadList="item.leadList"
              :notifyList="item.notifyList"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Vue from 'vue';
import { Card, Row, Col } from 'element-ui';
import CompareLine from '../components/common/CompareLine.vue';

Vue.use(Card)
  .use(Row)
  .use(Col);
export default {
  name: 'Compete',
  components: {
    CompareLine,
  },
  props: {
    data: {
      type: Array,
      default: () => [
        {
          indicatName: '移动装机/移机满意度',
          competitiveFigureId: '11',
          idOperator: {
            127: '电信',
            18: '移动',
            128: '联通',
          },
          nameList: ['201905', '201904', '201903'],
          mobileList: ['37141', '108731', '22466'],
          unicomList: ['78925', '49357', '52759'],
          telecomList: ['12952', '73485', '24141'],
        },
      ],
    },
    barnumIsShow: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    filtData() {
      const resultList = [];
      this.data.forEach((it) => {
        var nameList = it.nameList.map(name=>{
          name = name.substring(0,4)+'/'+name.substring(4,6);
          return name;
        })
        resultList.push({
          title: it.indicatName,
          id: it.competitiveFigureId,
          xAxis: nameList,
          leadList: it.leadList,
          notifyList: it.notifyList,
          data: [
            {
              name: '联通',
              data: it.unicomList,
            },
            {
              name: '电信',
              data: it.telecomList,
            },
            {
              name: '移动',
              data: it.mobileList,
            },
          ],
        });
      });
      return resultList;
    },
  },
  data() {
    return {
      value: '',
      loading: false,
    };
  },
  mounted() {},
  methods: {},
  watch: {},
};
</script>

<style lang='less'>
.CompetePage {
  position: relative;
  .el-card {
    border: 0px;
    border-radius: 0px;
  }
  .box-card {
    text-align: left;
    margin-bottom: 20px;
    .el-card__header {
      background-color: #fff;
      padding: 12px 20px;
      font-family: SourceHanSansSC-Regular;
      font-size: 16px;
      color: #262626;
      border-bottom: 0px;
    }
    .el-card__body {
      padding: 10px;
    }
    .box-card-content {
      position: relative;
      height: 15vw;
    }
  }
}
</style>
