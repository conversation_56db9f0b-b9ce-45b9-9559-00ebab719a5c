<template>
    <div class="Radar">
        <!-- <v-chart ref="RadarCharts" class="echarts" id="RadarCharts" :options="pieOptions" autoresize /> -->
        <div ref="chart" class="echarts"></div>
    </div>
</template>
<script>
import Vue from 'vue';
/*import ECharts from 'vue-echarts';
import 'echarts/lib/chart/radar';
import 'echarts/lib/component/legend';
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/title';
import 'echarts/lib/component/markLine';*/

import * as echarts from "echarts";

// Vue.component('v-chart', ECharts);
export  default {
    data(){
        return {
            activeName:'攻击途径',
        }
    },
    props:{
        indicator:{
            type:Array,
            default:()=>[
                {
                    name: '攻击途径',
                    max: 10
                },
                {
                    name: '攻击难度',
                    max: 10
                },
                {
                    name: '权限要求',
                    max: 10
                },
                {
                    name: '机密性影响',
                    max: 10
                },
                {
                    name: '完整性影响',
                    max: 10
                },
                {
                    name: '可用性影响',
                    max: 10
                }
            ]
        },
        indicatorInfo:{
            type:Object,
        },
        value:{
            type:Array,
            default: ()=>[3, 5, 8, 9, 10, 8]
        }},
    computed: {
        pieOptions() {
            let that = this;
            console.log(this.value);
            const buildSeries = function(data){
                const helper = data.map((item, index) => {
                    const arr = new Array(data.length);
                    arr.splice(index, 1, item);
                    console.log(arr);
                    return arr;
                })
                return [data, ...helper].map((item, index) => {
                    let a='',symbol='none';
                    if(index>0 && data[index - 1]>0){
                        a= that.indicator[index - 1].name + '（满意度）：' + data[index - 1];
                        symbol='circle';
                    }
                    console.log(a);
                    return {
                        name: '满意度',
                        type: 'radar',
                        symbolSize: 6,
                        data: [{
                            itemStyle: {
                                normal: {
                                    color: '#51B7FF',
                                    lineStyle: {
                                        color: '#51B7FF',
                                    },
                                },
                            },
                            value: item
                        }
                        ],
                        symbol:symbol,
                        areaStyle: {
                            normal: {
                                width: 1,
                                opacity: 0.3,
                            },
                        },
                        tooltip: {
                            show: true,
                            formatter: function() {
                                return a;
                            }
                        },
                    }
                })
            }
            let series = buildSeries(this.value);
            return {
                tooltip: {},
                radar: {
                    shape: "circle",
                    splitArea: {
                        show: false
                    },
                    splitNumber: 4,
                    name: {
                        formatter: function (params) {
                            var str = '';
                            if (params == that.activeName) {
                                str = `{active|${params}}`;
                            } else {
                                str = `{deactive|${params}}`;
                            }
                            return str;
                        },
                        rich: {
                            active: {
                                color: '#FFFFFF',
                                backgroundColor: '#4993FF',
                                borderColor: '#4993FF',
                                borderWidth: 1,
                                borderRadius: 3,
                                padding: 5
                            },
                            deactive: {
                                color: '#575B61',
                                backgroundColor: '#F6F9FA',
                                borderColor: '#D5DADE',
                                borderWidth: 1,
                                borderRadius: 3,
                                padding: 5
                            }
                        }
                    },
                    nameGap: 8,
                    triggerEvent: true,
                    indicator: this.indicator,
                },
                series: series
            }
          /* return {
               tooltip: {
                   show: true,
                   trigger: 'item',
                   padding: [5, 10],
               },
               radar: {
                   shape: "circle",
                   splitArea:{
                       show:false
                   },
                   splitNumber: 4,
                   name: {
                       formatter: function(params) {
                           var str = '';
                           if(params == that.activeName){
                               str = `{active|${params}}`;
                           }else{
                               str = `{deactive|${params}}`;
                           }
                           return str;
                       },
                       rich: {
                           active: {
                               color: '#FFFFFF',
                               backgroundColor: '#4993FF',
                               borderColor:'#4993FF',
                               borderWidth:1,
                               borderRadius: 3,
                               padding: 5
                           },
                           deactive:{
                               color: '#575B61',
                               backgroundColor: '#F6F9FA',
                               borderColor:'#D5DADE',
                               borderWidth:1,
                               borderRadius: 3,
                               padding: 5
                           }
                       }
                   },
                   nameGap:8,
                   triggerEvent: true,
                   indicator:this.indicator,
               },
               series: [{
                   name: '满意度',
                   type: 'radar',
                   areaStyle: {
                       normal: {
                           width: 1,
                           opacity: 0.3,
                       },
                   },
                   data: [{
                       itemStyle: {
                           normal: {
                               color: '#51B7FF',
                               lineStyle: {
                                   color: '#51B7FF',
                               },
                           },
                       },
                       value: this.value,
                   }]
               }]
           }*/
        }
    },
    mounted(){
        /*const that = this;
        const chartsDom = this.$refs.RadarCharts.chart;*/
        
    },

     mounted(){
              this.initChart();

          },
          methods:{
            initChart(){
                this.chart = echarts.init(this.$refs.chart, null, {
                    render: "svg"
                });

                this.chart.on('click', 'radar', (params) => {
                      console.log(params)
                      if (params.targetType === 'axisName'){
                          const id = params.name.indexOf('|');
                          const idxName = params.name.substring(id+1,params.name.length-1);
                          this.activeName = idxName;
                          console.log(this.activeName)
                          chartsDom.setOption(this.pieOptions);
                          this.$emit('clickSecondaryIndex',this.indicatorInfo[this.activeName]);
                      }
                  })

                this.updateOption();

                window.addEventListener("resize", ()=>{
                    this.chart.resize();
                });
            },

            updateOption(){
              let _this = this;
              this.chart.setOption(this.pie);
            }
          }
};
</script>
<style scoped lang="less">
    .Radar {
        width: 100%;
        height: 100%;
        .echarts {
            width: 100%;
            height: 100%;
        }
    }
</style>