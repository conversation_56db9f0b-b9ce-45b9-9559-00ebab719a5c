<template>
  <div class="echart-box" :style="{ height: height, width: width }">
    <div ref="lineBarChart" :style="{ width: '100%', height: '100%' }"></div>
  </div>
</template>
<script>
export default {
  props: {
    height: String,
    width: String,
    xName: Array,
    datas: Array,
    stack: Boolean,
    colors: Array,
    legends: Array,
  },
  data() {
    return {};
  },
  mounted() {
    this.drawChart();
    // console.log(this.datas);
  },
  watch: {
    datas() {
      this.drawChart();
    },
  },
  methods: {
    drawChart() {
      let lineValue = [];
      let barValue = [];
      let that = this;
      this.datas.forEach((item) => {
        lineValue.push(item.ratio);
        barValue.push(item.satisfacValue);
      });
      const myChart = this.$echarts.init(this.$refs.lineBarChart);
      const option = {
        // legend: {
        //     data: this.legends,
        // },
        // xAxis: {
        //     type: 'category',
        //     axisLine: { show: false },
        //     axisTick:{     //x轴刻度线
        //         "show":false
        //     },
        //     data: this.xName
        // },
        // yAxis: [
        //     {
        //         type: 'value',
        //         axisLine: { show: false },
        //         axisTick:{     //y轴刻度线
        //             "show":false
        //         },
        //         axisLabel: {
        //             formatter: '{value}'
        //         },
        //         splitLine :{ //网格线
        //             lineStyle:{
        //                 type:'dotted' //设置网格线类型 dotted：虚线 solid:实线
        //             }
        //         },
        //         max:100,
        //         scale:true,
        //         boundaryGap: [0.1, 0.1]

        //     },{
        //         type: 'value',
        //         axisLine: { show: false },
        //         axisTick:{     //y轴刻度线
        //             "show":false
        //         },
        //         axisLabel: {
        //             formatter: '{value}'
        //         },
        //         splitLine :{ //网格线
        //             show:false
        //         }
        //         // max : 100,
        //         // min : 0
        //         // splitNumber : 5
        //     }
        // ],
        // series: [
        //     {
        //         name: this.legends[0],
        //         data:barValue,
        //         type: 'bar',
        //         smooth: true,
        //         barWidth : 24,
        //         color:this.colors[0]
        //     },
        //     {
        //         name: this.legends[1],
        //         data: lineValue,
        //         type: 'line',
        //         color:this.colors[1],
        //         yAxisIndex: 1
        //     }
        // ]
        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(255,255,255)",
          formatter: function (val) {
            let list = [];
            that.datas.forEach((item) => {
              if (item.city === val[0].axisValue) {
                list.push(
                  `<div style="padding:5px;">
                                <span style="width:140px;display:inline-block;">${val[0].axisValue}</span>
                                <span style="width:60px;display:inline-block; text-align: center;">数值</span>
                                <span style="width:60px;display:inline-block; text-align: center;">排名</span>

                            </div>
                            <div style="padding:5px;">

                                <span style="width:140px;display:inline-block;">
                                    <i style="display: inline-block;width: 10px;height: 10px;background:${val[0].color}
                                    ;margin-right: 5px;border-radius: 50%;}"></i>
                                    ${val[0].seriesName}</span>
                                <span style="width:60px;display:inline-block; text-align: center;">${val[0].value}</span>
                                <span style="width:60px;display:inline-block; text-align: center;">${item.satisRank}</span>

                            </div>
                            <div style="padding:5px;">

                                <span style="width:140px;display:inline-block;">
                                    <i style="display: inline-block;width: 10px;height: 10px;background:${val[1].color}
                                    ;margin-right: 5px;border-radius: 50%;}"></i>
                                    ${val[1].seriesName}</span>
                                <span style="width:60px;display:inline-block; text-align: center;">${val[1].value}</span>
                                <span style="width:60px;display:inline-block;text-align: center;">${item.ratioRank}</span>

                            </div>
                            `
                );
              }
            });
            return '<div class="showBox">' + list + "</div>";
          },
        },
        grid: [
          {
            top: 40,
            left: 60,
            right: 70,
            bottom: 20,
          },
        ],
        legend: {
          data: ["Evaporation", "Temperature"],   selectedMode: false, // 是否允许点击
          icon: "circle",
          itemWidth: 10, // 设置宽度
          itemHeight: 10, // 设置高度
          itemGap: 40, // 设置间距
        },

        xAxis: [
          {
            type: "category",
            axisLine: { show: false },
            axisTick: {
              //x轴刻度线
              show: false,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "Precipitation",
            axisLine: { show: false },
            axisTick: {
              //y轴刻度线
              show: false,
            },
            axisLabel: {
              formatter: "{value}",
            },
            splitLine: {
              //网格线
              lineStyle: {
                type: "dotted", //设置网格线类型 dotted：虚线 solid:实线
              },
            },
            max: 100,
            scale: true,
            boundaryGap: [0.1, 0.1],
          },
          {
            type: "value",
            name: "Temperature",
            axisLine: { show: false },
            axisTick: {
              //y轴刻度线
              show: false,
            },
            axisLabel: {
              formatter: "{value}",
            },
            splitLine: {
              //网格线
              show: false,
            },
            // max : 100,
            // min : 0
            // splitNumber : 5
          },
        ],
        series: [
          {
            name: "Evaporation",
            type: "bar",
            barWidth: 24,
            color: this.colors[0],
            data: [
              2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4,
              3.3,
            ],
          },
          {
            name: "Temperature",
            type: "line",
            yAxisIndex: 1,
            color: this.colors[1],
            data: [
              2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2,
            ],
          },
        ],
      };
      myChart.setOption(option);
      setTimeout(function () {
        window.onresize = function () {
          myChart.resize();
        };
      }, 200);
    },
  },
};
</script>
<style lang="less" scoped>
.echart-box {
  width: 100%;
}
</style>
