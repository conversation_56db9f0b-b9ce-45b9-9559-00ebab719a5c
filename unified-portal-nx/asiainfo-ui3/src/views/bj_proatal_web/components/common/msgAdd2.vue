<template>
  <div style="padding-right:40px;">
    <el-form ref="form" :model="form" :rules="rules">
      <el-form-item v-if="isAddMsgTep" label="模版名称" :label-width="formLabelWidth" prop="title">
        <el-input v-model="form.title" placeholder="请输入模版名称" :size="conditonsize" style="width:50%" />
      </el-form-item>
      <el-form-item v-if="isAddMsgTep" label="模版类型" :label-width="formLabelWidth" prop="type">
        <el-select v-model="form.type" :size="conditonsize" placeholder="请选择类型" style="width:50%">
          <el-option
            v-for="item in typeopts"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="isAddMsgTep?false:true" label="选择模版" :label-width="formLabelWidth">

        <el-table
          ref="multipleTable"
          stripe
          :data="tableData"
          tooltip-effect="dark"
          style="width: 100%"
          class="selectionTable"
          border
          @row-click="singleElection"
        >
          <el-table-column label="选择模版">
            <template slot-scope="scope">
              <div>
                <el-radio v-model="templateSelection" class="radio" :label="scope.$index">&nbsp;</el-radio>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="模版名称"
            prop="title"
            show-overflow-tooltip
          />

        </el-table>
        <!-- 分页功能 -->
        <div style="padding:10px 0;background:#fff;">
          <el-pagination
            v-if="tableData"
            :current-page="page.current"
            :page-sizes="[5,10, 20, 50, 100]"
            :page-size="page.size"
            layout="total,sizes, prev, pager, next, jumper"
            :total="page.total"
            :pager-count="5"
            @size-change="sizeChange"
            @current-change="pageCurrentChange"
          />
        </div>

      </el-form-item>

      <el-form-item :label="isAddMsgTep?'模版内容':'短信内容'" label-width="120px" prop="content">
        <el-input
          v-model="form.content"
          :rows="5"
          type="textarea"
          placeholder="请输入内容"
          maxlength="999"
          show-word-limit
        />
      </el-form-item>

      <el-form-item v-if="isAddMsgTep?false:false" label="" label-width="120px">
        <el-checkbox v-model="form.checked">是否保存为模版</el-checkbox>
        <!-- <el-input v-if="form.checked" v-model="form.title" placeholder="请输入模版名称" :size="conditonsize" style="width:40%;margin-left:10px;" /> -->
      </el-form-item>

      <el-form-item v-if="form.checked" label="模版名称" :label-width="formLabelWidth" prop="title">
        <el-input v-model="form.title" placeholder="请输入模版名称" :size="conditonsize" style="width:50%" />
      </el-form-item>
      <el-form-item v-if="form.checked" label="模版类型" :label-width="formLabelWidth" prop="type">
        <el-select v-model="form.type" :disabled="Boolean(preType)" :size="conditonsize" placeholder="请选择类型" style="width:50%">
          <el-option
            v-for="item in typeopts"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

    </el-form>

    <div
      style="text-align:center"
      class="dialog-footer"
    >
      <el-button v-loading="btnLoading" :disabled="sendbtnabled" type="primary" size="mini" @click="submitForm('form')">{{ !isAddMsgTep && form.checked? `${confirmtxt}并保存为模版` : confirmtxt }}</el-button>
      <el-button size="mini" @click="cancel">取 消</el-button>
    </div>

  </div>
</template>
<script>
import { queryMultiMessageInfo, csmmessagetemplateAdd, csmmessagetemplateGet, csmmessagetemplateEdit, csmmessagetemplate, querySimpleMessageInfo } from '@/api/customer/index'

export default {
  props: {
    checkRow: {
      type: Object,
      required: false,
      default: () => {}
    },
    isAddMsgTep: {
      type: Boolean,
      required: false,
      default: () => false
    },
    confirmtxt: {
      type: String,
      required: false,
      default: () => '发送'
    },
    preType: {
      type: String,
      required: false,
      default: () => ''
    }
  },
  data() {
    return {
      sendbtnabled: false,
      btnLoading: false,
      show: true,
      templateSelection: '',
      templateRadio:'',
      page: {
        current: 1,
        size: 5,
        total: 0
      },

      tableData: [],
      formLabelWidth: '120px',
      tempopts: [], // 模版数据
      conditonsize: 'small',
      form: {
        'content': '',
        'createTime': '',
        'createUserId': '',
        'createUserName': '',
        'deleteStatus': 0,
        'id': 0,
        'title': '',
        'type': '',
        radio: '', // 选择的短信模版
        checked: ''
      },
      // 0:关怀客群，1：修复客群
      typeopts: [
        { label: '主动关怀', value: '0' },
        { label: '修复后评价', value: '1' }
      ],
      rules: {
        title: [
          { required: true, message: '请输入模版名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择模版类型', trigger: 'blue' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          { min: 1, max: 999, message: '内容过长', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    templateSelection: {
      deep: true,
      handler: function(v, oldv) {
        if(this.tableData[v]){
          this.form.content = this.tableData[v].content
        }
      }
    }
  },
  mounted() {
    console.log('this.preType:', this.preType)
    console.log('this.checkrow:', this.checkRow)
    // 预设关怀类型
    if (this.preType) {
      this.form.type = String(this.preType)
    }
    this.show = false
    // 模版
    if (this.checkRow && this.checkRow.id && this.isAddMsgTep) {
      this.form.id = this.checkRow.id
      csmmessagetemplateGet(this.checkRow.id).then(res => {
        const { code, data } = res
        console.log('res:', res)
        if (code == 200) {
          this.form = Object.assign(this.form, data)
          console.log('this.form:', this.form)
        }
      })
    }

    // 短信 查询所有模版 然后根据
    if (!this.isAddMsgTep) {
      // 获取所有模版
      this.queryList()
    }
  },
  methods: {
    sizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.page.size = val
      this.page.current = 1
      this.queryList()
      // 下方添加查询逻辑
    },
    pageCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.page.current = val
      this.queryList()
      // 下方添加查询逻辑
    },
    // 查询表格数据
    queryList() {
      const { page } = this
      const params = { pagination: { currentPage: page.current, pageSize: page.size }}
      // /
      this.templateSelection = ''
      csmmessagetemplate(params).then(res => {
        const { code, data } = res
        if (code == 200) {
          const { records, total, current } = data
          // 处理不满意指标
          this.tableData = records || []
           records.forEach((i,j) => {
            if(this.templateRadio == i.id) {
              this.templateSelection = j;
            }
          });
          this.page.total = total
          this.page.current = current
        }
      })
    },
     singleElection(row) {
      this.templateSelection = this.tableData&&this.tableData.indexOf(row);
      this.templateRadio = row.id;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const userInfo = JSON.parse(localStorage.getItem('userInfo'))
          const { title, type, content, id, createUserName, createUserId, checked } = this.form
          const p = {
            title,
            type,
            content,
            createUserName: createUserName || userInfo.name,
            createUserId: createUserId || userInfo.loginName,
            id: id || 0
          }
          this.btnLoading = true
          console.log(this.$route.path)

          if (this.isAddMsgTep) { // 模版管理
            if (id) { // 调用编辑接口
              csmmessagetemplateEdit(p).then(res => {
                const { code } = res
                if (code == 200) {
                  this.$message.success('编辑模版成功')
                  this.sendbtnabled = true
                  this.$emit('cancel')
                }
              }).finally(() => {
                this.btnLoading = false
              })
            } else { // x新增
              // 调后端保存短信模版
              csmmessagetemplateAdd(p).then(res => {
                const { code } = res
                if (code == 200) {
                  this.sendbtnabled = true
                  this.$message.success('新增模版成功')
                  this.$emit('cancel')
                }
              }).finally(() => {
                this.btnLoading = false
              })
            }
          } else { // 短信
            // 保存为模版
            if (checked) {
              if (!title) {
                this.$message.error('请输入模版名称')
                return
              }

              csmmessagetemplateAdd(p).then(res => {
                const { code } = res
                if (code == 200) {
                  this.sendbtnabled = true
                  this.$message.success('保存模版成功')
                  this.$emit('cancel')
                }
              })
              // 调用发送接口

              const psend = {
                id: this.checkRow.id,
                customerCode: this.checkRow.code,
                content: this.form.content
              }
              if (this.$route.path.indexOf('customergroup') != -1) { // 客群
                queryMultiMessageInfo(psend).then(res => {
                  if (res.code == 200) {
                    this.$message.success('已提交审批')
                  }
                }).catch(res => {
                  this.$message.error('提交审批失败')
                }).finally(() => {
                  this.$emit('cancel')
                  this.btnLoading = false
                })
              } else { // 客户
                querySimpleMessageInfo(psend).then(res => {
                  if (res.code == 200) {
                    this.$message.success('已提交审批')
                  }
                }).catch(res => {
                  this.$message.error('提交审批失败')
                }).finally(() => {
                  this.$emit('cancel')
                  this.btnLoading = false
                })
              }
            } else {
              const psend = {
                id: this.checkRow.id,
                customerCode: this.checkRow.code,
                content: this.form.content
              }
              if (this.$route.path.indexOf('customergroup') != -1) { // 客群
                queryMultiMessageInfo(psend).then(res => {
                  if (res.code == 200) {
                   this.$message.success('提交审批成功')
                    this.$emit('cancel','1')
                  }
                }).catch(res => {
                  this.$message.error('提交审批失败')
                }).finally(() => {
                 
                  this.btnLoading = false
                })
              } else { // 客户
                querySimpleMessageInfo(psend).then(res => {
                  if (res.code == 200) {
                    this.$message.success('已提交审批')
                  }
                }).catch(res => {
                  this.$message.error('提交审批失败')
                }).finally(() => {
                  this.$emit('cancel')
                  this.btnLoading = false
                })
              }
            }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    handleClose() {
      this.show = false
    }

  }
}
</script>
<style lang="scss" scoped>
  /deep/.selectionTable.el-table.el-table--medium th.el-table__cell{
   padding:0px 0 !important;
   text-align: center;
 }
 /deep/ .el-cascader__tags{
  .el-tag{
    >span{
      flex: auto !important;
    }
  }
}
/deep/ .el-select__tags{
    .el-tag{
        >span{
        flex: auto !important;
        }
    }
}
/deep/ .el-tag.el-tag--info{
    display: inline-block;
}

 /deep/.selectionTable.el-table.el-table--medium td.el-table__cell{
   padding:2px 0 !important;
   text-align: center;
 }
    /deep/.el-pagination{text-align: right;}
    /deep/.el-radio__label{
      display:none
    }
</style>
