<template>
    <div class="header" style="margin-top:0px;">
        <div class="num">{{num}}</div>
        <div class="text">{{text}}</div>
    </div>
</template>
<script>
    export default {
        name:"NumberTitle",
        props: {
            num:String,
            text:String
        }
    };
</script>
<style scoped lang="less">
    .header {
        position: relative;
        margin-top:20px;
        .num {
            font-size:56px;
            font-family: SourceHanSansSC-Bold;
            font-weight: bold;
            color: #FCD0A4;
            position: relative;
            &::after {
                content: '';
                width: 328px;
                height: 8px;
                display: inline-block;
                background: linear-gradient(90deg, rgb(255, 165, 31) 0%, rgb(245, 245, 245) 100%);
                position: absolute;
                left:0px;
                bottom: 8px;
            };
        }

        .text {
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-style: normal;
            font-size: 32px;
            line-height: 43px;
            position: absolute;
            left:0px;
            bottom: 4px;
        }
    }

</style>


