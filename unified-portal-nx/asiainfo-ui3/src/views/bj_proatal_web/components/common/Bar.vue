<template>
<div ref='chart'  style="width: 800px;height: 800px"></div>
</template>

<style>
/**
 * The default size is 600px×400px, for responsive charts
 * you may need to set percentage values as follows (also
 * don't forget to provide a size for the container).
 */
.echarts {
  width: 100%;
  height: 100%;
}
</style>

<script>
import * as echarts from "echarts";

export default {
  components: {
  },
  data() {
    const data = [];
    return {
      bar: {
        title: {
          text: '手机上网分公司情况',
          x: 'center',
          y: 'top',
          textStyle: {
            fontFamily: 'normal',
            fontSize: 25,
          },
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          itemWidth: 15,
          itemHeight: 15,
          data: ['3月完成值', '2月完成值'],
          icon: 'rect',
          itemGap: 25,
          x: 'center',
          y: 790,
        },
        toolbox: {


        },
        calculable: true,

        xAxis: [
          {
            type: 'category',
            data: ['分公司1', '分公司2', '分公司3', '分公司4', '分公司5', '分公司6', '分公司7', '分公司8', '分公司9', '分公司10', '分公司11', '分公司12'],
            axisTick: {
              show: false,
            },
            axisLabel: {
              rotate: 45,
              margin: 15,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            min: 0,
            max: 120,
          },

        ],

        series: [
          {
            name: '3月完成值',
            type: 'bar',
            barCategoryGap: 20,
            itemStyle: {
              normal: {
                color: 'rgba(38,88,149,0.9)',
              },
            },
            data: [90, 70, 80, 82, 85, 85, 78, 95, 78, 98, 96, 78],
            barGap: '15%',
          },
          {
            name: '2月完成值',
            type: 'bar',
            data: [80, 90, 78, 75, 76, 78, 90, 90, 88, 98, 96, 70],


          },
        ],
      },
    };
  },

  mounted(){
      this.initChart();

  },
  methods:{
    initChart(){
        this.chart = echarts.init(this.$refs.chart, null, {
            render: "svg"
        });

        this.updateOption();

        window.addEventListener("resize", ()=>{
            this.chart.resize();
        });
    },

    updateOption(){
      let _this = this;
      this.chart.setOption(this.bar);
    }
  }
};
</script>
