<template>
  <div class="TrendBar">
    <!-- <v-chart class="echarts" :options="echartOptions" autoresize/> -->
    <div ref='chart' class="echarts"></div>
  </div>
</template>

<script>
import Vue from 'vue';
import * as echarts from "echarts";

export default {
  props: {
    // 折线数据
    lineData: {
      type: Object,
      default: () => ({
        name: '去年值',
        data: [100, 50, 80],
      }),
    },
    // 柱状数据
    barData: {
      type: Object,
      default: () => ({
        name: '完成值',
        data: [70, 90, 120],
      }),
    },
    // 目标值数据
    targetData: {
      type: Object,
      default: () => ({
        name: '目标值',
        data: [80, 70, 100],
      }),
    },
    xAxis: {
      type: Array,
      default: () => ['3月完成值', '2月完成值', '1月完成值'],
    },
    barnumIsShow: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      chart:null
    };
  },
  computed: {
    echartOptions() {
      const legend = [];
      if (this.barData) {
        legend.push(this.barData.name);
      }
      if (this.lineData) {
        legend.push(this.lineData.name);
      }
      if (this.targetData) {
        legend.push(this.targetData.name);
      }
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#E0E0E0',
            },
          },
          formatter: (params) => {
            let toolTip = '';
            params.forEach((item) => {
              if (item.seriesName !== 'trend') {
                // toolTip += item.marker;
                toolTip += item.seriesName;
                toolTip += ':';
                toolTip += item.value;
                toolTip += '<br/>';
              }
            });
            return toolTip;
          },
        },
        legend: {
          top: 0,
          right: 0,
          itemWidth: 25,
          itemHeight: 10,
          itemGap: 15,
          data: legend,
          textStyle: {
            fontSize: 12,
            color:'#8c8c8c'
          },
        },
        grid: {
          top: 40,
          left: 10,
          right: 10,
          bottom: 0,
          containLabel: true,
        },
        xAxis: {
          axisLine: {
            lineStyle: {
              color: '#E0E0E0',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#8c8c8c',
            interval: 0
          },
          data: this.xAxis,
        },
        yAxis: {
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#ccc',
              type: 'dashed',
            },
          },


          //scale: true,
        },
        series: [
          {
            name: this.barData.name,
            type: 'bar',
            barWidth: '20%',
            barMaxWidth:20,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(71, 184, 255, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(97, 135, 255, 1)',
                  },
                ]),
                label: {
                  show: this.barnumIsShow,
                  position: 'top',
                  formatter(p) {
                    return p.value;
                  },
                },
              },
            },
            data: this.barData.data,
          },
          {
            name: this.lineData.name,
            type: 'line',
            data: this.lineData.data,
            itemStyle: {
              color: '#F5B23F',
            },
            lineStyle: {
              normal: {
                shadowColor: 'rgba(0,0,0,0.2)',
                shadowBlur: 10,
                shadowOffsetY: 10,
                color:'#F5B23F'
              },
            },
            // symbol: 'none',
            symbolSize: 1,
            smooth: true,
          },
          {
            name: this.targetData.name,
            type: 'line',
            step: 'middle',
            data: this.targetData.data,
            itemStyle: {
              color: '#ff7774',
            },
            lineStyle: {
              normal: {
                shadowColor: 'rgba(0,0,0,0.2)',
                shadowBlur: 10,
                shadowOffsetY: 10,
                type: 'dashed',
              },
            },
          },
          /*{
            name: 'trend',
            type: 'line',
            smooth: true,
            data: this.barData.data,
            itemStyle: {
              color: '#77c2fa',
            },
            symbolSize: 0,
          },*/
        ],
      };
    },
  },
  mounted(){
      this.initChart();

  },
  watch:{
    barnumIsShow(){
      this.updateOption();
    }
  },
  methods:{
    initChart(){
        this.chart = echarts.init(this.$refs.chart, null, {
            render: "svg"
        });

        this.updateOption();

        window.addEventListener("resize", ()=>{
            this.chart.resize();
        });
    },

    updateOption(){
      let _this = this;

      this.chart.setOption(this.echartOptions);
    }
  }
};
</script>


<style lang='less'>
.TrendBar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  .btn-area {
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    z-index: 100;
  }
  .echarts {
    width: 100%;
    height: 100%;
  }
}
</style>
