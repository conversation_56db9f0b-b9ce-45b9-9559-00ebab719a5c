<template>
  <div class="CompanyBar">
    <div ref="chart" style="width:100%; height:100%;"></div>
  </div>
</template>

<script>
import Vue from 'vue';
import * as echarts from "echarts";
export default {
  props: {
    // 得分率
    scoreList: {
      type: Object,
      default: () => ({
        name: '得分率',
        data: [1.5, 1.9, 2.0],
      }),
    },
    // 去年值
    lastData: {
      type: Object,
      default: () => ({
        name: '上周期',
        data: [100, 50, 80],
      }),
    },
    // 当前值
    currData: {
      type: Object,
      default: () => ({
        name: '当前值',
        data: [70, 90, 120],
      }),
    },
    // 目标值数据
    targetData: {
      type: Object,
      default: () => ({
        name: '目标值',
        data: [80, 70, 100],
      }),
    },
    xAxis: {
      type: Array,
      default: () => ['3月完成值', '2月完成值', '1月完成值'],
    },
    barnumIsShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    /*echartOptions() {
      const legend = [];
      if (this.lastData) {
        legend.push(this.lastData.name);
      }
      if (this.currData) {
        legend.push(this.currData.name);
      }
      if (this.targetData) {
        legend.push(this.targetData.name);
      }
      if (this.scoreList) {
        legend.push(this.scoreList.name);
      }
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#E0E0E0',
            },
          },
        },
        legend: {
          top: 5,
          right: 10,
          itemWidth: 25,
          itemHeight: 10,
          itemGap: 15,
          data: legend,
          fontSize: 28,
          textStyle: {
            fontSize: 12,
            color:'#8c8c8c'
          },
        },
        grid: {
          top: 50,
          left: 10,
          right: 10,
          bottom: 0,
          containLabel: true,
        },
        // dataZoom: [
        //   {
        //     type: 'inside',
        //     start: 10,
        //     end: 90,
        //   },
        // ],
        xAxis: {
          axisLine: {
            lineStyle: {
              color: '#E0E0E0',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            color: '#8c8c8c',
            formatter: value => this.insert_flg(value, '\n', 3),
          },
          data: this.xAxis,
        },
        yAxis: [
          {
            scale: true,
            // name: '单位：万',
            nameTextStyle: {
              fontSize: 13,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: '#ccc',
                type: 'dashed',
              },
            },
          },
          {
            scale: true,
            nameTextStyle: {
              fontSize: 13,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: '#ccc',
                type: 'dashed',
              },
            },
          },
        ],
        series: [
          {
            name: this.lastData.name,
            type: 'bar',
            barWidth: '20%',
            data: this.lastData.data,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#5FAEFF',
                  },
                  {
                    offset: 1,
                    color: '#0682FF',
                  },
                ]),

              },
            },
          },
          {
            name: this.currData.name,
            type: 'bar',
            barWidth: '20%',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#FFC266',
                  },
                  {
                    offset: 1,
                    color: '#FF9900',
                  },

                ]),

                label: {
                  show: this.barnumIsShow,
                  position: 'top',
                  formatter(p) {
                    return p.value;
                  },
                },
              },
            },
            data: this.currData.data,
          },
          {
            name: this.scoreList.name,
            yAxisIndex: 1,
            type: 'line',
            // step: 'middle',
            data: this.scoreList.data,
            itemStyle: {
              color: '#3398FF',
            },
            // lineStyle: {
            //   normal: {
            //     shadowColor: 'rgba(0,0,0,0.2)',
            //     // shadowBlur: 10,
            //     shadowOffsetY: 10,
            //     width: 2.5,
            //   },
            // },
            symbolSize: 0,
          },
          {
            name: this.targetData.name,
            type: 'line',
            step: 'middle',
            data: this.targetData.data,
            itemStyle: {
              color: '#F5B23F',
            },
            lineStyle: {
              normal: {
                shadowColor: 'rgba(0,0,0,0.2)',
                shadowBlur: 10,
                shadowOffsetY: 10,
                type: 'dashed',
              },
            },
          },
        ],
      };
    },*/
  },

  watch:{
    barnumIsShow(){
      this.updateOption();
    }
  },

  mounted(){
      this.initChart();
  },
  methods:{
    insert_flg(str, flg, sn) {
      let newstr = '';
      for (let i = 0; i < str.length; i += sn) {
        const tmp = str.substring(i, i + sn);
        newstr += tmp + flg;
      }
      return newstr;
    },
    initChart(){
        this.chart = echarts.init(this.$refs.chart, null, {
            render: "svg"
        });

        this.updateOption();

        window.addEventListener("resize", ()=>{
            this.chart.resize();
        });
    },

    updateOption(){
      let _this = this;
      const legend = [];
      if (this.lastData) {
        legend.push(this.lastData.name);
      }
      if (this.currData) {
        legend.push(this.currData.name);
      }
      if (this.targetData) {
        legend.push(this.targetData.name);
      }
      if (this.scoreList) {
        legend.push(this.scoreList.name);
      }
      let option =  {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#E0E0E0',
            },
          },
        },
        legend: {
          top: 5,
          right: 10,
          itemWidth: 25,
          itemHeight: 10,
          itemGap: 15,
          data: legend,
          fontSize: 28,
          textStyle: {
            fontSize: 12,
            color:'#8c8c8c'
          },
        },
        grid: {
          top: 50,
          left: 10,
          right: 10,
          bottom: 0,
          containLabel: true,
        },
        // dataZoom: [
        //   {
        //     type: 'inside',
        //     start: 10,
        //     end: 90,
        //   },
        // ],
        xAxis: {
          axisLine: {
            lineStyle: {
              color: '#E0E0E0',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            color: '#8c8c8c',
            formatter: value => _this.insert_flg(value, '\n', 3),
          },
          data: this.xAxis,
        },
        yAxis: [
          {
            scale: true,
            // name: '单位：万',
            nameTextStyle: {
              fontSize: 13,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: '#ccc',
                type: 'dashed',
              },
            },
          },
          {
            scale: true,
            nameTextStyle: {
              fontSize: 13,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: '#ccc',
                type: 'dashed',
              },
            },
          },
        ],
        series: [
          {
            name: this.lastData.name,
            type: 'bar',
            barWidth: '20%',
            data: this.lastData.data,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#5FAEFF',
                  },
                  {
                    offset: 1,
                    color: '#0682FF',
                  },
                ]),

                label: {
                  show: this.barnumIsShow,
                  position: 'top',
                  formatter(p) {
                    return p.value;
                  },
                },

              },
            },
          },
          {
            name: this.currData.name,
            type: 'bar',
            barWidth: '20%',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#FFC266',
                  },
                  {
                    offset: 1,
                    color: '#FF9900',
                  },

                ]),

                label: {
                  show: this.barnumIsShow,
                  position: 'top',
                  formatter(p) {
                    return p.value;
                  },
                },
              },
            },
            data: this.currData.data,
          },
          {
            name: this.scoreList.name,
            yAxisIndex: 1,
            type: 'line',
            // step: 'middle',
            data: this.scoreList.data,
            itemStyle: {
              color: '#3398FF',
            },
            // lineStyle: {
            //   normal: {
            //     shadowColor: 'rgba(0,0,0,0.2)',
            //     // shadowBlur: 10,
            //     shadowOffsetY: 10,
            //     width: 2.5,
            //   },
            // },
            symbolSize: 0,
          },
          {
            name: this.targetData.name,
            type: 'line',
            step: 'middle',
            data: this.targetData.data,
            itemStyle: {
              color: '#F5B23F',
            },
            lineStyle: {
              normal: {
                shadowColor: 'rgba(0,0,0,0.2)',
                shadowBlur: 10,
                shadowOffsetY: 10,
                type: 'dashed',
              },
            },
          },
        ],
      };
      this.chart&&this.chart.setOption(option);
    }
  }
};
</script>


<style lang='less'>
.CompanyBar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  .btn-area {
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    z-index: 100;
  }
  .echarts {
    width: 100%;
    height: 100%;
  }
}
</style>
