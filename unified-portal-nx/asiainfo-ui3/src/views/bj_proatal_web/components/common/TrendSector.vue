<template>
  <div class="TrendSector">
    <!-- <v-chart class="SectorEcharts" :options="echartOptions" /> -->
    <div ref='chart' class="SectorEcharts" style="width:50%; height:100%;"></div>
    <div style="width:50%; height:50%; display:flex; flex-direction:column; justify-content:space-between;">
      <p v-for="(item,index) in legendData" style="margin:0px;" class="legend">
        <span style="width:100px;">
          <span class="rounder"  :style="{background:colors[index]}"></span>
          <span style="color:#262626;">{{item}}</span>
        </span>
        <span style="width:50px; color:#8c8c8c;">{{sectorData[index].value}}%</span>
      </p>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import * as echarts from "echarts";

export default {
  props: ['sectorData', 'legendData'],
  data() {
    return {
      chart: null,
      colors: ['#EE8383', '#FFC06F', '#329BF8', '#83C284'],
    };
  },
  computed: {
    echartOptions() {
      return {
        title: {
          show: false,
          text: '某站点用户访问来源',
          subtext: '纯属虚构',
          left: 'center',
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}%',
          // formatter: '{a} <br/>{b} : {c} ({d}%)',
        },
        legend: {
          show:false,
          position:[0,0],
          orient:'vertical',
          top:'26%',
          right:'10%',
          width:'200',
          itemWidth:10,
          itemHeight:10,
          formatter(name){
            return name
          },
          // itemWidth: 20,
          // type: 'scroll',
          // itemHeight: 12,
          data: this.legendData,
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: ['45%','60%'],
            color: this.colors,
            center: ['50%', '50%'],
            itemStyle: { // 图形样式
              normal: {
                borderColor: '#ffffff',
                borderWidth: 1,
              },
            },
            label: {
              normal: {
                show: false,
                position: 'inside',
                formatter: params => `${params.value} %`,
              },
              emphasis: {
                show: false,
              },
            },
            data: this.sectorData,
          },
        ],
      };
    },
  },
  mounted(){
      this.initChart();

  },
  methods:{
    initChart(){
        this.chart = echarts.init(this.$refs.chart, null, {
            render: "svg"
        });

        this.updateOption();

        window.addEventListener("resize", ()=>{
            this.chart.resize();
        });
    },

    updateOption(){
      let _this = this;
      console.error(this.echartOptions)
      this.chart.setOption(this.echartOptions);
    }
  }
};
</script>


<style lang='less'>
.TrendSector {
  position: relative;
  width: 100%;
  height:100%;
  display: flex;
  align-items: center;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  .btn-area {
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    z-index: 100;
  }
  .echarts {
    width: 100%;
    height: 100%;
  }

  .legend {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;

    .rounder {
      display: inline-block;
      width: 12px;
      height:12px;
      border-radius: 10px;
      margin-right:10px;
    };
  }
}
</style>
