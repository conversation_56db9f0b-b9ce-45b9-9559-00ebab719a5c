<template>
  <div class="dateChoose">
    <div class="radioArea" v-if="defaultDateTypeList.length">
      <label>选择时间:</label>
      <!-- <el-radio-group v-model="dateType" size="small" @change="dateTypeChange">
        <el-radio v-for="item in defaultDateTypeList" :key="item" :label="item"/>
      </el-radio-group>-->
      <el-select
        class="partment-select dateSelect"
        v-model="dateType"
        size="small"
        ref="refHandle01"
        @focus="inputFocus"
        popper-class="monthPopper"
        @change="dateTypeChange"
      >
        <el-option v-for="item in defaultDateTypeList" :key="item" :label="item" :value="item"></el-option>
      </el-select>
    </div>
    <!-- 时间日期选择 -->
    <el-date-picker
      :class="dateType=='日'&& 'showit' "
      v-model="day_choosed"
      class="controlDom"
      type="date"
      size="small"
      popper-class="pickerPopper"
      :clearable="false"
      placeholder="选择日期"
      @change="dayChange"
      :picker-options="pickOptions"
    ></el-date-picker>
    <el-date-picker
      :class="dateType=='日累计' && 'daterange'"
      v-if="dateType=='日累计'"
      v-model="day_daterange"
      class="controlDom"
      type="daterange"
      popper-class="pickerPopper"
      range-separator="-"
      :clearable="false"
        format="yyyy-MM-dd"
      @change="daterangeChange"
      :picker-options="pickOptions"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
    ></el-date-picker>
    <el-date-picker
      :class="dateType=='月' && 'showit'"
      v-model="month_choosed"
      class="controlDom"
      type="month"
      size="small"
      format="yyyy年MM月"
      :clearable="false"
      placeholder="选择月份"
      :picker-options="pickOptions"
      @change="monthChange"
    ></el-date-picker>
    <WeekInput
      :class="dateType=='周' && 'showit'"
      class="controlDom"
      placeholder="选择周"
      @change="weekChange"
      :picker-options="pickOptions"
      :defaultWeek="week_choosed_text"
    />
    <div class="quarterChooseArea">
      <el-date-picker
        :class="dateType=='季' && 'showit'"
        v-model="quarter_choosed"
        class="controlDom quarterChoose"
        popper-class="quarterPoper"
        type="month"
        :clearable="false"
        format="yyyy年M季度"
        placeholder="选择季度"
        size="small"
        @focus="handleQuarterFocus"
        @change="quarterChange"
        :picker-options="pickOptions"
      ></el-date-picker>
    </div>
    <el-date-picker
      :class="dateType=='年' && 'showit'"
      v-model="year_choosed"
      type="year"
      :clearable="false"
      class="controlDom"
      size="small"
      placeholder="选择年"
      :picker-options="pickOptions"
      @change="yearChange"
    ></el-date-picker>
    <slot name="default"></slot>
  </div>
</template>
<script>
import Vue from "vue";
import { RadioGroup, Radio, DatePicker } from "element-ui";
import Common from "../../lib/date";
import WeekInput from "./WeekInput.vue";

Vue.use(RadioGroup);
Vue.use(Radio);
Vue.use(DatePicker);
export default {
  name: "dateChoose",
  props: {
    // 日期类型选择数组
    defaultDateTypeList: {
      type: Array,
      default: () => ["年", "季", "月", "周", "日","日累计"]
    },
    // 默认日期
    defaultDate: {
      type: String,
      default: Common.formatDate(new Date(), "yyyyMM第w周")
    }
  },
  data() {
    const dateString = Common.dateTranslate(this.defaultDate).date;
    const dateTypeName = Common.dateTranslate(this.defaultDate).dateName;
    let day = null;
    let month = null;
    let week = null;
    let quarter = null;
    let year = null;

    day = new Date(dateString);
    week = new Date(dateString);
    month = new Date(dateString);
    quarter = new Date(dateString);
    year = new Date(dateString);
    // 设置上周/上月/上季
    if (dateTypeName === "日") {
      week.setDate(week.getDate() - 7);
      month.setMonth(month.getMonth() - 1);
      quarter.setMonth(quarter.getMonth() - 3);
    } else if (dateTypeName === "周") {
      month.setMonth(month.getMonth() - 1);
      quarter.setMonth(quarter.getMonth() - 3);
    } else if (dateTypeName === "月") {
      quarter.setMonth(quarter.getMonth() - 3);
    }
    return {
      value: "",
      day_daterange:'',
      day_choosed: day, // 天Date
      month_choosed: month, // 月Date
      week_choosed_text: Common.formatDate(week, "yyyyMM第w周"), // 周String
      quarter_choosed: new Date(quarter.setMonth(quarter.getMonth() / 3)), // 季度Date
      year_choosed: year, // 年Date
      dateType: dateTypeName,
      pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    };
  },
  components: { WeekInput },
  created() {},
  methods: {
    dropDownVisible() {
      const { classList } = document.querySelector(
        ".dateSelect .el-input--suffix"
      );
      if (classList.toString().indexOf("is-focus") !== -1) {
        document.querySelector(".dateSelect").click();
      }
    },
    inputFocus() {
      setTimeout(() => {
        const elMain = document.querySelector(".el-main");
        elMain.removeEventListener("scroll", this.dropDownVisible);
        elMain.addEventListener("scroll", this.dropDownVisible);
      }, 10);
    },
    handleQuarterFocus() {
      // 季度选择框focus触发
      // 替换月度选择为季度选择
      this.$nextTick(() => {
        console.log(
          "ddddd",
          document.querySelector(
            ".quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(1) a"
          )
        );
        document.querySelector(
          ".quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(1) a"
        ).innerText = "第一季度";
        document.querySelector(
          ".quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(2) a"
        ).innerText = "第二季度";
        document.querySelector(
          ".quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(3) a"
        ).innerText = "第三季度";
        document.querySelector(
          ".quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(4) a"
        ).innerText = "第四季度";
      });
    },
    dateTypeChange(val) {
      switch (val) {
        case "日":
          this.emitDateChange(
            Common.formatDate(this.day_choosed, "yyyy-MM-dd")
          );
          break;
           case "日累计":
          this.emitDateChange(
            Common.formatDate(this.day_daterange, "yyyy-MM-dd,yyyy-MM-dd")
          );
          break;
        case "周":
          this.emitDateChange(this.week_choosed_text);
          break;
        case "月":
          this.emitDateChange(Common.formatDate(this.month_choosed, "yyyyMM"));
          break;
        case "季":
          /* eslint-disable */
          const year = this.quarter_choosed.getFullYear();
          const month = this.quarter_choosed.getMonth() * 3 + 1;
          /* eslint-enable */
          this.emitDateChange(
            Common.formatDate(
              new Date(`${year}-${month < 10 ? "0" : ""}${month}-01`),
              "yyyy年q季度"
            )
          );
          break;
        case "年":
          this.emitDateChange(Common.formatDate(this.year_choosed, "yyyy"));
          break;
        default:
          break;
      }
    },
    daterangeChange(val){
      console.log(val,'picker-options');
        this.emitDateChange(Common.formatDate(val, ["yyyy-MM-dd","yyyy-MM-dd"]));
    },
    dayChange(val) {
      this.emitDateChange(Common.formatDate(val, "yyyy-MM-dd"));
    },
    weekChange(val) {
      this.week_choosed_text = val;
      this.emitDateChange(this.week_choosed_text);
    },
    monthChange(val) {
      this.emitDateChange(Common.formatDate(val, "yyyyMM"));
    },
    quarterChange(val) {
      // 月份==>季度月份
      const year = val.getFullYear();
      const month = val.getMonth() * 3 + 1;
      const transQuarter = new Date(
        `${year}-${month < 10 ? "0" : ""}${month}-01`
      );
      this.emitDateChange(Common.formatDate(transQuarter, "yyyy年q季度"));
    },
    yearChange(val) {
      this.emitDateChange(Common.formatDate(val, "yyyy"));
    },
    showWeekPicker() {
      // 触发周选择器
      this.$refs.weekChoosePicker.focus();
    },
    emitDateChange(val) {
      this.$emit("dateChange", val);
    }
  },
  watch: {
    defaultDateTypeList(val) {
      if (val.indexOf(this.dateType) === -1 && val[0]) {
        this.dateType = val[0].toString();
      }
    }
  }
};
</script>
<style lang="less" scope>
.dateChoose {
  display: inline-block;
  color: white;
  .radioArea {
    float: left;
    vertical-align: middle;
    line-height: 30px;
    .el-radio {
      margin-right: 15px;
    }
    .el-radio__label {
      padding-left: 5px;
      color: white;
    }
    & > label {
      margin-right: 10px;
      // font-size: 13px;
      font-weight: normal;
      font-family: SourceHanSansSC-Regular;
      font-size: 14px !important;
      color: #262626 !important;
      letter-spacing: -0.27px;
    }
  }
  .weekChooseArea {
    display: inline-block;
    position: relative;
    label {
      position: absolute;
      z-index: 2;
      pointer-events: none;
      font-size: 14px;
      line-height: 14px;
      color: rgb(96, 98, 102);
      top: 13px;
      left: 30px;
    }
  }
  .quarterChooseArea {
    display: inline-block;
  }
  .controlDom {
    width: 0px !important;
    margin-right: 0px !important;
    vertical-align: middle;
    // visibility: hidden;
    display: none;
    color: white;
    &.showit {
      width: 160px !important;
      margin-right: 10px !important;
      // visibility:inherit;
      display: inline-block;
    }
    &.showit.weekInput {
      width: 260px !important;
    }
    input {
      background-color: #ffffff;
      border-color: #e6e6e6 !important;
      // color: white;
      text-align: center;
      &::placeholder {
        font-family: SourceHanSansSC-Regular;
        font-size: 14px;
        color: #595959;
        letter-spacing: -0.27px;
      }
    }
    // .el-input__prefix {
    //   color: white;
    // }
  }
  .daterange {
      width: 300px !important;
      margin-right: 10px !important;
      // visibility:inherit;
      display: inline-block;
    }
}
</style>
<style lang="less">
.quarterPoper {
  .el-picker-panel__content .el-month-table tbody tr {
    display: none;
  }
  .el-picker-panel__content .el-month-table tbody tr:first-child {
    display: table-row;
  }
}
.dateSelect {
  input {
    width: 100px;
  }
}
</style>
