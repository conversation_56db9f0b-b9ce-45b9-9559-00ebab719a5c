<template>
  <div v-loading="submitLoading" class="customerAddForm">
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="140px" class="demo-ruleForm">
      <el-form-item label="客群名称:" prop="customerName">
        <el-input v-model="ruleForm.customerName" style="width:80%" size="mini" />
      </el-form-item>
      <el-form-item label="区域:" prop="gridId">
        <el-cascader
          v-model="ruleForm.gridId"
          style="width:80%"
          :options="optionsCascader"
          size="mini"
          clearable
          :props="{
            checkStrictly:true
          }"
          @change="handleChangeCascader"
        />
      </el-form-item>
      <el-form-item label="满意度类型:" prop="satisfiedType">
        <el-select v-model="ruleForm.satisfiedType" style="width:80%" placeholder="请选择" size="small">
          <el-option label="满意用户" value="满意" />
          <el-option label="中立用户" value="中立" />
          <el-option label="不满客户" value="不满意" />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="指标名称:" prop="csmcustomerlableSeleted">
        <el-cascader
          v-model="ruleForm.csmcustomerlableSeleted"
          style="width:80%"
          :options="treeData"
          :props="cascaderProps"
          popper-class="treeLabelDataCascader"
          :show-all-levels="false"
          clearable
        />
      </el-form-item> -->

      <el-divider />
      <div class="subt">客户属性</div>
      <!-- 短信群发(电渠) 在线ivr外呼 在线人工 网格通 装维app -->

      <el-form-item label="属性摸排方式:" prop="attributeStyle">
        <el-radio-group v-model="ruleForm.attributeStyle" size="mini">
          <!-- <el-radio border label="-1">不限</el-radio> -->
          <!-- <el-radio border label="短信群发(电渠)">短信群发(电渠)</el-radio>
          <el-radio border label="在线ivr外呼">在线ivr外呼</el-radio>
          <el-radio border label="在线人工">在线人工</el-radio>
          <el-radio border label="网格通">网格通</el-radio>
          <el-radio border label="装维app">装维app</el-radio> -->
          <el-radio v-for="i in attribute_style" :key="i" border :label="i" />
        </el-radio-group>

      </el-form-item>

      <div v-if="cusAttributeVis" class="morebox">
        <el-form-item label="得分:" prop="cusAttribute">

          <!-- 自己选择 -->
          <el-select v-model="ruleForm.cusAttribute[0]" placeholder="请选择" size="small" @change="cusAttributeChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span style="padding:0 10px;">至</span>
          <el-select v-model="ruleForm.cusAttribute[1]" placeholder="请选择" size="small" @change="cusAttributeChange">
            <el-option
              v-for="item in optionsPoints"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

        </el-form-item>
        <!-- <div v-show="moretxt2vis" class="moretxt" @click="moretxt2status=!moretxt2status">{{ moretxt2status?'收起':'展开' }}>>></div> -->
      </div>
      <el-form-item v-if="customerButtonVis" label="客户按键:" prop="customerButton">
        <el-radio-group v-model="ruleForm.customerButton" size="mini">
          <el-radio border label="-1">不限</el-radio>
          <el-radio border label="1">1</el-radio>
          <el-radio border label="2">2</el-radio>
          <el-radio border label="3">3</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="是否是集团用户:" prop="ifGroup">
        <el-radio-group v-model="ruleForm.ifGroup" size="mini">
          <el-radio border label="-1">不限</el-radio>
          <el-radio border label="是">是</el-radio>
          <el-radio border label="否">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="修复数据来源渠道:" prop="dataSource">
        <el-radio-group v-model="ruleForm.dataSource" size="mini">
          <el-radio border label="-1">不限</el-radio>

          <el-radio v-for="i in data_source" :key="i" border :label="i" />

        </el-radio-group>
      </el-form-item>

      <el-divider />
      <div class="subt">客群属性</div>

      <el-form-item label="客群类别:" prop="customerCategory">
        <el-radio-group v-model="ruleForm.customerCategory" size="mini">
          <el-radio border label="1">关怀客群</el-radio>
          <el-radio border label="2">修复客群</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-divider />
      <el-form-item style="text-align:right">
        <el-button size="mini" type="primary" @click="submitForm('ruleForm')">生成</el-button>
        <el-button size="mini" @click="cancel">取消</el-button>
        <el-button size="mini" @click="resetForm('ruleForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { createProvinceCustomer, getFieldEnumImp } from '@/api/customer/index'

export default {
  props: {

    optionsCascader: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    },

    treeData: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    },
    attribute_style: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    },
    data_source: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    }

  },

  data() {
    return {

      optionsCascaderExpand: [],
      submitLoading: false,
      cascaderProps: {
        multiple: true,
        checkStrictly: true
      },
      conditionSize: 'small',

      ruleForm: {
        attributeStyle: '在线ivr外呼',
        cusAttribute: ['0', '10'],
        gridId: [],
        ifGroup: '-1',
        customerButton: '-1',
        customerName: '',
        satisfiedType: '',
        dataSource: '-1',
        customerCategory: '1'
      },
      rules: {
        customerName: [
          { required: true, message: '请输入客群名称', trigger: 'blur' }
        ],
        gridId: [
          { required: false, message: '请选择区域', trigger: 'change' }
        ],
        cusAttribute: [{ required: false, message: '请选择得分', trigger: 'change' }]
      },

      moretxt1DyHeight: 'unset',
      moretxt1status: 0, // 1 展开 0 隐藏 2不出现更多
      moretxt1vis: false,
      moretxt1Arr: [],

      // 得分
      optionsPoints: [
        { label: '0分', value: '0' },
        { label: '1分', value: '1' },
        { label: '2分', value: '2' },
        { label: '3分', value: '3' },
        { label: '4分', value: '4' },
        { label: '5分', value: '5' },
        { label: '6分', value: '6' },
        { label: '7分', value: '7' },
        { label: '8分', value: '8' },
        { label: '9分', value: '9' },
        { label: '10分', value: '10' }
      ]

    }
  },
  computed: {
    // 1属性摸排方式如果是在线ivr外呼 那么得分隐藏   2如果属性摸排方式不是在线ivr外呼，那么客户按键就隐藏  3 如果摸排选择了 不限 得分和客户按键都隐藏起来
    // 2 区域这个字段 如果选择地市就传地市里面 区县就传区县 网格就传网格
    cusAttributeVis() {
      if (this.ruleForm.attributeStyle == '-1' || this.ruleForm.attributeStyle == '在线ivr外呼') {
        return false
      }
      return true
    },
    customerButtonVis() {
      if (this.ruleForm.attributeStyle == '在线ivr外呼') {
        return true
      }
      return false
    }
  },

  watch: {
    'ruleForm.csmcustomerlableSeleted': {
      deep: true,
      handler(v, oldv) {

      }
    }

  },

  mounted() {
    console.log('this.optionsCascader:', this.optionsCascader)
    this.handlerCascaderDataExpand(this.optionsCascader)
    console.log('x:', this.optionsCascaderExpand)

    this.getClientHeight('moretxt2')
  },

  methods: {

    repairStatusChange(v) {
      if (v[v.length - 1] != '-1') {
        this.ruleForm.repairStatus = v.filter(i => i != '-1')
      } else {
        this.ruleForm.repairStatus = ['-1']
      }
    },
    customerButtonChange(v) {
      if (v[v.length - 1] != '-1') {
        this.ruleForm.customerButton = v.filter(i => i != '-1')
      } else {
        this.ruleForm.customerButton = ['-1']
      }
    },
    attributeStyleChange(v) {
      if (v[v.length - 1] != '-1') {
        this.ruleForm.attributeStyle = v.filter(i => i != '-1')
      } else {
        this.ruleForm.attributeStyle = ['-1']
      }
    },
    cancel() {
      this.$emit('closedrawer')
    },

    // 计算高度元素高度
    getClientHeight(reflabel) {
      const self = this
      this.$nextTick(() => {
        const elHeight = self?.$refs[reflabel]?.$el?.offsetHeight

        if (elHeight > 42) {
          this[`${reflabel}vis`] = true
          this[`${reflabel}DyHeight`] = '42px'
        }
      })
    },
    // 处理级联数据 展开平铺
    handlerCascaderDataExpand(arr) {
      if (Array.isArray(arr) && arr.length) {
        arr.forEach(i => {
          const x = {
            label: i.label,
            value: i.value,
            level: i.level
          }
          this.optionsCascaderExpand.push(x)
          if (i.children && i.children.length) {
            this.handlerCascaderDataExpand(i.children)
          } else {
            delete i.children
          }
        })
      }
    },
    cusAttributeChange() {
      const { cusAttribute } = this.ruleForm
      if (Array.isArray(cusAttribute) && cusAttribute.length == 2) {
        if (Number(cusAttribute[0]) >= Number(cusAttribute[1])) {
          this.$message.error('最低得分必须小于最高得分')
        }
      }
    },

    handleChangeCascader() {

    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {}
          //    attributeStyle: '-1',
          // cusAttribute: ['0', '10'],
          // gridId: [],
          // ifGroup: '-1',
          // customerButton: ['-1'],
          // customerName: '',
          // satisfiedType: '',
          // dataSource: '-1',
          // customerCategory: '1'
          const { customerCategory, dataSource, cusAttribute, attributeStyle, customerName, ifGroup, customerButton, satisfiedType } = this.ruleForm

          // 处理网格
          const gridObj = this.getAllGridPathObj(this.ruleForm.gridId)// 网格处理好了
          console.log('gridObj:', gridObj)
          // 处理得分
          if (Array.isArray(cusAttribute) && cusAttribute.length == 2) {
            if (Number(cusAttribute[0]) >= Number(cusAttribute[1])) {
              this.$message.error('最小得分必须小于最大得分')
              return
            }
            params.cusAttributeStart = cusAttribute[0]
            params.cusAttributeEnd = cusAttribute[1]
          } else {
            this.$message.error('请选择得分')
            return
          }
          const userInfo = JSON.parse(localStorage.getItem('userInfo')) || {}
          params = Object.assign(
            {},
            params,
            {
              customerName,
              attributeStyle,
              ifGroup,
              customerButton,
              customerCategory,
              dataSource,
              satisfiedType
            },
            gridObj,
            {
              createUserName: userInfo.name,
              createUserId: userInfo.loginName
            }
          )

          // cusAttributeVis customerButtonVis
          // 1属性摸排方式如果是在线ivr外呼 那么得分隐藏   2如果属性摸排方式不是在线ivr外呼，那么客户按键就隐藏  3 如果摸排选择了 不限 得分和客户按键都隐藏起来
          if (!this.cusAttributeVis) {
            params.cusAttributeStart = ''
            params.cusAttributeEnd = ''
          }
          if (!this.customerButtonVis) {
            params.customerButton = ''
          }

          Object.keys(params).forEach(key => {
            if (params[key] == '-1' && key != 'customerName') {
              params[key] = ''
            }
          })

          console.log('params===:', params)
          this.submitLoading = true
          createProvinceCustomer(params).then(res => {
            if (res.code == 200) {
              this.$message.success('创建客群成功')
              this.$emit('closedrawer')
            }
          }).finally(() => {
            this.submitLoading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 处理网格
    getAllGridPathObj(keyarr) {
      const { optionsCascaderExpand } = this
      const len = keyarr.length
      const obj = {
        city: null,
        cityName: null,
        district: null,
        districtName: null,
        grid: null,
        gridName: null
      }
      let tar = null
      if (len) {
        const lastkey = keyarr[len - 1]

        tar = optionsCascaderExpand.filter((i) => {
          if (i.value == lastkey) {
            return true
          }
          return false
        })

        tar = tar[0]
        const level = tar.level
        // 后端反馈只能传中文
        if (level == 1) {
          obj.city = tar.label
          obj.cityName = tar.value
        } else if (level == 2) {
          obj.district = tar.label
          obj.districtName = tar.value
        } else if (level == 3) {
          obj.grid = tar.label
          obj.gridName = tar.value
        }
      }

      return obj
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
<style lang="scss" scoped>
.customerAddForm{
    padding:0 20px;
}
.el-cascader {
        /deep/.el-icon-arrow-down:before {
          content: "\E6E1";

        }
       /deep/.el-icon-arrow-down{
         transform: rotate(180deg);
       }
       /deep/.is-reverse.el-icon-arrow-down{
         transform: rotate(0deg);
       }
       /deep/.el-input .el-input__inner{
           line-height: 32px;
           height: 32px;
       }

  }

  /deep/.el-checkbox.is-bordered.is-checked{
    background:#FF9900;
    border-color: #FF9900;

  }
  /deep/.el-checkbox.is-bordered.el-checkbox--mini .el-checkbox__label{

font-size: 14px;

  }

  /deep/.el-checkbox.is-bordered.el-checkbox--mini{
   margin-right: 0px;
    padding: 3px 8px;
    margin-top: 6px;
    height: 24px;
    line-height: 18px;
  }

  /deep/.el-checkbox__input.is-checked + .el-checkbox__label{
    color:#262626;

  }
  /deep/.el-checkbox__inner{display:none}
  /deep/.el-checkbox__label{
    padding:0;
    font-size: 14px;

font-weight: 400;

color: #595959;

letter-spacing: -0.27px;
  }

  /deep/.el-form-item__label{

    font-size: 14px;
    font-weight: 400;

    text-align: right;
    color: #262626;

    letter-spacing: -0.27px;
  }
/deep/.el-form-item.el-form-item--medium{
  margin-bottom: 22px;
}

/deep/.el-radio--mini.is-bordered{
      margin-right: 0px;
    padding: 3px 8px;
    margin-top: 6px;
    height: 24px;
    line-height: 18px;
    margin-bottom: 10px;

  }
  /deep/.el-radio--mini.is-bordered .el-radio__label{
    font-size:14px;
    color:#262626;
    font-weight: 400;
    padding-left: 0;
    text-align: center;

  }
  /deep/.el-radio__input{display:none}
  /deep/.el-radio.is-bordered.is-checked{
     background:#FF9900;
    border-color: #FF9900;
  }

  .subt{
      margin-bottom: 15px;
      font-size: 14px;
      font-weight: 500;
      text-align: left;
      color: #262626;
      line-height: 21px;
      letter-spacing: -0.27px;
  }
  .morebox{
      position: relative;
      padding-right:50px;
      margin-bottom: 22px;

  }
  .moretxt{
      position: absolute;
      right:0;
      top:0;
      color:#595959;
      font-size: 12px;
      line-height: 42px;
      &:hover{
          color:#FF9900;
          cursor:pointer;
      }
  }

</style>
