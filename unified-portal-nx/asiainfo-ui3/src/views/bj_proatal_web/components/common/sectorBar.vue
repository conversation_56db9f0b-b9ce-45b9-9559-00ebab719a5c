<template>
  <div class="TrendBar">
    <div ref="chart" class="echarts" ></div>
  </div>
</template>

<script>
import Vue from 'vue';
import * as echarts from "echarts";

export default {
  props: ['barData'],
  computed: {
    echartOptions() {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#E0E0E0',
            },
          },
          formatter: params => `${params[0].name}: ${params[0].value}%`,
        },
        grid: {
          top: 40,
          left: 10,
          right: 10,
          bottom: 0,
          containLabel: true,
        },
        xAxis: {
          axisLine: {
            lineStyle: {
              color: '#E0E0E0',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#333',
            interval: 0,
          },
          data: this.barData.xAxis,
        },
        yAxis: {
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#ccc',
              type: 'dashed',
            },
          },
          scale: true,
        },
        series: [
          {
            type: 'bar',
            barWidth: '20%',
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(71, 184, 255, 1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(97, 135, 255, 1)',
                  },
                ]),
                label: {
                  show: this.barnumIsShow,
                  position: 'top',
                  formatter(p) {
                    return p.value;
                  },
                },
              },
            },
            data: this.barData.data,
          },
        ],
      };
    },
  },
  mounted(){
      this.initChart();
  },
  methods:{
    initChart(){
        this.chart = echarts.init(this.$refs.chart, null, {
            render: "svg"
        });

        this.updateOption();

        window.addEventListener("resize", ()=>{
            this.chart.resize();
        });
    },

    updateOption(){
      let _this = this;
      this.chart.setOption(this.echartOptions);
    }
  }
};
</script>
<style lang='less'>
.TrendBar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  .btn-area {
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    z-index: 100;
  }
  .echarts {
    width: 100%;
    height: 100%;
  }
}
</style>
