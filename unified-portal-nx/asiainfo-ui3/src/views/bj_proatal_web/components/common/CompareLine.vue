<template>
  <div class="CompareLine">
    <el-radio-group v-model="compareName" class="btn-area" size="mini">
      <el-radio-button key="领先" label="领先" />
      <template v-for="(it, index) in data">
        <el-radio-button v-if="index !== targetDataIndex" :key="it.name" :label="it.name" />
      </template>
    </el-radio-group>
    <div ref="chart" class="echarts" />
  </div>
</template>

<script>
import Vue from 'vue'
import * as echarts from 'echarts'
import { RadioGroup, RadioButton } from 'element-ui'

import utils from '../../utils/utils'
import upicon from '../../assets/img/up.png'
import downicon from '../../assets/img/down.png'

Vue.use(RadioGroup).use(RadioButton)
export default {
  name: 'CompareLine',
  props: {
    data: {
      type: Array,
      default: () => [
        {
          name: '联通',
          data: [100, 50, 80]
        },
        {
          name: '电信',
          data: [70, 90, 120]
        },
        {
          name: '移动',
          data: [80, 70, 100]
        }
      ]
    },
    leadList: {
      type: Array,
      default: () => [
        {
          value: '22900',
          ifRedWord: 1,
          ifGrenUp: 1,
          ifRedDown: 0,
          ifyellowExclamation: 0,
          challengeValue: null,
          basicValue: null,
          targetValue: null,
          assessmentValue: null
        },
        {
          value: '12631',
          ifRedWord: 1,
          ifGrenUp: 0,
          ifRedDown: 0,
          ifyellowExclamation: 0,
          challengeValue: null,
          basicValue: null,
          targetValue: null,
          assessmentValue: null
        },
        {
          value: '20526',
          ifRedWord: 1,
          ifGrenUp: 0,
          ifRedDown: 0,
          ifyellowExclamation: 0,
          challengeValue: null,
          basicValue: null,
          targetValue: null,
          assessmentValue: null
        }
      ]
    },
    notifyList: {
      type: Array,
      default: () => [
        '连续2个周期未达目标值<br/>连续7个周期超过目标值<br/>',
        '连续2个周期未达目标值<br/>连续7个周期超过目标值<br/>',
        '连续2个周期未达目标值<br/>连续7个周期超过目标值<br/>'
      ]
    },
    targetDataIndex: {
      type: Number,
      default: 2
    },
    xAxis: {
      type: Array,
      default: () => ['3月完成值', '2月完成值', '1月完成值']
    },
    barnumIsShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return { compareName: '联通' || this.data[0].name }
  },
  computed: {
    echartOptions() {
      const kdata = []
      const {
        data, xAxis, targetDataIndex, compareName, notifyList, leadList
      } = this
      const series = [
        // 基准折线图
        {
          type: 'line',
          symbolSize: 10,
          data: data[targetDataIndex].data,
          name: data[targetDataIndex].name,
          z: 3,
          tooltip: {
            show: compareName === '领先'
          },
          itemStyle: {
            borderWidth: 2
            /* shadowColor: 'rgba(0,0,0,0.2)',
            shadowOffsetX: 5,
            shadowOffsetY: 5,
            shadowBlur: 10,*/
          },
          lineStyle: {
            width: 3
            // shadowColor: 'rgba(0,0,0,0.2)',
          }
        }
      ]
      if (compareName === '领先') {
        this.data.forEach((item, index) => {
          if (index !== targetDataIndex) {
            series.push({
              type: 'line',
              symbolSize: 10,
              data: item.data,
              name: item.name,
              z: 3,
              itemStyle: {
                borderWidth: 2
                /* shadowColor: 'rgba(0,0,0,0.2)',
                shadowOffsetX: 5,
                shadowOffsetY: 5,
                shadowBlur: 10,*/
              },
              lineStyle: {
                width: 3
                // shadowColor: 'rgba(0,0,0,0.2)',
              }
            })
          }
        })
        // k线数据
        this.leadList.forEach((it, index) => {
          const max = Math.max(it.value, data[targetDataIndex].data[index])
          const min = Math.min(it.value, data[targetDataIndex].data[index])
          const leadValue = max - min
          kdata.push([leadValue, max, leadValue, max])
        })
      }
      data.forEach((item) => {
        if (item.name === compareName) {
          item.data.forEach((it, index) => {
            const max = Math.max(it, data[targetDataIndex].data[index])
            const min = Math.min(it, data[targetDataIndex].data[index])
            kdata.push([it, data[targetDataIndex].data[index], min, max])
          })
          // 对比折线图
          series.push({
            type: 'line',
            symbolSize: 10,
            data: item.data,
            name: item.name,
            z: 3,
            tooltip: {
              show: false
            },
            itemStyle: {
              borderWidth: 2
              /* shadowColor: 'rgba(0,0,0,0.2)',
              shadowOffsetX: 5,
              shadowOffsetY: 5,
              shadowBlur: 10,*/
            },
            lineStyle: {
              width: 3
              // shadowColor: 'rgba(0,0,0,0.2)',
            }
          })
        }
      })
      const itemStyle = {
        borderWidth: 1,
        borderColor: 'rgba(133,194,133,0.8)',
        opacity: 0.9,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(133,194,133,0.65)' // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(133,194,133,0.65)' // 100% 处的颜色
            }
          ]
        },
        color0: '#F76F40'
      }
      // K线配置
      series.push({
        type: 'k',
        barWidth: 12,
        itemStyle,
        data: kdata,
        tooltip: {
          show: true
        },
        emphasis: {
          itemStyle: {
            borderColor: 'rgba(75,190,255,0.5)',
            borderColor0: 'rgba(255, 0, 0, 0.4)'
          }
        }
      })
      const itemStyleShow = {
        normal: {
          label: {
            show: this.barnumIsShow,
            position: 'top',
            formatter(p) {
              return p.value
            }
          }
        }
      }
      series[0].itemStyle = itemStyleShow
      const options = {
        color: ['#F5B23F', '#3398FF', '#61E394'],
        legend: {
          left: 10,
          top: 0,
          textStyle: {
            fontSize: 13
          }
        },
        grid: {
          top: 40,
          left: 0,
          right: 10,
          bottom: 0,
          containLabel: true
        },
        xAxis: {
          data: xAxis,
          axisLine: {
            lineStyle: {
              color: '#E0E0E0'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#8c8c8c'
          }
        },
        yAxis: {
          scale: true,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#E0E0E0'
            }
          }
        },
        tooltip: {
          trigger: 'axis',
          confine: true,
          formatter(params) {
            if (compareName !== '领先') {
              const val = params[0].value[2] - params[0].value[1]
              const val1 = Math.abs(val)
              return val > 0 ? `领先值${val1.toFixed2(2)}` : `落后值${val1.toFixed2(2)}`
            }
            let textString = `${params[0].name}<br/>`
            textString += `${params[0].marker}${params[0].seriesName} ${params[0].value}<br/>`
            textString += `${params[1].marker}${params[1].seriesName} ${params[1].value}<br/>`
            if (params.length > 3) {
              textString += `${params[2].marker}${params[2].seriesName} ${params[2].value}<br/>`
              // const val = params[3].value[2] - params[3].value[1];
              textString += `${params[3].marker}`
              const kobj = leadList[params[3].dataIndex]
              const val = kobj.value
              const val1 = Math.abs(val)
              // console.log(kobj);
              if (kobj.ifRedWord === 1) {
                textString += `<span style="color: red">${val > 0 ? `领先 ${val1}` : `领先 -${val1}`}</span>`
              } else {
                textString += `${val > 0 ? `领先 ${val1}` : `领先 -${val1}`}`
              }
              if (kobj.ifGrenUp === 1) {
                textString += `<img style="width:13px;height:13px;margin-top:-3px;vertical-align: middle" src=${upicon} />`
              }
              if (kobj.ifRedDown === 1) {
                textString += `<img style="width:13px;height:13px;margin-top:-3px;vertical-align: middle" src=${downicon} />`
              }
            } else {
              textString += `${params[2].marker}`
              const kobj = leadList[params[2].dataIndex]
              const val = kobj.value
              const val1 = Math.abs(val)
              console.log(kobj)
              if (kobj.ifRedWord === 1) {
                textString += `<span style="color: red">${val > 0 ? `领先 ${val1}` : `领先 -${val1}`}</span>`
              } else {
                textString += `${val > 0 ? `领先 ${val1}` : `领先 -${val1}`}`
              }
              if (kobj.ifGrenUp === 1) {
                textString += `<img style="width:13px;height:13px;margin-top:-3px;vertical-align: middle" src=${upicon} />`
              }
              if (kobj.ifRedDown === 1) {
                textString += `<img style="width:13px;height:13px;margin-top:-3px;vertical-align: middle" src=${downicon} />`
              }
              console.log(params)
            }
            textString += `<br/>${notifyList[params[0].dataIndex]}`
            return textString
          }
        },
        series
      }
      if (utils.IEVersion() < 0) {
        options.series.forEach((it) => {
          if (it.lineStyle) {
            Object.assign(it.lineStyle, {
              /* shadowOffsetX: 5,
              shadowOffsetY: 5,
              shadowBlur: 5,*/
            })
          }
        })
      }
      return options
    }
  },
  watch: {
    data() {
      this.setOptions()
    },

    barnumIsShow() {
      this.updateOption()
    },

    compareName() {
      setTimeout(() => {
        this.updateOption()
      }, 500)
    }
  },
  mounted() {},

  mounted() {
    this.initChart()
  },
  methods: {

  },
  methods: {
    setOptions() {
      this.echartOptions.series[0].data = this.data.map((d, index) => ({
        value: d.count,
        name: `${d.name}`,
        itemStyle: {
          normal: {
            color: this.pieOptions.color[index]
          }
        }
      }))
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chart, null, {
        render: 'svg'
      })

      this.updateOption()

      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    },

    updateOption() {
      const _this = this

      this.chart.setOption(this.echartOptions)
    }
  }
}
</script>

<style lang="less" scoped>
.CompareLine {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  .btn-area {
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    z-index: 100;
  }
  .echarts {
    width: 100%;
    height: 100%;
  }
}
</style>
