<template>
  <div class="Radar">
    <!-- <v-chart ref="RadarCharts" class="echarts" id="RadarCharts" :options="pieOptions" autoresize /> -->
    <div ref="RadarCharts" class="echarts" />
    <div class="tooltip" />
  </div>
</template>

<script>
import Vue from 'vue'
import * as echarts from 'echarts'

import upicon from '../../assets/img/up.png'
import downicon from '../../assets/img/down.png'
import wrongicon from '../../assets/img/wrong.png'
import arrowright from '../../assets/img/arrowright.png'
import redLegend from '../../assets/img/red.png'

export default {
  props: {
    data: {
      type: Array,
      default: () => [
        {
          name: '3月目标值',
          data: [
            {
              value: 95,
              data: 1204,
              name: '党群工作办公室0'
            },
            {
              value: 90,
              data: 1204,
              name: '党群工作办公室1'
            },
            {
              value: 85,
              data: 1204,
              name: '党群工作办公室2'
            },
            {
              value: 63,
              data: 1204,
              name: '党群工作办公室3'
            }
          ]
        },
        {
          name: '3月完成值',
          data: [
            {
              value: 48,
              data: 1204,
              name: '党群工作办公室4'
            },
            {
              value: '35',
              data: 1204,
              name: '党群工作办公室5'
            },
            {
              value: 32,
              data: 1204,
              name: '党群工作办公室6'
            },
            {
              value: 26,
              data: 1204,
              name: '党群工作办公室7'
            }
          ]
        }
      ]
    },
    radartitle: {
      type: String,
      default: () => '雷达图标题'
    }
  },
  data() {
    return {
      chart: null,
      pieOptions: null
    }
  },
  computed: {
  },

  watch: {
    data: {
      handler(val) {
        this.generateOptions()
      },
      deep: true
    }
  },

  mounted() {
    this.initChart()
  },
  methods: {

    generateOptions() {
      // eslint-disable-next-line radix
      // const myColor = `#${parseInt(Math.random() * 10)}3D7FF`;
      const that = this
      const legendlist = []
      const indicator = []
      this.data.forEach((it) => {
        // console.log(this.data);
        it.value = []
        // console.warn(it.name)
        legendlist.push(it.name)
        it.data.forEach((i) => {
          // console.log(i.value);
          if (i.value === null) {
            it.value.push(0)
          } else {
            it.value.push(parseFloat(i.value))
          }
        })
      })
      this.data[0].data.forEach((it) => {
        console.warn(it.name)
        indicator.push({
          name: it.name,
          data: it,
          max: parseInt(it.maxdata, 10),
          min: it.mindata !== null ? parseInt(it.mindata, 10) : 0 // 最大最小值
        })
      })

      // 处理鼠标滑过显示完成值的tooltip:  用多个series描绘,单独定义tooltip  2020-4-20
      const buildSeries = function(data) {
        const helper = data.map((item, index) => {
          const arr = new Array(data.length)
          arr.splice(index, 1, item)
          console.log(arr)
          return arr
        })
        return [data, ...helper].map((item, index) => {
          let a = ''; let symbol = 'none'
          if (index > 0 && data[index - 1] > 0) {
            a = indicator[index - 1].name + '（完成值）：' + data[index - 1]
            symbol = 'circle'
          }
          return {
            name: '完成值',
            type: 'radar',
            data: [item],
            symbol: 'none',
            symbolSize: 10,
            areaStyle: {
              normal: {
                opacity: 0.6
              }
            },
            label: {
              normal: {
                show: false,
                formatter: params => `{subtitle| ${params.value}}`,
                rich: {
                  subtitle: {
                    // color: '#83D7FF',
                    position: 'relative',
                    showMaxLabel: null,
                    fontSize: 16,
                    fontWeight: 500,
                    textShadowColor: 'rgba(0,0,0,0.2)',
                    textShadowOffsetX: 1,
                    textShadowOffsetY: 2
                  }
                }
              }
            },
            labelLine: { show: true },
            tooltip: {
              show: true,
              textStyle: {
                color: '#8c8c8c'
              },
              formatter: function() {
                return a
              }
            }
          }
        })
      }
      // 完成值series
      const series = buildSeries(this.data[0].value)
      // 指标阈值series
      series.push({
        name: '指标阈值',
        type: 'radar',
        data: [this.data[1].value],
        symbolSize: 0,
        areaStyle: {
          normal: {
            opacity: 0.4,
            color: '#FF9900'
          }
        },
        lineStyle: {
          normal: {
            color: '#FF9900',
            width: 1,
            type: 'dashed'
          }
        },
        label: {
          normal: {
            show: false,
            formatter: params => `{subtitle| ${params.value}}`,
            rich: {
              subtitle: {
                // color: '#83D7FF',
                position: 'relative',
                showMaxLabel: null,
                fontSize: 16,
                fontWeight: 500,
                textShadowColor: 'rgba(0,0,0,0.2)',
                textShadowOffsetX: 1,
                textShadowOffsetY: 2
              }
            }
          }
        },
        labelLine: { show: true },
        tooltip: {
          show: false
        }
      })

      this.pieOptions = {
        title: {
          // text: this.radartitle,
          left: 'center'
        },
        color: ['#339933', '#FF9900'],
        tooltip: {
          show: false
        },
        legend: [
          {
            data: ['完成值'],
            left: 0,
            icon: 'circle',
            selectedMode: false,
            textStyle: {
              color: '#262626'
            }
          },
          {
            data: ['指标阈值'],
            left: 90,
            icon: `circle`,
            itemWidth: 14,
            itemHeight: 14,
            selectedMode: false,
            textStyle: {
              color: '#262626'
            }
          }
        ],
        radar: {
          scale: true,
          center: ['50%', '50%'],
          radius: '66%',
          shape: 'circle',
          splitArea: {
            areaStyle: {
              color: ['transparent']
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              opacity: 0.5,
              color: '#AFAFAF',
              width: 1
            }
          },
          axisLine: {
            lineStyle: {
              opacity: 1,
              color: '#AFAFAF',
              width: 1
            }
          },
          name: {
            formatter(value, data) {
              const {
                ratioT,
                ratioH,
                enabled,
                valueObj,
                targetValue,
                targetOrBasicName
              } = data.data
              let result = `{title|${value}}`
              if (data.data.wordColor) {
                switch (data.data.wordColor) {
                  case 'Y':
                    result = `{titleY|${value}}`
                    if (enabled) {
                      result = `{clicktitleY|${value}}{arrow|}`
                    }
                    break
                  case 'G':
                    result = `{titleG|${value}}`
                    if (enabled) {
                      result = `{clicktitleG|${value}}{arrow|}`
                    }
                    break
                  case 'R':
                    result = `{titleR|${value}}`
                    if (enabled) {
                      result = `{clicktitleR|${value}}{arrow|}`
                    }
                    break
                  case null:
                    if (enabled) {
                      result = `{clicktitle|${value}}{arrow|}`
                    }
                    break
                }
              } else {
                result = `{clicktitle|${value}}{arrow|}`
              }

              // let result = `{title|${value}}`;
              // // if (valueObj.ifRedWord === 1) {
              // //   result = `{redtitle|${value}}`;
              // // } else if (enabled) {
              // //   result = `{clicktitle|${value}}{arrow|}`;
              // // }
              // if (enabled) {
              //   result = `{clicktitle|${value}}{arrow|}`;
              // }
              // if (valueObj.ifGrenUp === 1) {
              //   result += '{upicon|}';
              // }
              // if (valueObj.ifRedDown === 1) {
              //   result += '{downicon|}';
              // }
              result += '\n'
              // console.log(targetOrBasicName);
              // 完成值 目标值
              if (targetOrBasicName.key !== '') {
                if (valueObj.value > targetValue.value) {
                  result += `{subtitle|完成值:${valueObj.value}  ${targetOrBasicName.key}${targetOrBasicName.value}}`
                } else {
                  result += `{subtitleRed|完成值:${valueObj.value}}  {subtitle|${targetOrBasicName.key}${targetOrBasicName.value}}`
                }
              } else {
                result += `{subtitle|完成值:${valueObj.value}}`
              }
              result += '\n'
              // 判断同比
              // if (ratioT.ifyellowExclamation) {
              //   result += `{wrongicon|}{subtitleYellow|同比:${(
              //     ratioT.value * 100
              //   ).toFixed2(2)}%}`;
              // } else {
              //   result += `{subtitle|同比:${(ratioT.value * 100).toFixed2(2)}%}`;
              // }

              if (ratioT.ifRedWord) {
                result += `{subtitleRed|同比:${(ratioT.value * 100).toFixed(
                  2
                )}%}`
              } else {
                result += `{subtitle|同比:${(ratioT.value * 100).toFixed2(2)}%}`
              }
              if (ratioT.ifRedDown) {
                result += '{downicon|}'
              }
              if (ratioT.ifGrenUp) {
                result += '{upicon|}'
              } // 判断环比
              // if (ratioH.ifyellowExclamation) {
              //   result += ` {wrongicon|}{subtitleYellow|环比:${(
              //     ratioH.value * 100
              //   ).toFixed2(2)}%}`;
              // } else {
              //   result += ` {subtitle|环比:${(ratioH.value * 100).toFixed(
              //     2
              //   )}%}`;
              // }
              if (ratioH.ifRedWord) {
                result += `{subtitleRed|环比:${(ratioH.value * 100).toFixed(
                  2
                )}%}`
              } else {
                result += `{subtitle|环比:${(ratioH.value * 100).toFixed2(2)}%}`
              }

              if (ratioH.ifRedDown) {
                result += '{downicon|}'
              }
              if (ratioH.ifGrenUp) {
                result += '{upicon|}'
              }
              // return `{title|${value}}\n{subtitle|同比${(ratioT.value * 100).toFixed2(2)}%}
              // {downicon|}{wrongicon|}{subtitle|环比${(ratioH.value * 100).toFixed2(2)}%}{upicon|}`;
              console.log('_++_+_', result)
              return result
            },
            rich: {
              title: {
                color: '#262626',
                fontSize: 14,
                lineHeight: 18,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              titleY: {
                color: '#FF9900',
                fontSize: 14,
                lineHeight: 18,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              titleR: {
                color: '#FF3333',
                fontSize: 14,
                lineHeight: 18,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              titleG: {
                color: '#4E9A35',
                fontSize: 14,
                lineHeight: 18,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              redtitle: {
                color: '#ff7a7a',
                fontSize: 14,
                lineHeight: 18,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              arrow: {
                backgroundColor: {
                  image: arrowright
                },
                color: '#',
                height: 10,
                width: 10,
                align: 'left'
              },
              clicktitle: {
                color: '#262626',
                fontSize: 14,
                lineHeight: 24,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              clicktitleG: {
                // color: '#5CFFFD',
                fontSize: 14,
                lineHeight: 24,
                color: '#4E9A35',
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              clicktitleY: {
                // color: '#5CFFFD',
                fontSize: 14,
                lineHeight: 24,
                color: '#FF9900',
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              clicktitleR: {
                // color: '#5CFFFD',
                fontSize: 14,
                lineHeight: 24,
                color: '#FF3333',
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              subtitle: {
                color: '#262626',
                fontSize: 11,
                lineHeight: 15,
                padding: [0, 10, 0, 0],
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              subtitleRed: {
                color: '#ff7774',
                fontSize: 11,
                lineHeight: 15,
                padding: [0, 10, 0, 0],
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              subtitleYellow: {
                color: 'yellow',
                fontSize: 11,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left'
              },
              upicon: {
                backgroundColor: {
                  image: upicon
                },
                height: 15,
                width: 15,
                align: 'left'
              },
              downicon: {
                backgroundColor: {
                  image: downicon
                },
                height: 15,
                width: 15,
                align: 'left'
              },
              wrongicon: {
                backgroundColor: {
                  image: wrongicon
                },
                height: 15,
                width: 15,
                align: 'left'
              }
            }
          },
          triggerEvent: true,
          indicator
        },
        series: series
      }

      this.updateOption()

      /* 以下是原始代码，请勿删除  2020--4-20*/
      /* return {
        title: {
          // text: this.radartitle,
          left: 'center',
        },
        color: ['#00E8B1', '#ff7774'],
        legend: [
          {
            data: ['完成值'],
            left: 30,
            icon: 'circle',
            selectedMode: false,
            textStyle: {
              color: '#262626',
            },
          },
          {
            data: ['指标阈值'],
            left: 110,
            icon: `image://${redLegend}`,
            itemWidth: 14,
            itemHeight: 14,
            selectedMode: false,
            textStyle: {
              color: '#262626',
            },
          },
        ],
        radar: {
          scale: true,
          center: ['50%', '50%'],
          radius: '66%',
          shape: 'circle',
          splitArea: {
            areaStyle: {
              color: ['transparent'],
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              opacity: 0.5,
              color: '#ffffff',
              width: 1,
            },
          },
          axisLine: {
            lineStyle: {
              opacity: 1,
              color: '#ffffff',
              width: 1,
            },
          },
          name: {
            formatter(value, data) {
              const {
                ratioT,
                ratioH,
                enabled,
                valueObj,
                targetValue,
                targetOrBasicName,
              } = data.data;
              let result = `{title|${value}}`;
              if(data.data.wordColor){
                  switch (data.data.wordColor) {
                    case 'Y':
                      result = `{titleY|${value}}`;
                      if (enabled) {
                        result = `{clicktitleY|${value}}{arrow|}`;
                      }
                        break;
                    case 'G':
                        result = `{titleG|${value}}`;
                      if (enabled) {
                        result = `{clicktitleG|${value}}{arrow|}`;
                      }
                        break;
                    case 'R':
                        result = `{titleR|${value}}`;
                      if (enabled) {
                        result = `{clicktitleR|${value}}{arrow|}`;
                      }
                        break;
                    case null:
                      if (enabled) {
                        result = `{clicktitle|${value}}{arrow|}`;
                      }
                        break;
                }
              }else{
                 result = `{clicktitle|${value}}{arrow|}`;
              }

              // let result = `{title|${value}}`;
              // // if (valueObj.ifRedWord === 1) {
              // //   result = `{redtitle|${value}}`;
              // // } else if (enabled) {
              // //   result = `{clicktitle|${value}}{arrow|}`;
              // // }
              // if (enabled) {
              //   result = `{clicktitle|${value}}{arrow|}`;
              // }
              // if (valueObj.ifGrenUp === 1) {
              //   result += '{upicon|}';
              // }
              // if (valueObj.ifRedDown === 1) {
              //   result += '{downicon|}';
              // }
              result += '\n';
              // console.log(targetOrBasicName);
              // 完成值 目标值
              if (targetOrBasicName.key !== '') {
                if (valueObj.value > targetValue.value) {
                  result += `{subtitle|完成值:${valueObj.value}  ${targetOrBasicName.key}${targetOrBasicName.value}}`;
                } else {
                  result += `{subtitleRed|完成值:${valueObj.value}}  {subtitle|${targetOrBasicName.key}${targetOrBasicName.value}}`;
                }
              } else {
                result += `{subtitle|完成值:${valueObj.value}}`;
              }
              result += '\n';
              // 判断同比
              // if (ratioT.ifyellowExclamation) {
              //   result += `{wrongicon|}{subtitleYellow|同比:${(
              //     ratioT.value * 100
              //   ).toFixed2(2)}%}`;
              // } else {
              //   result += `{subtitle|同比:${(ratioT.value * 100).toFixed2(2)}%}`;
              // }

              if (ratioT.ifRedWord) {
                result += `{subtitleRed|同比:${(ratioT.value * 100).toFixed(
                  2,
                )}%}`;
              } else {
                result += `{subtitle|同比:${(ratioT.value * 100).toFixed2(2)}%}`;
              }
              if (ratioT.ifRedDown) {
                result += '{downicon|}';
              }
              if (ratioT.ifGrenUp) {
                result += '{upicon|}';
              } // 判断环比
              // if (ratioH.ifyellowExclamation) {
              //   result += ` {wrongicon|}{subtitleYellow|环比:${(
              //     ratioH.value * 100
              //   ).toFixed2(2)}%}`;
              // } else {
              //   result += ` {subtitle|环比:${(ratioH.value * 100).toFixed(
              //     2
              //   )}%}`;
              // }
              if (ratioH.ifRedWord) {
                result += `{subtitleRed|环比:${(ratioH.value * 100).toFixed(
                  2,
                )}%}`;
              } else {
                result += `{subtitle|环比:${(ratioH.value * 100).toFixed2(2)}%}`;
              }

              if (ratioH.ifRedDown) {
                result += '{downicon|}';
              }
              if (ratioH.ifGrenUp) {
                result += '{upicon|}';
              }
              // return `{title|${value}}\n{subtitle|同比${(ratioT.value * 100).toFixed2(2)}%}
              // {downicon|}{wrongicon|}{subtitle|环比${(ratioH.value * 100).toFixed2(2)}%}{upicon|}`;
              console.log('_++_+_', result);
              return result;
            },
            rich: {
              title: {
                color: '#262626',
                fontSize: 14,
                lineHeight: 18,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
               titleY: {
                color: '#FF9900',
                fontSize: 14,
                lineHeight: 18,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
               titleR: {
                color: '#FF3333',
                fontSize: 14,
                lineHeight: 18,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
               titleG: {
                color: '#00FF00',
                fontSize: 14,
                lineHeight: 18,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
              redtitle: {
                color: '#ff7a7a',
                fontSize: 14,
                lineHeight: 18,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
              arrow: {
                backgroundColor: {
                  image: arrowright,
                },
                height: 15,
                width: 15,
                align: 'left',
              },
              clicktitle: {
                // color: '#5CFFFD',
                fontSize: 14,
                lineHeight: 24,
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
              clicktitleG: {
                // color: '#5CFFFD',
                fontSize: 14,
                lineHeight: 24,
                color: '#00FF00',
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
              clicktitleY: {
                // color: '#5CFFFD',
                fontSize: 14,
                lineHeight: 24,
                color: '#FF9900',
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
              clicktitleR: {
                // color: '#5CFFFD',
                fontSize: 14,
                lineHeight: 24,
                color: '#FF3333',
                // textShadowBlur: 3,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
              subtitle: {
                color: '#262626',
                fontSize: 11,
                lineHeight: 15,
                padding: [0, 10, 0, 0],
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
              subtitleRed: {
                color: '#ff7774',
                fontSize: 11,
                lineHeight: 15,
                padding: [0, 10, 0, 0],
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
              subtitleYellow: {
                color: 'yellow',
                fontSize: 11,
                // textShadowColor: 'rgba(0,0,0,0.4)',
                // textShadowOffsetX: 1,
                // textShadowOffsetY: 2,
                align: 'left',
              },
              upicon: {
                backgroundColor: {
                  image: upicon,
                },
                height: 15,
                width: 15,
                align: 'left',
              },
              downicon: {
                backgroundColor: {
                  image: downicon,
                },
                height: 15,
                width: 15,
                align: 'left',
              },
              wrongicon: {
                backgroundColor: {
                  image: wrongicon,
                },
                height: 15,
                width: 15,
                align: 'left',
              },
            },
          },
          triggerEvent: true,
          indicator,
        },
        series: [
          {
            name: '完成值',
            type: 'radar',
            data: [this.data[0].value],
            symbolSize: 0,
            areaStyle: {
              normal: {
                opacity: 0.2,
              },
            },
            label: {
              normal: {
                show: true,
                formatter: params => `{subtitle| ${params.value}}`,
                rich: {
                  subtitle: {
                    // color: '#83D7FF',
                    position: 'relative',
                    showMaxLabel: null,
                    fontSize: 16,
                    fontWeight: 500,
                    textShadowColor: 'rgba(0,0,0,0.2)',
                    textShadowOffsetX: 1,
                    textShadowOffsetY: 2,
                  },
                },
              },
            },
            labelLine: { show: true },
          },
          {
            name: '指标阈值',
            type: 'radar',
            data: [this.data[1].value],
            symbolSize: 0,
            areaStyle: {
              normal: {
                opacity: 0,
              },
            },
            lineStyle: {
              normal: {
                color: '#ff7774',
                width: 2,
                type: 'dashed',
              },
            },
            label: {
              normal: {
                show: true,
                formatter: params => `{subtitle| ${params.value}}`,
                rich: {
                  subtitle: {
                    // color: '#83D7FF',
                    position: 'relative',
                    showMaxLabel: null,
                    fontSize: 16,
                    fontWeight: 500,
                    textShadowColor: 'rgba(0,0,0,0.2)',
                    textShadowOffsetX: 1,
                    textShadowOffsetY: 2,
                  },
                },
              },
            },
            labelLine: { show: true },
          },
        ],
      };*/
    },

    setOptions() {
      this.pieOptions.series[0].data = this.data.map((d, index) => ({
        value: d.count,
        name: `${d.name}`,
        itemStyle: {
          normal: {
            color: this.pieOptions.color[index]
          }
        }
      }))
    },

    insert_flg(str, flg, sn) {
      let newstr = ''
      for (let i = 0; i < str.length; i += sn) {
        const tmp = str.substring(i, i + sn)
        newstr += tmp + flg
      }
      return newstr
    },

    initChart() {
      this.chart = echarts.init(this.$refs.RadarCharts, null, {
        render: 'svg'
      })
      this.generateOptions()
      const tooltipDom = document.querySelector('.tooltip')
      let displayThrottle = null
      let isout = true
      this.chart.on('click', 'radar', (params) => {
        if (params.targetType === 'axisName') {
          const temlist = this.data[0].data.filter((it) => {
            if (params.name.indexOf(it.name) !== -1) {
              return true
            }
            return false
          })

          if (temlist[0].enabled) {
            this.$emit('axisClick', { id: temlist[0].id, name: temlist[0].name })
          }
        }
      })
      this.chart.on('mousemove', 'radar', (params) => {
        const { offsetX, offsetY } = params.event
        const temlist = this.data[0].data.filter((it) => {
          if (params.name.indexOf(it.name) !== -1) {
            return true
          }
          return false
        })
        if (temlist[0]) {
          if (temlist[0].desc === '' || temlist[0].desc === null) {
            return
          }
          tooltipDom.style.display = 'inherit'
          tooltipDom.style.left = `${offsetX}px`
          tooltipDom.style.top = `${offsetY - tooltipDom.offsetHeight}px`
          tooltipDom.innerHTML = temlist[0].desc
        }
        isout = false
      })
      this.chart.on('mouseout', 'radar', (params) => {
        if (displayThrottle) {
          clearTimeout(displayThrottle)
        }
        isout = true
        displayThrottle = setTimeout(() => {
          if (isout) {
            tooltipDom.style.display = 'none'
          }
          displayThrottle = null
        }, 100)
      })

      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    },

    updateOption() {
      const _this = this
      this.chart.setOption(this.pieOptions)
    }
  }
}
</script>

<style lang="less" scoped>
.Radar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  .echarts {
    width: 100%;
    height: 100%;
  }
  .tooltip {
    position: absolute;
    display: none;
    top: 0;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px;
    border-radius: 3px;
    color: #ffff;
    font-size: 13px;
    transition: all 0.2s;
    text-align: left;
  }
}
</style>
