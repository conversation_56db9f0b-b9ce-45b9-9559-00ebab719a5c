<template>
  <div class="dateChoose">
    <div v-if="defaultDateTypeList.length" class="radioArea">
      <label>选择时间:</label>
      <!-- <el-radio-group v-model="dateType" size="small" @change="dateTypeChange">
        <el-radio v-for="item in defaultDateTypeList" :key="item" :label="item"/>
      </el-radio-group>-->
      <el-select
        ref="refHandle01"
        v-model="dateType"
        class="partment-select dateSelect"
        size="small"
        popper-class="monthPopper"
        @focus="inputFocus"
        @change="dateTypeChange"
      >
        <el-option v-for="item in defaultDateTypeList" :key="item" :label="item" :value="item" />
      </el-select>
    </div>
    <!-- 时间日期选择 -->
    <el-date-picker
      v-model="day_choosed"
      :class="dateType=='日'&& 'showit' "
      class="controlDom"
      type="date"
      size="small"
      popper-class="pickerPopper"

      placeholder="选择日期"
      @change="dayChange"
    />
    <el-date-picker
      v-if="dateType=='周'"
      v-model="week_choosed_text"
      :class="dateType=='周'&& 'showit' "
      type="week"
      size="small"
      class="controlDom"
      :picker-options="pickOptions"
      popper-class="pickerPopper"
      format="yyyy 第 WW 周"
      placeholder="选择周"
    />
    <el-date-picker
      v-if="dateType=='日累计'"
      v-model="day_daterange"
      :class="dateType=='日累计' && 'showit'"
      class="controlDom"
      type="date"
      size="small"
      :clearable="false"
      :picker-options="pickOptions"
      popper-class="pickerPopper"
      placeholder="选择日期"
      @change="daterangeChange"
    />
    <el-date-picker
      v-model="month_choosed"
      :class="dateType=='月' && 'showit'"
      class="controlDom"
      type="month"
      size="small"
      format="yyyy年MM月"
      :clearable="false"
      placeholder="选择月份"
      :picker-options="pickOptions"
      @change="monthChange"
    />
    <div class="quarterChooseArea">
      <el-date-picker
        v-model="quarter_choosed"
        :class="dateType=='季度' && 'showit'"
        class="controlDom quarterChoose"
        popper-class="quarterPoper"
        type="month"
        :clearable="false"
        format="yyyy年M季度"
        placeholder="选择季度"
        size="small"
        :picker-options="pickOptions"
        @focus="handleQuarterFocus"
        @change="quarterChange"
      />
    </div>
    <el-date-picker
      v-model="year_choosed"
      :class="dateType=='年' && 'showit'"
      type="year"
      :clearable="false"
      class="controlDom"
      size="small"
      placeholder="选择年"
      :picker-options="pickOptions"
      @change="yearChange"
    />
    <slot name="default" />
  </div>
</template>
<script>
import Vue from 'vue'
import { RadioGroup, Radio, DatePicker } from 'element-ui'
import Common from '../../lib/date'

Vue.use(RadioGroup)
Vue.use(Radio)
Vue.use(DatePicker)
export default {
  name: 'DateChoose',
  props: {
    // 日期类型选择数组
    defaultDateTypeList: {
      type: Array,
      default: () => ['日', '年', '月', '季度', '日累计']
    },
    // 默认日期
    defaultDate: {
      type: String,
      default: Common.formatDate(new Date(), 'yyyy-MM')
    }
  },
  data() {
    const dateString = Common.dateTranslate(this.defaultDate).date
    const dateTypeName = Common.dateTranslate(this.defaultDate).dateName
    console.log('dateString:', dateString)
    console.log('dateTypeName:', dateTypeName)
    let day = null
    let month = null
    let week = null
    let quarter = null
    let year = null
    day = new Date(dateString)
    week = new Date(dateString)
    month = new Date(dateString)
    quarter = new Date(dateString)
    year = new Date(dateString)
    // 设置上周/上月/上季
    if (dateTypeName === '日' || dateTypeName === '日累计') {
      month.setMonth(month.getMonth() - 1)
      quarter.setMonth(quarter.getMonth() - 3)
    } else if (dateTypeName === '月') {
      quarter.setMonth(quarter.getMonth() - 3)
    }

    return {
      value: '',
      day_daterange: day || '',
      day_choosed: day, // 天Date
      month_choosed: month, // 月Date
      week_choosed_text: Common.formatDate(week, 'yyyyMM第w周'), // 周String
      quarter_choosed: new Date(quarter.setMonth(quarter.getMonth() / 3)), // 季度Date
      year_choosed: year, // 年Date
      dateType: dateTypeName || '月',
      pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },

  watch: {
    defaultDateTypeList(val) {
      if (val.indexOf(this.dateType) === -1 && val[0]) {
        this.dateType = val[0].toString()
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    dropDownVisible() {
      const { classList } = document.querySelector(
        '.dateSelect .el-input--suffix'
      )
      if (classList.toString().indexOf('is-focus') !== -1) {
        document.querySelector('.dateSelect').click()
      }
    },
    inputFocus() {
      setTimeout(() => {
        const elMain = document.querySelector('.el-main')
        // elMain.removeEventListener("scroll", this.dropDownVisible);
        // elMain.addEventListener("scroll", this.dropDownVisible);
      }, 10)
    },
    handleQuarterFocus() {
      // 季度选择框focus触发
      // 替换月度选择为季度选择
      this.$nextTick(() => {
        console.log(
          'ddddd',
          document.querySelector(
            '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(1) a'
          )
        )
        document.querySelector(
          '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(1) a'
        ).innerText = '第一季度'
        document.querySelector(
          '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(2) a'
        ).innerText = '第二季度'
        document.querySelector(
          '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(3) a'
        ).innerText = '第三季度'
        document.querySelector(
          '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(4) a'
        ).innerText = '第四季度'
      })
    },
    dateTypeChange(val) {
      switch (val) {
        case '日':
          this.$emit('dateType', '日')
          this.emitDateChange(
            Common.formatDate(this.day_choosed, 'yyyy-MM-dd'), '日'
          )

          break
        case '日累计':
          // let startTime = Common.formatDate(val[0], "yyyy-MM-dd");
          // let endTime = Common.formatDate(val[1], "yyyy-MM-dd");
          // let list = [startTime, endTime];
          this.$emit('dateType', '日累计')
          this.emitDateChange(
            Common.formatDate(this.day_daterange, 'yyyy-MM-dd'), '日累计'
          )

          break
        case '月':
          this.$emit('dateType', '月')
          this.emitDateChange(Common.formatDate(this.month_choosed, 'yyyy-MM'), '月')
          break
        case '季度':
          /* eslint-disable */
          const year = this.quarter_choosed.getFullYear();
          const month = this.quarter_choosed.getMonth() * 3 + 1;
          /* eslint-enable */
          this.$emit('dateType', '季度')
          this.emitDateChange(
            Common.formatDate(
              new Date(`${year}-${month < 10 ? '0' : ''}${month}-01`),
              'yyyy年q季度'
            ), '季度'
          )
          break
        case '年':
          this.$emit('dateType', '年')
          this.emitDateChange(Common.formatDate(this.year_choosed, 'yyyy'), '年')
          break
        default:
          break
      }
    },
    daterangeChange(val) {
      // let startTime = Common.formatDate(val[0], "yyyy-MM-dd");
      // let endTime = Common.formatDate(val[1], "yyyy-MM-dd");
      // let list = [startTime, endTime];
      this.$emit('dateType', '日累计')
      this.emitDateChange(Common.formatDate(val, 'yyyy-MM-dd'), '日累计')
    },
    dayChange(val) {
      this.$emit('dateType', '日')
      this.emitDateChange(Common.formatDate(val, 'yyyy-MM-dd'), '日')
    },
    monthChange(val) {
      this.$emit('dateType', '月')
      this.emitDateChange(Common.formatDate(val, 'yyyy-MM'), '月')
    },
    quarterChange(val) {
      // 月份==>季度月份
      const year = val.getFullYear()
      const month = val.getMonth() * 3 + 1
      const transQuarter = new Date(
        `${year}-${month < 10 ? '0' : ''}${month}-01`
      )
      this.$emit('dateType', '季度')
      this.emitDateChange(Common.formatDate(transQuarter, 'yyyy-q季度'), '季度')
    },
    yearChange(val) {
      this.$emit('dateType', '年')
      this.emitDateChange(Common.formatDate(val, 'yyyy'), '年')
    },
    emitDateChange(val, type) {
      this.$emit('dateChange', val, type)
    }
  }
}
</script>
<style lang="less" scope>
.dateChoose {
  display: inline-block;
  color: white;
  .radioArea {
    float: left;
    vertical-align: middle;
    .el-radio {
      margin-right: 15px;
    }
    .el-radio__label {
      padding-left: 5px;
      color: white;
    }
    & > label {
      margin-right: 10px;
      // font-size: 13px;
      font-weight: normal;
      font-family: SourceHanSansSC-Regular;
      font-size: 14px !important;
      color: #262626 !important;
      letter-spacing: -0.27px;
    }
  }
  .weekChooseArea {
    display: inline-block;
    position: relative;
    label {
      position: absolute;
      z-index: 2;
      pointer-events: none;
      font-size: 14px;
      line-height: 14px;
      color: rgb(96, 98, 102);
      top: 13px;
      left: 30px;
    }
  }
  .quarterChooseArea {
    display: inline-block;
  }
  .controlDom {
    width: 0px !important;
    margin-right: 0px !important;
    vertical-align: middle;
    // visibility: hidden;
    display: none;
    color: white;
    &.showit {
      width: 160px !important;
      // margin-right: 10px !important;
      // visibility:inherit;
      display: inline-block;
    }
    input {
      background-color: #ffffff;
      border-color: #e6e6e6 !important;
      // color: white;
      text-align: center;
      &::placeholder {
        font-family: SourceHanSansSC-Regular;
        font-size: 14px;
        color: #595959;
        letter-spacing: -0.27px;
      }
    }
    // .el-input__prefix {
    //   color: white;
    // }
  }
  .daterange {
    width: 300px !important;
    margin-right: 10px !important;
    // visibility:inherit;
    display: inline-block;
  }
}
</style>
<style lang="less">
.quarterPoper {
  .el-picker-panel__content .el-month-table tbody tr {
    display: none;
  }
  .el-picker-panel__content .el-month-table tbody tr:first-child {
    display: table-row;
  }
}
.dateSelect {
  margin-right:5px;
  input {
    width: 100px;
  }
}
</style>
