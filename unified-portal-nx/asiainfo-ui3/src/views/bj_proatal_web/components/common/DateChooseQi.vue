<template>
  <div class="dateChoose">
    <div class="radioArea">
      <label>选择时间:</label>
      <!-- <el-radio-group v-model="dateType" size="small" @change="dateTypeChange">
        <el-radio v-for="item in defaultDateTypeList" :key="item" :label="item"/>
      </el-radio-group>-->
      <el-select
        class="partment-select dateSelect"
        v-model="dateType"
        size="small"
        ref="refHandle01"
        @focus="inputFocus"
        popper-class="monthPopper"
        @change="dateTypeChange"
      >
        <el-option v-for="item in defaultDateTypeList" :key="item" :label="item" :value="item"></el-option>
      </el-select>
    </div>
    <!-- 时间日期选择 -->
    <el-date-picker
      :class="dateType=='日' && 'showit'"
      v-model="day_choosed"
      class="controlDom"
      type="date"
      size="small"
      style="width:236px !important;"
      popper-class="pickerPopper"
      :clearable="false"
      placeholder="选择日期"
      @change="dayChange"
      :picker-options="pickOptions"
    ></el-date-picker>
    <el-date-picker
      :class="dateType=='月' && 'showit'"
      v-model="month_choosed"
      class="controlDom"
      type="month"
      size="small"
      style="width:236px !important;"
      format="yyyy年MM月"
      :clearable="false"
      placeholder="选择月份"
      :picker-options="pickOptions"
      @change="monthChange"
    ></el-date-picker>
    <WeekInput
      :class="dateType=='周' && 'showit'"
      class="controlDom"
      placeholder="选择周"
       style="width:236px !important;"
      @change="weekChange"
      :picker-options="pickOptions"
      :defaultWeek="week_choosed_text"
    />
    <div class="quarterChooseArea">
      <el-date-picker
        :class="dateType=='期' && 'showit'"
        v-model="quarter_choosed"
         style="width:236px !important;"
        class="controlDom quarterChoose"
        popper-class="quarterPoper"
        type="month"
        :clearable="false"
        format="yyyy年第M期"
        placeholder="选择期"
        size="small"
        @focus="handleQuarterFocus"
        @change="quarterChange"
      ></el-date-picker>
    </div>
    <div class="quarterChooseArea">
      <el-date-picker
       style="width:236px !important;"
              :class="dateType=='季' && 'showit'"
              v-model="quarter_choosed1"
              class="controlDom quarterChoose"
              popper-class="quarterPoper1"
              type="month"
              :clearable="false"
              format="yyyy年M季度"
              placeholder="选择季度"
              size="small"
              @focus="handleQuarterFocus1"
              @change="quarterChange1"
              :picker-options="pickOptions1"
      ></el-date-picker>
    </div>
    <el-date-picker
      :class="dateType=='年' && 'showit'"
      v-model="year_choosed"
      type="year"
      :clearable="false"
      class="controlDom"
      size="small"
       style="width:236px !important;"
      placeholder="选择年"
      :picker-options="pickOptions"
      @change="yearChange"
    ></el-date-picker>
    <slot name="default"></slot>
  </div>
</template>
<script>
import Vue from 'vue';
import { RadioGroup, Radio, DatePicker,Select, Option } from 'element-ui';
import Common from '../../lib/dateQi';
import WeekInput from './WeekInput.vue';

Vue.use(RadioGroup);
Vue.use(Radio);
Vue.use(DatePicker);
Vue.use(Select);
Vue.use(Option);
export default {
  name: 'dateChoose',
  props: {
    // 日期类型选择数组
    defaultDateTypeList: {
      type: Array,
      default: () => ['年', '期', '月', '周', '日'],
    },
    // 默认日期
    defaultDate: {
      type: String,
      default: Common.formatDate(new Date(), 'yyyyMM第w周'),
    },
  },
  data() {
    const dateString = Common.dateTranslate(this.defaultDate).date;
    const dateTypeName = Common.dateTranslate(this.defaultDate).dateName;
    let day = null;
    let month = null;
    let week = null;
    let quarter = null;
    let year = null;

    day = new Date(dateString);
    week = new Date(dateString);
    month = new Date(dateString);
    quarter = new Date(dateString);
    year = new Date(dateString);
    // 设置上周/上月/上季
    if (dateTypeName === '日') {
      week.setDate(week.getDate() - 7);
      month.setMonth(month.getMonth() - 1);
      quarter.setMonth(quarter.getMonth() - 3);
    } else if (dateTypeName === '周') {
      month.setMonth(month.getMonth() - 1);
      quarter.setMonth(quarter.getMonth() - 3);
    } else if (dateTypeName === '月') {
      quarter.setMonth(quarter.getMonth() - 3);
    }
    return {
      value: '',
      day_choosed: day, // 天Date
      month_choosed: month, // 月Date
      week_choosed_text: Common.formatDate(week, 'yyyyMM第w周'), // 周String
      quarter_choosed: new Date(quarter.setMonth(quarter.getMonth() / 3)), // 期Date
      quarter_choosed1: new Date(quarter.setMonth(quarter.getMonth() / 3)), // 季度Date
      year_choosed: year, // 年Date
      dateType: dateTypeName,
      pickOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      pickOptions1: {
          disabledDate(time) {
              return time.getTime() > Date.now();
          },
      },
    };
  },
  components: { WeekInput },
  created() {
  },
  computed:{
    
  },
  methods: {
    dropDownVisible() {
      const { classList } = document.querySelector(
        '.dateSelect .el-input--suffix',
      );
      if (classList.toString().indexOf('is-focus') !== -1) {
        document.querySelector('.dateSelect').click();
      }
    },
    inputFocus() {
      setTimeout(() => {
        const elMain = document.querySelector('.el-main');
        elMain.removeEventListener('scroll', this.dropDownVisible);
        elMain.addEventListener('scroll', this.dropDownVisible);
      }, 10);
    },
    handleQuarterFocus() {
      // 季度选择框focus触发
      // 替换月度选择为季度选择
      this.$nextTick(() => {
        console.log(
          'ddddd',
          document.querySelector(
            '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(1) a',
          ),
        );
        document.querySelector(
          '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(1) a',
        ).innerText = '第1期';
        document.querySelector(
          '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(2) a',
        ).innerText = '第2期';
        document.querySelector(
          '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(3) a',
        ).innerText = '第3期';
        document.querySelector(
          '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(4) a',
        ).innerText = '第4期';
        document.querySelector(
          '.quarterPoper .el-picker-panel__content .el-month-table tbody tr:nth-child(2) td:nth-child(1) a',
        ).innerText = '第5期';
      });
    },
    dateTypeChange(val) {
      switch (val) {
        case '日':
          this.emitDateChange(
            Common.formatDate(this.day_choosed, 'yyyy-MM-dd'),
          );
          break;
        case '周':
          this.emitDateChange(this.week_choosed_text);
          break;
        case '月':
          this.emitDateChange(Common.formatDate(this.month_choosed, 'yyyyMM'));
          break;
        case '期':
          /* eslint-disable */
          const year = this.quarter_choosed.getFullYear();
          const month = this.quarter_choosed.getMonth() + 1;
          /* eslint-enable */
          this.emitDateChange(
            Common.formatDate(
              new Date(`${year}-${month < 10 ? '0' : ''}${month}-01`),
              'yyyy年第q期',
            ),
          );
          break;
        case '季':
            /* eslint-disable */
            const year1 = this.quarter_choosed.getFullYear();
            const month1 = this.quarter_choosed.getMonth() * 3 + 1;
            /* eslint-enable */
            this.emitDateChange(
                Common.formatDate(
                    new Date(`${year1}-${month1 < 10 ? '0' : ''}${month1}-01`),
                    'yyyy年q季度',
                ),
            );
            break;
        case '年':
          this.emitDateChange(Common.formatDate(this.year_choosed, 'yyyy'));
          break;
        default:
          break;
      }
    },
    dayChange(val) {
      this.emitDateChange(Common.formatDate(val, 'yyyy-MM-dd'));
    },
    weekChange(val) {
      this.week_choosed_text = val;
      this.emitDateChange(this.week_choosed_text);
    },
    monthChange(val) {
      this.emitDateChange(Common.formatDate(val, 'yyyyMM'));
    },
    quarterChange(val) {
      // 月份==>季度月份
      const year = val.getFullYear();
      const month = val.getMonth() * 3 + 1;
      const transQuarter = new Date(
        `${year}-${month < 10 ? '0' : ''}${month}-01`,
      );
      console.log('transQuarter', month, val.getMonth());
      this.emitDateChange(Common.formatDate(val, 'yyyy年第q期'));
    },
    yearChange(val) {
      this.emitDateChange(Common.formatDate(val, 'yyyy'));
    },
    showWeekPicker() {
      // 触发周选择器
      this.$refs.weekChoosePicker.focus();
    },
    emitDateChange(val) {
      this.$emit('dateChange', val);
    },
    handleQuarterFocus1() {
        // 季度选择框focus触发
        // 替换月度选择为季度选择
        this.$nextTick(() => {
            console.log(
                'ddddd',
                document.querySelector(
                    '.quarterPoper1 .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(1) a',
                ),
            );
            document.querySelector(
                '.quarterPoper1 .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(1) a',
            ).innerText = '第一季度';
            document.querySelector(
                '.quarterPoper1 .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(2) a',
            ).innerText = '第二季度';
            document.querySelector(
                '.quarterPoper1 .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(3) a',
            ).innerText = '第三季度';
            document.querySelector(
                '.quarterPoper1 .el-picker-panel__content .el-month-table tbody tr:first-child td:nth-child(4) a',
            ).innerText = '第四季度';
        });
    },
    quarterChange1(val) {
        // 月份==>季度月份
        const year = val.getFullYear();
        const month = val.getMonth() + 1;
        const transQuarter = new Date(
            `${year}-${month < 10 ? '0' : ''}${month}-01`,
        );
        console.log("val:",val);
        console.log("transQuarter：",transQuarter);
        this.emitDateChange(Common.formatDate(transQuarter, 'yyyy年q季度'));
    },

    timeChange(dateStr){
      const dateString = Common.dateTranslate(dateStr).date;
      const dateTypeName = Common.dateTranslate(dateStr).dateName;
      let day = null;
      let month = null;
      let week = null;
      let quarter = null;
      let year = null;

      day = new Date(dateString);
      week = new Date(dateString);
      month = new Date(dateString);
      quarter = new Date(dateString);
      year = new Date(dateString);
      // 设置上周/上月/上季
      if (dateTypeName === '日') {
        week.setDate(week.getDate() - 7);
        month.setMonth(month.getMonth() - 1);
        quarter.setMonth(quarter.getMonth() - 3);
      } else if (dateTypeName === '周') {
        month.setMonth(month.getMonth() - 1);
        quarter.setMonth(quarter.getMonth() - 3);
      } else if (dateTypeName === '月') {
        quarter.setMonth(quarter.getMonth() - 3);
      }

      this.day_choosed = day;
      this.month_choosed = month;
      this.week_choosed_text = Common.formatDate(week, 'yyyyMM第w周');
      
      this.quarter_choosed = new Date(dateString);
      this.quarter_choosed1 = new Date(dateString);

      console.error('-==========-----',new Date(dateString));

      this.year_choosed = year;
      this.dateType = dateTypeName;
    }
  },
  watch: {
    defaultDateTypeList(val) {
      if (val.indexOf(this.dateType) === -1 && val[0]) {
        this.dateType = val[0].toString();
      }
    },
  },
};
</script>
<style lang="less" scope>
.dateChoose {
  display: inline-block;
  color: #262626;
  .radioArea {
    float: left;
    vertical-align: middle;
    line-height: 30px;
    .el-radio {
      margin-right: 15px;
    }
    .el-radio__label {
      padding-left: 5px;
      color: white;
    }
    & > label {
      margin-right: 10px;
      // font-size: 13px;
      font-weight: normal;
      font-family: SourceHanSansSC-Regular;
      font-size: 14px !important;
      color: #262626 !important;
      letter-spacing: -0.27px;
    }
  }
  .weekChooseArea {
    display: inline-block;
    position: relative;
    label {
      position: absolute;
      z-index: 2;
      pointer-events: none;
      font-size: 14px;
      line-height: 14px;
      color: rgb(96, 98, 102);
      top: 13px;
      left: 30px;
    }
  }
  .quarterChooseArea {
    display: inline-block;
  }
  .controlDom {
    width: 0px !important;
    margin-right: 0px !important;
    vertical-align: middle;
    // visibility: hidden;
    display: none;
    color: #595959;
    &.showit {
      width: 160px !important;
      margin-right: 10px !important;
      // visibility:inherit;
      display: inline-block;
    }
    &.showit.weekInput {
      width: 260px !important;
    }
    input {
      background-color: white;
      border-color: #e6e6e6 !important;
      color: #595959;
      text-align: center;
    }
    .el-input__prefix {
      color: #595959;
    }
  }
}
</style>
<style lang="less">
.quarterPoper {
  .el-picker-panel__content .el-month-table tbody tr {
    display: none;
  }
  .el-picker-panel__content .el-month-table tbody tr:not(:last-child) {
    display: table-row;
  }
  .el-picker-panel__content .el-month-table tbody tr:nth-child(2) td:not(:first-child) {
    display: none;
  }
}
.quarterPoper1 {
  .el-picker-panel__content .el-month-table tbody tr {
    display: none;
  }
  .el-picker-panel__content .el-month-table tbody tr:first-child {
    display: table-row;
  }
}
.dateSelect {
  input {
    width: 70px;
  }
}

.monthPopper {
  height: 148px;
}
</style>
