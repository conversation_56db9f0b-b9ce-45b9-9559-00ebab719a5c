<template>
  <div style="padding: 20px; box-sizing: border-box;background: #fff;">
    <el-table
      :data="tableData"
      :span-method="objectSpanMethod"
      style="width: 100%; margin-top: 0px; border: 1px solid #dfe6ec; border-bottom:0px;">
      <el-table-column
        prop="indicateName"
        label="指标名称">
      </el-table-column>
      <el-table-column
        prop="currentValue"
        label="本月">
      </el-table-column>
      <el-table-column
        prop="lastRatio"
        label="月环比">
      </el-table-column>
      <el-table-column
        prop="lastYearRatio"
        label="月同比">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
/*import { Table, TableColumn } from 'element-ui';
import Vue from 'vue';

Vue.use(TableColumn).use(Table);*/

export default {
  props: {
    parentData: {
      type: Object,
      default() {
        return [
          {
            tableVoList: {},
          },
        ];
      },
    },
  },
  data() {
    return {
      // tableData: [{
      //   type: '营业厅基础数据',
      //   rowspan: '4',
      //   id: '12987122',
      //   name: '叫号量',
      //   benyue: '234',
      //   benji: '3.2',
      //   yuetongbi: 10,
      // }, {
      //   type: '营业厅基础数据',
      //   rowspan: '4',
      //   id: '12987123',
      //   name: '等待时间',
      //   benyue: '165',
      //   benji: '4.43',
      //   yuetongbi: 12,
      // }, {
      //   type: '营业厅基础数据',
      //   rowspan: '4',
      //   id: '12987124',
      //   name: '办理时间',
      //   benyue: '324',
      //   benji: '1.9',
      //   yuetongbi: 9,
      // }, {
      //   type: '营业厅基础数据',
      //   rowspan: '4',
      //   id: '12987125',
      //   name: '营业厅数量',
      //   benyue: '621',
      //   benji: '2.2',
      //   yuetongbi: 17,
      // }, {
      //   type: '营业厅业务量类数据',
      //   rowspan: '1',
      //   id: '12987126',
      //   name: '总业务量',
      //   benyue: '539',
      //   benji: '4.1',
      //   yuetongbi: 15,
      // }],
      indicateNameNumber: 0,
    };
  },
  methods: {
    objectSpanMethod({
      row, column, rowIndex, columnIndex, event,
    }) {
      const { indicateName } = row;
      if (columnIndex === 0) {
        // if (rowIndex % 3 === 0) {
        //   return {
        //     rowspan: 1,
        //     colspan: 1,
        //   };
        // }
        return {
          rowspan: 1,
          colspan: 1,
        };
      }
    },
  },
  computed: {
    tableData() {
      return this.parentData.tableVoList;
    },
  },
  mounted() {
  },
};
</script>

<style>

</style>
