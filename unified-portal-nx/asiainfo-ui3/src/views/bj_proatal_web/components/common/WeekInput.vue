<template>
  <div class="weekInput" :class="className">
    <el-popover placement="bottom" v-model="popShow">
      <div class="el-date-picker week-popover">
        <div class="el-picker-panel__body-wrapper">
          <!---->
          <div class="el-picker-panel__body">
            <!---->
            <div class="el-date-picker__header">
              <button
                type="button"
                aria-label="前一年"
                class="el-picker-panel__icon-btn el-date-picker__prev-btn el-icon-d-arrow-left"
                @click="prevYear"
              ></button>
              <button
                type="button"
                aria-label="上个月"
                class="el-picker-panel__icon-btn el-date-picker__prev-btn el-icon-arrow-left"
                @click="prevMonth"
              ></button>
              <span role="button" class="el-date-picker__header-label">{{year}} 年</span>
              <span role="button" class="el-date-picker__header-label">{{month}} 月</span>
              <button
                type="button"
                aria-label="后一年"
                class="el-picker-panel__icon-btn el-date-picker__next-btn el-icon-d-arrow-right"
                @click="nextYear"
              ></button>
              <button
                type="button"
                aria-label="下个月"
                class="el-picker-panel__icon-btn el-date-picker__next-btn el-icon-arrow-right"
                @click="nextMonth"
              ></button>
            </div>
            <div class="el-picker-panel__content">
              <table cellspacing="0" cellpadding="0" class="el-date-table">
                <tbody>
                  <!-- <tr></tr> -->
                  <tr class="el-date-table__row">
                    <td v-for="i in 4" :key="i" :class="week === i && 'active'">
                      <div @click="weekChoose(i)">
                        <span>{{weekNames[i - 1]}}</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-if="weekNum > 4">
                    <td
                      v-for="i in (weekNum - 4)"
                      :key="i + 4"
                      :class="week === (i + 4) && 'active'"
                    >
                      <div @click="weekChoose(i + 4)">
                        <span>{{weekNames[i + 4 - 1]}}</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="week-input" slot="reference">
        <el-input
          :placeholder="placeholder"
          v-show="false"
          readonly
          v-model="weekText"
          size="small"
          prefix-icon="el-icon-date"
        ></el-input>
        <el-input
          :placeholder="placeholder"
          readonly
          v-model="weekTextInput"
          size="small"
          prefix-icon="el-icon-date"
        ></el-input>
      </div>
    </el-popover>
  </div>
</template>

<script>
import Vue from 'vue';
import { Popover, Input } from 'element-ui';

Vue.use(Popover).use(Input);
export default {
  name: 'WeekInput',
  props: {
    className: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    defaultWeek: {
      type: String,
      required: true,
    },
  },
  data() {
    const weekObj = this.formateWeekString(this.defaultWeek);
    return {
      popShow: false,
      year: parseInt(weekObj.year, 10),
      month: parseInt(weekObj.month, 10),
      week: parseInt(weekObj.week, 10),
      weekText: this.defaultWeek,
      weekNames: ['第一周', '第二周', '第三周', '第四周', '第五周', '第六周'],
      weekTextInput: '',
    };
  },
  methods: {
    prevYear() {
      this.year = parseInt(this.year, 10) - 1;
    },
    prevMonth() {
      if (parseInt(this.month, 10) === 1) {
        this.month = 12;
        this.prevYear();
      } else {
        this.month = parseInt(this.month, 10) - 1;
      }
    },
    nextYear() {
      this.year = parseInt(this.year, 10) + 1;
    },
    nextMonth() {
      if (parseInt(this.month, 10) === 12) {
        this.month = 1;
        this.nextYear();
      } else {
        this.month = parseInt(this.month, 10) + 1;
      }
    },
    weekChoose(index) {
      this.week = index;
      this.popShow = false;
      this.weekText = `${this.year}${
        this.month < 10 ? `0${this.month}` : this.month
      }第${this.week}周`;
      this.changweekshow(this.weekText);
      this.$emit('change', this.weekText);
    },
    // 获取201801第2周 年月周
    formateWeekString(val) {
      const year = val.substr(0, 4);
      const month = val.substr(4, 2);
      const week = parseInt(val.substr(7, 1), 10);
      return {
        year,
        month,
        week,
      };
    },
    changweekshow(params) {
      this.$http
        .post(`/dos-service/querydos/changweekshow?weekState=${encodeURI(params)}`)
        .then((res) => {
          console.error(888,res.data);
          this.weekTextInput = res.data;
        })
        .catch((err) => {
          console.error(888,res.data);
          this.weekTextInput = params;
        });
    },
  },
  computed: {
    weekNum() {
      const date = new Date(this.year, this.month, 0);
      return Math.ceil(date.getDate() / 7);
    },
  },
  mounted() {
    this.changweekshow(this.weekText);
  },
};
</script>
<style lang="less" scope>
.weekInput {
  display: inline-block;
  input {
    background-color: transparent;
    border-color: white !important;
    color: white;
  }
  .el-input__prefix {
    color: white;
  }
}
.week-popover {
  .el-date-table td span {
    width: auto;
  }
  td.active {
    color: #409eff;
  }
}
.week-input {
  // width: 180px;
}
</style>
