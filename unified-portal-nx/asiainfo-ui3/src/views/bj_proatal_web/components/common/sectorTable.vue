<template>
  <div style="padding: 20px; box-sizing: border-box;background: #fff;">
    <el-table
      :data="tableData"
      :span-method="objectSpanMethod"
      style="width: 100%; margin-top: 0px; border: 1px solid #dfe6ec; border-bottom:0px;">
      <el-table-column
        prop="indicateName"
        label="部门">
      </el-table-column>
      <el-table-column
        prop="firstValue"
        label="A级">
      </el-table-column>
      <el-table-column
        prop="secondValue"
        label="B级">
      </el-table-column>
      <el-table-column
        prop="thirdValue"
        label="C级">
      </el-table-column>
      <el-table-column
        prop="fourthValue"
        label="D级">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { Table, TableColumn } from 'element-ui';
import Vue from 'vue';

Vue.use(TableColumn).use(Table);

export default {
  props: ['parentData'],
  data() {
    return {
    };
  },
  methods: {
    objectSpanMethod({
      row, column, rowIndex, columnIndex, event,
    }) {
      const { indicateName } = row;
      if (columnIndex === 0) {
        // if (rowIndex % 3 === 0) {
        //   return {
        //     rowspan: 1,
        //     colspan: 1,
        //   };
        // }
        return {
          rowspan: 1,
          colspan: 1,
        };
      }
    },
  },
  computed: {
    tableData() {
      const specialVoListItem = [];
      if (this.parentData !== undefined) {
        const { specialVoList } = this.parentData;
        specialVoList.forEach((item) => {
          let {
            indicateName, firstValue, secondValue, thirdValue, fourthValue,
          } = item;
          indicateName = indicateName.replace('[', '').replace(']', '');
          item.indicateName = indicateName;
          item.firstValue = parseFloat(firstValue);
          item.secondValue = parseFloat(secondValue);
          item.thirdValue = parseFloat(thirdValue);
          item.fourthValue = parseFloat(fourthValue);
          item = JSON.parse(JSON.stringify(item));
          specialVoListItem.push(item);
        });
      }
      return specialVoListItem;
    },
  },
  mounted() {
  },
};
</script>

<style>

</style>
