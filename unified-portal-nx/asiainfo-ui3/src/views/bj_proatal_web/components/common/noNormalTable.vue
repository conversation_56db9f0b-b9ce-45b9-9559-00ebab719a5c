<template>
  <div>
    <el-table
      @cell-click='cellClick'
      :data="tableData"
      :highlight-current-row='true'
      :span-method="objectSpanMethod"
      border
      style="width: 100%; margin-top: 20px">
      <el-table-column
        prop="type"
        label="类别">
      </el-table-column>
      <el-table-column
        prop="indicateName"
        label="指标名称">
      </el-table-column>
      <el-table-column
        prop="currentValue"
        label="本月">
      </el-table-column>
      <el-table-column
        prop="lastRatio"
        label="月环比">
      </el-table-column>
      <el-table-column
        prop="lastYearRatio"
        label="月同比">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { Table, TableColumn } from 'element-ui';
import Vue from 'vue';

Vue.use(TableColumn).use(Table);

export default {
  props: {
    parentData: {
      type: Object,
      default() {
        return [
          {
            tableVoList: {},
          },
        ];
      },
    },
  },
  data() {
    return {
      indicateNameNumber: '',
      topParentIdList: [],
    };
  },
  methods: {
    cellClick(row, column, cell, event) {
      if (this.topParentIdList.indexOf(row.indicateId) !== -1) {
        // console.log(row.indicateName, row);
        this.$emit('tableClick', row);
      }
    },
    // objectSpanMethod({
    //   row, column, columnIndex,
    // }) {
    //   const { indicateName } = row;
    //   console.log(1);
    //   if (columnIndex === 0) {
    //     // console.log(22);
    //     if (row.rowSpan) {
    //       if (this.indicateNameNumber !== row.type) {
    //         this.indicateNameNumber = row.type;
    //         // return [row.rowSpan, 1];
    //         return {
    //           rowspan: row.rowSpan,
    //           colspan: 1,
    //         };
    //       }
    //       return {
    //         rowspan: 0,
    //         colspan: 0,
    //       };
    //     }
    //     return {
    //       rowspan: 1,
    //       colspan: 1,
    //     };
    //   }
    // },
    objectSpanMethod({
      row, column, rowIndex, columnIndex,
    }) {
      // console.log(row, rowIndex);
      if (columnIndex === 0) {
        if (row.rowSpan !== null) {
          return {
            rowspan: row.rowSpan,
            colspan: 1,
          };
        }
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    },
  },
  computed: {
    tableData() {
      return this.parentData.tableVoList;
      // return [
      //   {
      //     type: '分析',
      //     rowSpan: '1',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: '获取',
      //     rowSpan: 1,
      //     indicateId: '928',
      //     indicateName: 'CEM系统调研覆盖客户数',
      //     currentValue: '1493571.0000',
      //     lastRatio: '-52.95',
      //     lastYearRatio: '-43.49',
      //   },
      //   {
      //     type: '分析',
      //     rowSpan: '10',
      //     indicateId: '931',
      //     indicateName: '家宽满意度模型待修复客户数',
      //     currentValue: null,
      //     lastRatio: null,
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: '分析',
      //     rowSpan: '',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: '分析',
      //     rowSpan: '',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: '分析',
      //     rowSpan: '',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: '分析',
      //     rowSpan: '',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: '分析',
      //     rowSpan: '',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: '分析',
      //     rowSpan: '',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: '分析',
      //     rowSpan: '',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: '分析',
      //     rowSpan: '',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: '分析',
      //     rowSpan: '',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      //   {
      //     type: 'dfdhfdsa',
      //     rowSpan: '',
      //     indicateId: '938',
      //     indicateName: '2G覆盖不满意用户占比',
      //     currentValue: '50.8443',
      //     lastRatio: '12.38',
      //     lastYearRatio: null,
      //   },
      // ];
    },
  },
  watch:{
      tableData(val){
          this.topParentIdList =  this.parentData.topParentIdList;
      }
  },
  mounted() {
    const { topParentIdList } = this.parentData;
    this.topParentIdList = topParentIdList;
  },
};
</script>

<style>

</style>
