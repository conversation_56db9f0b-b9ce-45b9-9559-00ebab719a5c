<template>
  <div class="date-picker">
    <div class="date-picker-types">
      <span>选择时间：</span>
      <el-select @change="handleDateChange('type')" size="small" v-model="dateType" value-key="label">
        <el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in dateTypeList" />
      </el-select>
    </div>
    <!-- 季度组件 -->
    <date-quarter @change="handleDateChange('time')" v-if="dateType=='quarter'" v-model="dateObj.quarterdateValue" />
    <!-- < 年月日组件 -->
    <el-date-picker
      :clearable="clearble"
      :format="format[dateType]"
      :key="dateType"
      :picker-options="pickerOptions"
      :type="type"
      @change="handleDateChange('time')"
      size="small"
      v-else
      v-model="dateObj[`${dateType}dateValue`]"
    />
  </div>
</template>

<script>
import DateQuarter from "./DateQuarters.vue";
import Common from "bj_src/lib/date";
import { getLatestStatDate } from '@/api/customer'

export default {
  name: "newDatePicker",
  components: { DateQuarter },
  props: {
    defaultType: {
      type: String,
      default: "month"
    },
    dateTypes: {
      type: Array,
      default: () => ['日', '周', '月', '季度', '日累计']
    },
    tableName: {
      type: String,
      default: ''
    },
    format: {
      type: Object,
      default: () => ({
        date: "yyyy-MM-dd",
        month: "yyyy年MM月",
        year: "yyyy年",
        quarter: "yyyy年Q季度",
        daterange: "yyyy-MM-dd",
        daycumulative: 'yyyy-MM-dd'
      })
    },
    valueFormat: {
      type: Object,
      default: () => ({
        date: "yyyy-MM-dd",
        month: "yyyy-MM",
        year: "yyyy",
        quarter: "yyyy-MM",
        daterange: "yyyy-MM-dd",
        daycumulative: 'yyyy-MM-dd'
      })
    },
    clearble: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dateTypeList() {
      const dateTypeLists = [
        { label: "日", value: "date" },
        { label: "日", value: "daterange" },
        { label: "周", value: "week" },
        { label: "月", value: "month" },
        { label: "季度", value: "quarter" },
        { label: "日累计", value: "daycumulative" },
      ]
      // 将dateTypes和dateTypeLists的value匹配
      return dateTypeLists.filter(({ value }) => this.dateTypes.includes(value));
    },

    type(){
      if (this.dateType == 'date'||this.dateType == 'daycumulative') {
        return 'date'
      }else if (this.dateType == 'daterange') {
        return 'daterange'
      }else if (this.dateType == 'week') {
        return 'week'
      }else if (this.dateType =='month') {
        return'month'
      }else if (this.dateType == 'quarter') {
        return 'quarter'
      }
    }
  },
  data() {
    const day30 = 30 * 24 * 3600 * 1000;
    return {
      // daterange的pickerOptions，选择时间范围补超过30天
      pickerMinDate: null,
      pickerMaxdate: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && this.pickerMinDate) {
            this.pickerMinDate = null;
          } else if (minDate) {
            this.pickerMinDate = minDate.getTime();
          }
        },
        disabledDate: time => {
          if (this.pickerMinDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() > this.pickerMinDate + day30 ||
              time.getTime() < this.pickerMinDate - day30
            );
          }
          return time.getTime() > Date.now();
        }
      },
      dateType: this.defaultType || 'date',
      dateValue: '',//后台传到默认时间
      dateObj: {},
    };
  },
  created() {
    // dateType匹配defaultType
    this.dateType = this.dateTypeList.find(item => item.value === this.defaultType).value || 'date'
    this.handleDateChange('type');
  },
  methods: {
    /**
     * @description 获取有数据的最新日期
     * @param
     * <AUTHOR>
     * @date 2023-12-11
     */
    async queryLatestDate() {
      // 1-date/daterange,2-week,3-mon,4-季度，5-日累计
      const { data, code } = await getLatestStatDate({
        statType: (() => {
          switch (this.dateType) {
            case 'date':
            case 'daterange':
              return '1';
            case 'week':
              return '2';
            case 'month':
              return '3';
            case 'quarter':
              return '4';
            case 'daycumulative':
              return '5';
            default:
              return '';
          }
        })(),
        tableName: this.tableName
      });
      if (code == 200 && data) {
        if (data.length > 0) {
          this.dateValue = data[0]
        }
      }
    },
    /**
     * @description 初始化日期数据
     * @param
     * <AUTHOR>
     * @date 2023-12-11
     */
    async initDateValues() {
      await this.queryLatestDate()
      if (this.dateType === 'quarter') {
        const result = this.dateValue.replace(/-Q(\d)/, '-0$1');
        this.$set(this.dateObj, `${this.dateType}dateValue`, result);
      } else if (this.dateType.indexOf("range") > -1) {
        const start = new Date(this.dateValue);
        // 提前14天
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 14);
        this.$set(this.dateObj, `${this.dateType}dateValue`, [start, new Date(this.dateValue)]);
      } else {
        this.$set(this.dateObj, `${this.dateType}dateValue`, new Date(this.dateValue));
      }
    },
    async handleDateChange(type) {
      const label = this.dateTypeList.find(item => item.value === this.dateType).label;
      if (type === 'type') {
        await this.initDateValues()
      }
      if (this.dateType == "quarter") {
        this.$emit("change", [label, this.dateObj[`${this.dateType}dateValue`]]);
      } else if (this.dateType.indexOf("range") > -1) {
        this.$emit("change", [label, this.dateObj[`${this.dateType}dateValue`].map(item => {
          return Common.formatDate(item, this.valueFormat[this.dateType]);
        })
        ]);
      } else {
        this.$emit("change", [label, Common.formatDate(this.dateObj[`${this.dateType}dateValue`], this.valueFormat[this.dateType])]);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.date-picker {
  display: flex;
  span {
    color: #262626;
    font-size: 14px;
  }
  .el-select {
    width: 100px;
    margin-right: 5px;
  }
  /deep/ .el-date-editor {
    &:not(.el-range-editor) {
      width: 160px;
    }
    &.el-range-editor {
      width: 240px;
    }
  }
}
</style>
