<template>
  <div class="TrendPage" v-loading="loading">
    <Title :title='parentData.partName'></Title>
    <CommonTable :parentData='parentData'></CommonTable>
    <chartsTitle></chartsTitle>
    <el-row :gutter="20">
      <el-col :span="8" v-for="item in parentData.trendVoList" :key="item.indicateId">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>{{item.indicateName}}</span>
          </div>
          <div class="box-card-content">
            <trend-bar-line
              :barData="item"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Vue from 'vue';
import {
  Card, Row, Col,
} from 'element-ui';
import TrendBarLine from '../components/common/TrendBarLine.vue';
import CommonTable from '../components/common/commonTable.vue';
import Title from '../components/common/Title.vue';
import chartsTitle from '../components/common/chartsTitle.vue';

Vue.use(Card)
  .use(Row)
  .use(Col);
export default {
  name: 'TrendBarTable',
  components: {
    TrendBarLine,
    CommonTable,
    Title,
    chartsTitle,
  },
  props: {
    parentData: {
      type: Object,
      default: () => ({
        tableVoList: [],
        trendVoList: [],
      }),
    },
    data: {
      type: Array,
      default: () => [
        {
          indicatName: '移动装机/移机满意度',
          competitiveFigureId: '11',
          idOperator: {
            127: '电信',
            18: '移动',
            128: '联通',
          },
          nameList: ['201905', '201904', '201903'],
          mobileList: ['37141', '108731', '22466'],
          unicomList: ['78925', '49357', '52759'],
          telecomList: ['12952', '73485', '24141'],
        },
      ],
    },
    barnumIsShow: {
      type: Boolean,
      default: () => false,
    },
  },
  computed: {
    filtData() {
      console.log(this.data);
      const resultList = [];
      this.data.forEach((it) => {
        resultList.push({
          title: it.indicatName,
          id: it.indicatId,
          xAxis: it.nameList,
          barData: {
            name: '完成值',
            data: it.newVList,
          },
          lineData: {
            name: '去年值',
            data: it.oldVList,
          },
          targetData: {
            name: '目标值',
            data: it.targetList,
          },
        });
      });
      return resultList;
    },
  },
  data() {
    return {
      value: '',
      loading: false,
    };
  },
  mounted() {

  },
  methods: {},
  watch: {},
};
</script>

<style lang='less' scoped>
.TrendPage {
  position: relative;
  .box-card {
    text-align: left;
    margin-bottom: 20px;
    .el-card__header {
      background-color: rgba(87, 156, 255, 0.05);
      padding: 10px 20px;
      font-size: 15px;
      border-bottom: 0px;
      font-size: 16px;
    }
    .el-card__body {
      padding: 10px;
    }
    .box-card-content {
      position: relative;
      height: 15vw;
    }
  }
}
</style>
<style lang="less">
  
.TrendPage .el-card {
    border:0px;
}
</style>