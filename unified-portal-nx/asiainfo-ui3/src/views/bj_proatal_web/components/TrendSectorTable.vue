<template>
  <div class="TrendPage" v-loading="loading">
  <Title :title='parentData?parentData.partName:""'></Title>
  <sectorTable :parentData='parentData'></sectorTable>
  <chartsTitle></chartsTitle>
  <template>
    <el-tabs v-model="activeName">
        <el-tab-pane label="饼状图" name="sector"></el-tab-pane>
        <el-tab-pane label="柱状图" name="bar"></el-tab-pane>
    </el-tabs>
  </template>
    <el-row :gutter="20" v-if='sectorShow'>
      <el-col :span="8" v-for="(item,index) in sectorData" :key="'elcol'+index">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>{{item.indicateName}}</span>
          </div>
          <div class="box-card-content">
            <trend-sector
              :legendData='legendData'
              :sectorData='item.data'
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" v-if='!sectorShow'>
      <el-col :span="8" v-for="(item,index) in barData" :key="'barData'+index">
        <el-card class="box-card" shadow="never">
          <div slot="header" class="clearfix">
            <span>{{item.indicateName}}</span>
          </div>
          <div class="box-card-content">
            <sectorBar
              :barData='item'
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Vue from 'vue';
import {
  Card, Row, Col, Tabs, TabPane,
} from 'element-ui';
import sectorTable from '../components/common/sectorTable.vue';
import TrendSector from '../components/common/TrendSector.vue';
import sectorBar from '../components/common/sectorBar.vue';
import Title from '../components/common/Title.vue';
import chartsTitle from '../components/common/chartsTitle.vue';

Vue.use(Card)
  .use(Row)
  .use(TabPane)
  .use(Tabs)
  .use(Col);
export default {
  name: 'TrendSectorTable',
  components: {
    TrendSector,
    sectorTable,
    sectorBar,
    Title,
    chartsTitle,
  },
  props: {
    parentData: {
      type: Object,
      default: () => {},
    },
    barnumIsShow: {
      type: Boolean,
      default: () => false,
    },
  },
  computed: {
    sectorData() {
      let listItem = {
        indicateName: '',
        data: [
          { value: 50, name: 'A级' },
          { value: 40, name: 'B级' },
          { value: 30, name: 'C级' },
          { value: 20, name: 'D级' },
        ],
      };
      let resultList = [];
      if (this.parentData !== undefined) {
        const { specialVoList } = this.parentData;
        specialVoList.forEach((item) => {
          listItem = JSON.parse(JSON.stringify(listItem));
          resultList = JSON.parse(JSON.stringify(resultList));
          item = JSON.parse(JSON.stringify(item));
          let { indicateName } = item;
          indicateName = indicateName.replace('[', '').replace(']', '');
          listItem.indicateName = indicateName;
          listItem.data[0].value = parseFloat(item.firstValue);
          listItem.data[1].value = parseFloat(item.secondValue);
          listItem.data[2].value = parseFloat(item.thirdValue);
          listItem.data[3].value = parseFloat(item.fourthValue);
          resultList.push(listItem);
        });
      }
      return resultList;
    },
    barData() {
      let barItem = {
        indicateName: '',
        xAxis: this.legendData,
        data: [],
      };
      let barList = [];
      if (this.parentData !== undefined) {
        const { specialVoList } = this.parentData;
        specialVoList.forEach((item) => {
          barItem = JSON.parse(JSON.stringify(barItem));
          barList = JSON.parse(JSON.stringify(barList));
          let { indicateName } = item;
          indicateName = indicateName.replace('[', '').replace(']', '');
          barItem.indicateName = indicateName;
          barItem.data[0] = parseFloat(item.firstValue);
          barItem.data[1] = parseFloat(item.secondValue);
          barItem.data[2] = parseFloat(item.thirdValue);
          barItem.data[3] = parseFloat(item.fourthValue);
          barList.push(barItem);
        });
      }
      return barList;
    },
  },
  data() {
    return {
      loading: false,
      legendData: ['A级', 'B级', 'C级', 'D级'],
      sectorShow: true,
      activeName: 'sector',
    };
  },
  mounted() {

  },
  methods: {},
  watch: {
    activeName() {
      this.sectorShow = !this.sectorShow;
    },
  },
};
</script>

<style lang='less' scoped>
.TrendPage {
  position: relative;
  .box-card {
    text-align: left;
    margin-bottom: 20px;
    .el-card__header {
      background-color: rgba(87, 156, 255, 0.05);
      padding: 10px 20px;
      font-size: 15px;
    }
    .el-card__body {
      padding: 10px;
    }
    .box-card-content {
      position: relative;
      height: 15vw;
    }
  }
}
</style>

<style lang='less'>
.TrendPage {
  .el-tabs__item {
    font-weight: normal;
    height: 30px;
    font-family: SourceHanSansSC-Regular;
    font-size: 14px;
    color: #595959;
  }
  .el-tabs__active-bar {
    background: #FFB03A;
    height: 0px;
    bottom: 5px;
    -box-shadow: 3px 3px 5px rgba(81, 139, 255, 0.5);
  }
  .el-tabs__item.is-active {
    color: #262626;
    font-family: SourceHanSansSC-Medium;
  }

}
  
</style>
