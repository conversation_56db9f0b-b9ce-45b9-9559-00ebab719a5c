<template>
    <div class="experience">
        <h3 class="chartsTitle">
            <span>体验历程类指标</span>
        </h3>
        <!--<el-row :gutter="20" style="margin-left: -10px; margin-right: -10px;">
          <el-col :span="4">
            <div class="grid-content bg-purple1" @click.stop="toExperienceCourse">
              <div>
                <img src="../assets/img/experience/person.png" />
              </div>
              <div class="experience-text">
                <span>个人业务(C)</span>
              </div>
            </div>
          </el-col>
          <el-col :span="1">
            <span>&nbsp;</span>
          </el-col>
          <el-col :span="4">
            <div class="grid-content bg-purple2" @click.stop="toExperienceCourse">
              <div>
                <img src="../assets/img/experience/family.png" />
              </div>
              <div class="experience-text">家庭业务(H)</div>
            </div>
          </el-col>
          <el-col :span="1">&nbsp;</el-col>
          <el-col :span="4">
            <div class="grid-content bg-purple3" @click.stop="toExperienceCourse">
              <div>
                <img src="../assets/img/experience/government.png" />
              </div>
              <div class="experience-text">政企业务(B)</div>
            </div>
          </el-col>
          <el-col :span="1">&nbsp;</el-col>
          <el-col :span="4">
            <div class="grid-content bg-purple4" @click.stop="toExperienceCourse">
              <div>
                <img src="../assets/img/experience/new.png" />
              </div>
              <div class="experience-text">新兴业务(N)</div>
            </div>
          </el-col>
          <el-col :span="1">&nbsp;</el-col>
          <el-col :span="4">
            <div class="grid-content bg-purple5" @click.stop="toExperienceCourse">
              <div>
                <img src="../assets/img/experience/channel.png" />
              </div>
              <div class="experience-text">渠道</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="grid-content bg-purple6" @click.stop="toExperienceCourse">
              <div>
                <img src="../assets/img/experience/web.png" />
              </div>
              <div class="experience-text">网络</div>
            </div>
          </el-col>
          <el-col :span="1">&nbsp;</el-col>
          <el-col :span="4">
            <div class="grid-content bg-purple7" @click.stop="toExperienceCourse">
              <div>
                <img src="../assets/img/experience/other.png" />
              </div>
              <div class="experience-text">其他</div>
            </div>
          </el-col>
        </el-row>-->
        <div class="exp-menu-main">
            <template v-for="(item,i) in dataMenu">
                <div class="grid-content"
                     @click.stop="toExperienceCourse(item,i)">  <!--:class="'bg-purple'+(i+1)"-->
                    <div class="grid-top" :class="'back-t-'+(i%7)">   <!--颜色和图标循环展示-->
                        <div class="grid-icon">
                            <img :src="item.icon" />
                        </div>
                        <div class="experience-text">
                            <span>{{item.name}}</span>
                        </div>
                    </div>
                    <div class="grid-bottom" :class="'back-b-'+(i%7)"></div>
                </div>
            </template>
        </div>
    </div>
</template>
<script>
    import Vue from "vue";
    import { Card, Row, Col } from "element-ui";

    const  iconList = {
        'icon0':require('../assets/img/experience/person.png'),
        'icon1':require('../assets/img/experience/family.png'),
        'icon2':require('../assets/img/experience/government.png'),
        'icon3':require('../assets/img/experience/new.png'),
        'icon4':require('../assets/img/experience/channel.png'),
        'icon5':require('../assets/img/experience/web.png'),
        'icon6':require('../assets/img/experience/other.png'),
    };
    const dateMap = {
        1: '日',
        2: '周',
        3: '月',
        4: '季',
        5: '年',
        6: '期'
    };
    Vue.use(Card)
        .use(Row)
        .use(Col);
    export default {
        name: "experience",
        components: {},
        props: {},
        computed: {},
        data() {
            return {
                dataMenu:[],
            };
        },
        created(){
            this.initMenu();
        },
        mounted() {},
        methods: {
            initMenu(){
                const _this=this;
                _this.dataMenu=[];
                _this.$http
                    .post('/experience-service/experience/getMain',{})
                    .then((res) => {
                        const data=res.data;
                        data.forEach(function (e,i) {
                            const iconKey='icon'+i;
                            _this.dataMenu.push({
                                name:e.mainServiceName,
                                icon:iconList['icon'+i],
                                indicateCycle:e.indicateCycle,
                            })
                        })

                    }).catch((res) =>{
                })
            },
            //跳转体验历程类
            toExperienceCourse(item,i){
                const timeType = [];
                item.indicateCycle.split(',').forEach((it) => {
                    if (dateMap[it]) {
                        timeType.push(dateMap[it]);
                    }
                });
                console.log('___555',timeType);
                this.$router.push({
                    name:'experienceCourse',
                });
                this.$store.commit('setShowHeader',0);
                this.$store.commit('setDateCycle',timeType);
                this.$store.commit('setExperienceType',{
                    /*experienceTypeName:item.name.substring(0,item.name.indexOf('(')),*/
                    experienceTypeName:item.name,
                    experienceTypeCode:item.code,
                    experienceTypeIndex:i,
                })
            }
        },
        watch: {}
    };
</script>

<style lang='less' scoped>
    /*.el-row {
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    .el-col {
      border-radius: 4px;
    }
    .bg-purple-dark {
      background: #99a9bf;
    }*/
    .exp-menu-main{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }
    /*.bg-purple1 {
      background: url("../assets/img/experience/personbg.png") no-repeat;
      background-size: contain;
      background-position: center;
      height: auto;
    }

    .bg-purple2 {
      background: url("../assets/img/experience/familybg.png") no-repeat;
      background-size: contain;
      background-position: center;
      height: auto;
    }

    .bg-purple3 {
      background: url("../assets/img/experience/governmentbg.png") no-repeat;
      background-size: contain;
      background-position: center;
      height: auto;
    }

    .bg-purple4 {
      background: url("../assets/img/experience/newbg.png") no-repeat;
      background-size: contain;
      background-position: center;
      height: auto;
    }

    .bg-purple5 {
      background: url("../assets/img/experience/channelbg.png") no-repeat;
      background-size: contain;
      background-position: center;
      height: auto;
    }

    .bg-purple6 {
      background: url("../assets/img/experience/webbg.png") no-repeat;
      background-size: contain;
      background-position: center;
      height: auto;
    }

    .bg-purple7 {
      background: url("../assets/img/experience/otherbg.png") no-repeat;
      background-size: contain;
      background-position: center;
      height: auto;
    }*/

    /*.bg-purple-light {
      background: #e5e9f2;
    }*/
    .grid-content {
        /*border-radius: 4px;*/
        /*padding: 20px 0;*/
        width: 230px;
        /*height: 95px;*/
        margin-right: 20px;
        padding-top: 30px;
        /*display: flex;
        flex-direction: column;
        align-items: center;*/
        float: left;
        .grid-top{
            width: 100%;
            height: 90px;
            border-radius: 4px;
            /*display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;*/
            .grid-icon{
                width: 230px;
                height: 54px;
                display: table-cell;
                vertical-align: middle;
                text-align: center;
            }

        }
        .grid-bottom{
            width: 90%;
            height: 7px;
            border-bottom-right-radius: 4px;
            border-bottom-left-radius: 4px;
            opacity: 0.4;
            margin-left: 5%;
        }
    }
    .back-t-0{
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#5EE7E2, endColorstr=#27C8E4);
        background-image: linear-gradient(-179deg, #5EE7E2 0%, #27C8E4 97%);
    }
    .back-b-0{
        background: #2CCBE5;
    }
    .back-t-1{
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#55D0FF, endColorstr=#2AB2F2);
        background-image: linear-gradient(-179deg, #55D0FF 1%, #2AB2F2 100%);
    }
    .back-b-1{
        background: #2CB3F2;
    }
    .back-t-2{
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#52E2B2, endColorstr=#18C7A8);
        background-image: linear-gradient(-179deg, #52E2B2 5%, #18C7A8 100%);
    }
    .back-b-2{
        background: #1ECAA9;
    }
    .back-t-3{
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#63B7FF, endColorstr=#5791F6);
        background-image: linear-gradient(-179deg, #63B7FF 0%, #5791F6 100%);
    }
    .back-b-3{
        background: #6DA0F8;
    }
    .back-t-4{
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#BDAFF7, endColorstr=#968DF5);
        background-image: linear-gradient(-179deg, #BDAFF7 1%, #968DF5 100%);
    }
    .back-b-4{
        background: #9B91F5;
    }
    .back-t-5{
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#8FAFF7, endColorstr=#698FF2);
        background-image: linear-gradient(-179deg, #8FAFF7 2%, #698FF2 100%);
    }
    .back-b-5{
        background: #7D9EF2;
    }
    .back-t-6{
        filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#64D5F8, endColorstr=#1CC2F5);
        background-image: linear-gradient(-179deg, #64D5F8 0%, #1CC2F5 98%);
    }
    .back-b-6{
        background: #26C6F4;
    }
    .row-bg {
        padding: 10px 0;
        background-color: #f9fafc;
    }

    .experience {
        padding: 20px 4%;
        //min-height: 30vw;
        position: relative;
    }

    .experience-text {
        font-family: SourceHanSansSC-Medium;
        //font-size: 18px;
        font-size: 18px;
        color: #ffffff;
        letter-spacing: -0.68px;
        text-align: center;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.16);
        height: 30%;
    }

    .chartsTitle {
        span {
            display: inline-block;
            background: rgb(223, 237, 255);
            color: #1e85fe;
            padding: 0px 10px;
            padding-top: 2px;
            box-sizing: border-box;
        }
        margin: 20px 0 10px;
        text-align: left;
        border-bottom: 3px solid rgb(223, 237, 255);
        line-height: 30px;
        color: rgb(75, 78, 84);
        font-size: 14px;
        font-weight: 500;
    }

    .grid-content :hover{
        cursor: pointer;
        /*box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);*/
    }
</style>
