<template>
  <div class="week-report">
    <div class="report-header">
      <el-select v-model="weekValue" style="margin-right:10px;width:320px !important;" placeholder="请选择">
        <el-option
          v-for="item in weekOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button type="primary" size="small" @click="checkDate">查询</el-button>
      <el-button type="default" size="small" @click="reset">重置</el-button>
    </div>

    <div class="report-content">
      <p style="font-size:18px;">触点服务感知周报</p>

      <el-table
        v-loading="loading"
        border
        :span-method="objectSpanMethod"
        :cell-class-name="formatterCellClass"
        :header-cell-class-name="formatterCellClass"
        :data="tableData"
      >
        <el-table-column
          label=""
          prop="firstType"
          align="center"
        />

        <el-table-column
          label=""
          prop="secondType"
          align="center"
        />

        <el-table-column
          label="考核指标"
          prop="targetName"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.targetName">{{ scope.row.targetName }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          v-for="(item,index) in columns"
          :key="item.id"
          :label="`第${item.date}周`"
          :prop="item.date"
          :formatter="formatCol"
          align="center"
        >
          <template slot-scope="scope">
            <div v-if="scope.row[item.date]" style="display:inline-block;width:45px"> {{ Number(scope.row[item.date]).toFixed2(2) }} </div>
            <div v-else width="60px" style="display:inline-block;width:45px"> - </div>
            <span v-if="(scope.row.scoreType == '0' || scope.row.scoreType == '1') && index == columns.length-1 && index != 0">
              <img :src="scope.row.scoreType == '0'?upImg:downImg" width="12" style="margin-left:15px;">
            </span>
          </template>
        </el-table-column>

      </el-table>
    </div>
  </div>
</template>
<script>
import { getUniformedWarning } from '../../../../api/teleservice/api'

export default {
  name: 'WeekReport',
  components: {
  },
  props: {

  },

  data() {
    return {
      weekOptions: [],
      weekValue: '',
      tableData: [],
      nowWeek: '',
      columns: '',
      loading: false,
      upImg: require('@/assets/images/up-icon.png'),
      downImg: require('@/assets/images/down-icon.png')
    }
  },
  computed: {

  },
  watch: {
    // weekValue(){
    //   this.initWeek()
    // }

  },
  mounted() {
    this.initDate()
  },
  methods: {
    formatterCellClass({ row, column, rowIndex, columnIndex }) {
      if (columnIndex > 2) {
        return 'border-right'
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        if (row.colNum0 || row.colNum1) {
          return {
            rowspan: columnIndex === 0 ? row.colNum0 : row.colNum1,
            colspan: 1
          }
        } else {
          return {
            rowspan: 1,
            colspan: 0
          }
        }
      }
    },
    formatCol(row, column, cellValue, index) {
      if (cellValue == undefined) {
        return '-'
      } else {
        return cellValue
      }
    },
    async initDate() {
      // 获取年周下拉框值
      const { data } = await getUniformedWarning({
        restSerialNo: 'group-view-allyweeklist'
      })
      const arr = []
      data.data.forEach(item => {
        const y = item.start_time.slice(0, 4)
        const sTime = item.start_time.replace('-', '/').replace('-', '/')
        const eTime = item.end_time.replace('-', '/').replace('-', '/')
        const label = `${y}年第${item.stat_date}周  (${sTime} - ${eTime})`
        const i = {
          label,
          value: item.start_time
        }
        arr.push(i)
      })
      this.weekOptions = arr
      this.weekValue = arr[0].value
      this.initWeek()
    },
    async initWeek() {
      this.loading = true
      const { data } = await getUniformedWarning({
        restSerialNo: 'group-view-week',
        opTime: this.weekValue
      })
      const arr = []
      const columns = []
      const dates = []
      data.children.forEach(item1 => {
        let columns0 = 0
        if (item1.children && item1.children.length == 0) {
          columns0 += 1
        }
        item1.children.forEach(child => {
          columns0 += child.children.length
          if (child.children && child.children.length == 0) {
            columns0 += 1
          }
        })
        item1.children.forEach((item2, idx2) => {
          // 初始化数据，colNum0 != 0 时合并第一列
          let item = {
            colNum0: idx2 == 0 ? columns0 : 0,
            firstType: item1.name,
            secondType: item2.name,
            targetId: item2.code
          }
          if (item2.children.length !== 0) {
            item2.children.forEach((item3, idx3) => {
              const itemDates = []
              // columns为返回的数据列数，根据columns的值渲染几列数据
              item3.trend.forEach(trend => {
                if (!dates.includes(trend.stat_date)) {
                  const element = {
                    date: trend.stat_date,
                    id: parseInt(Math.random() * 100)
                  }
                  columns.push(element)
                  dates.push(trend.stat_date)
                }
                itemDates.push(trend.stat_date)
              })
              // 初始化数据，colNum1 != 0 时合并列第二列
              item = {
                colNum0: idx2 == 0 && idx3 == 0 ? columns0 : 0,
                firstType: item1.name,
                secondType: item2.name,
                colNum1: idx3 == 0 ? item2.children.length : 0,
                targetName: item3.target_name,
                targetId: item3.target_id,
                // avgScore : item3.avg_score ,
                scoreType: item3.score_type
              }
              itemDates.forEach((date, idx) => {
                item[date] = item3.trend[idx].score
              })
              arr.push(item)
            })
          } else {
            item.colNum1 = 1
            arr.push(item)
          }
        })
      })
      this.columns = columns
      this.tableData = arr
      this.loading = false
    },
    // 点击查询
    checkDate() {
      this.initWeek()
    },
    // 重置
    reset() {
      this.weekValue = this.weekOptions[0].value
      this.initWeek()
    }

  }
}
</script>

<style lang='less'>
  .week-report {

    .report-header {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin: 20px 0;

      h4 {
        -color: #ffa51f ;
        font-size: 16px;
        font-family: SourceHanSansSC-Normal;
        font-weight: normal;
      }
    }

    .report-content {
      width: 100%;
      background: #fff;
      padding: 24px;
      box-sizing: border-box;
    }
  }
  .border-right {
    border-right: none !important;
  }

</style>
