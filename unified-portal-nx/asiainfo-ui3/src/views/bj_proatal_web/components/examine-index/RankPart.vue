<template>
  <div class="conten-box">
    <div v-for="(item,index) in data" :key="index" class="main-content">
      <div class="data-title next-page" @click="moreDetail(item)">
        {{ item.targetname }}
      </div>
      <div class="data-cotent">
        <div v-if="item.score != '-'" class="score">{{ Number(item.score).toFixed2(2) }}</div>
        <div v-else class="score">{{ item.score }}</div>
        <div class="rank" style="font-size:14px;color:rgb(51, 51, 51)">
          <div class="rank-num">
            排名：{{ item.rank }}
          </div>
          <div v-if="item.ranktype == '0' || item.ranktype == '1'" class="rank-type">
            <img :src="item.ranktype == '0'?upImg:downImg" width="12" style="margin-right:6px;">
            {{ item.rankdiff }}
          </div>
          <span v-if="item.rankdiff === '0'" class="rank-type" style="margin-right: 6px; margin-left: 6px;">
            <i class="el-icon-minus" style="color:rgb(236, 189, 44);font-size:22px;" />
            {{ item.rankdiff }}
          </span>
          <span v-if="item.rankdiff === '-'" class="rank-type" style="margin-right: 6px; margin-left: 6px;">
            {{ item.rankdiff }}
          </span>

        </div>
        <div class="momrate" style="font-size:14px;color:rgb(51, 51, 51)">环比：
          <div v-if="item.momratetype == '0' || item.momratetype == '1'" class="momrate-type">
            <img :src="item.momratetype == '0'?upImg:downImg" width="12" style="margin-right:6px;">
            {{ Number(item.momrate).toFixed2(2) }}pp
          </div>
          <div v-if="item.momrate == '-'" class="momrate-type">
            {{ item.momrate }}
          </div>
        </div>
      </div>
      <div class="child-data">
        <div v-for="(element,id) in item.trend" :key="id" class="child-list">
          <div class="list-head">
            <div class="line" />
            <div class="list-name next-page" @click="moreDetail(element)">{{ element.targetname }}</div>
          </div>
          <div class="rank" style="font-size:13px;color:rgb(93, 93, 93)">
            <div class="rank-num">
              排名：{{ element.rank }}
            </div>
            <div v-if="element.rankType == '0' || element.rankType == '1'" class="rank-type">
              <img :src="element.rankType == '0'?upImg:downImg" width="12" style="margin-right:6px;">
              {{ element.rankdiff }}
            </div>
            <span v-if="element.rankdiff === '0'" class="rank-type" style="margin-right: 6px; margin-left: 6px;">
              <i class="el-icon-minus" style="color:rgb(253, 182, 77);font-size:35px;" />
              {{ element.rankdiff }}
            </span>
            <span v-if="element.rankdiff === '-'" class="rank-type" style="margin-right: 6px; margin-left: 6px;">
              {{ element.rankdiff }}
            </span>

          </div>
          <div class="momrate" style="font-size:13px;color:rgb(93, 93, 93)">环比：
            <div v-if="element.momrateType == '0' || element.momrateType == '1'" class="momrate-type">
              <img :src="element.momrateType == '0'?upImg:downImg" width="12" style="margin-right:6px;">
              {{ Number(element.momrate).toFixed2(2) }}pp
            </div>
            <div v-if="item.momrate == '-'" class="momrate-type">
              {{ element.momrate }}
            </div>
          </div>

        </div>

      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'RankPart',
  components: {
  },
  props: {
    data: Array

  },

  data() {
    return {
      upImg: require('@/assets/images/up-icon.png'),
      downImg: require('@/assets/images/down-icon.png')
    }
  },
  computed: {

  },
  watch: {},
  mounted() {},
  methods: {

    // 下钻
    moreDetail(i) {
      console.log(i)
      i.level = '服务分项情况'
      sessionStorage.setItem('menuUrl', '/examine-index/serveDetail')
      localStorage.setItem('detailItem', JSON.stringify(i))
      this.$router.push({
        name: 'ServeDetail',
        params: i
      })
    }

  }
}
</script>

<style lang='less'>
 .main-content{
  margin-top: 10px;
  // background-color: pink;
  padding: 10px 20px 10px 20px;
  .next-page{
    font-size: 15px;
  }
  .next-page:hover{
    color: #ff9900;
    cursor: pointer;
  }
  .data-cotent{
    display: flex;
    justify-content: space-between;
    vertical-align: bottom;
    .score{
      width: 25%;
      font-size: 22px;
      color: black;
    }
    .rank,.momrate{
      width: 35%;
      line-height: 2;
      color: rgb(93, 93, 93);
    }
    .rank{
      display: flex;
      justify-content: space-between;
      .rank-num{
        width: 45%;
      }
      .rank-type{
        width: 45%;
        display: inline-block;

      }
    }
    .momrate{
      .momrate-type{
        display: inline-block;

      }
    }
  }

  .child-data{
    .child-list{
      display: flex;
      justify-content: space-around;
      // margin-left: 20px;
      padding: 5px 0 5px 20px;
      .list-head{
        width: 20%;
        div{
          display: inline-block;
        }
        .line{
          width: 3px;
          height: 14px;
          margin-right: 10px;
          background: #ff9900;
        }
        .list-name{
          font-size: 14px;
        }
      }
      .rank,.momrate{
        width: 30%;
        line-height: 2;
        color: rgb(93, 93, 93);
      }
      .rank{
        display: flex;
        justify-content: space-between;
        .rank-num{
          width: 45%;
        }
        .rank-type{
          width: 45%;
          display: inline-block;
        }
      }
      .momrate{
        .momrate-type{
          display: inline-block;
        }
      }

    }
  }

}
.main-content:hover{
  background-color:rgb(245, 245, 245);
}

</style>
