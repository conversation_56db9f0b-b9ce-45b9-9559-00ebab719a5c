<template>
  <div class="month-serve">  
      <div class="nav-box" >
          <div style="width: 20%; height:100%;" >
              <div>
                  <ul class="SubTitle">
                      <li 
                      v-for="item of navMenu" 
                      :key="item.value" 
                      :class="item.value===current ? 'active' : ''" 
                      @click="toggleNav(item)" >
                          {{item.menuName}}
                      </li>
                  </ul>
              </div> 
          </div>
          <div style="width: 80%; height:100%;">

            <div class="content" v-if="current == 1">
              
              <div class="content-type">
                <div class="content-head">
                  <div class="content-title">
                    手机客户（用后即评）
                  </div>
                  <el-select :class="timeType == 1 ? 'year-type' : 'week-type'" style="float:right" v-model="phoneTimeValue" placeholder="请选择">
                    <el-option
                      v-for="item in phoneTimeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
                <rank-part v-loading="phoneLoading" :data="phoneList" />    
              </div>

              <div class="content-type">
                <div class="content-head">
                  <div class="content-title">
                    家庭客户（用后即评）
                  </div>
                  <el-select :class="timeType == 1 ? 'year-type' : 'week-type'" style="float:right" v-model="familyTimeValue" placeholder="请选择">
                    <el-option
                      v-for="item in familyTimeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
                <rank-part v-loading="familyLoading" :data="familyList" />    

              </div>
              
            </div>

            <div class="content" v-else>
              <div class="content-type">
                <div class="content-head">
                  <div class="content-title">
                    资费套餐（用后即评）
                  </div>
                  <el-select :class="timeType == 1 ? 'year-type' : 'week-type'" style="float:right" v-model="feeTimeValue" placeholder="请选择">
                    <el-option
                      v-for="item in feeTimeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
                <rank-part v-loading="feeLoading" :data="feeList" />    
              </div>

              <div class="content-type">
                <div class="content-head">
                  <div class="content-title">
                    触点质量（用后即评）
                  </div>
                  <el-select :class="timeType == 1 ? 'year-type' : 'week-type'" style="float:right" v-model="economyTimeValue" placeholder="请选择">
                    <el-option
                      v-for="item in economyTimeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </div>
                <rank-part v-loading="economyLoading" :data="economyList" />    

              </div>
              
            </div>
             
            </div>  
      </div>

  </div>
</template>
<script>
import { getServeData } from '../../../../api/teleservice/api'
import RankPart from '../../components/examine-index/RankPart';

export default {
  name: 'MonthServe',
  components: {
    RankPart,
  },
  props: {
    timeType:String

  },
  inject:['changeScrollTop'],
 
  data() {
    return {
      upImg: require('@/assets/images/up-icon.png'),
      downImg: require('@/assets/images/down-icon.png'),
      navMenu:[{
        menuName:'满意度',
        value:1
      },{
        menuName:'质量提升',
        value:2
      }],
      current:1,
      phoneTimeOptions: [],
      familyTimeOptions: [],
      feeTimeOptions: [],
      economyTimeOptions: [],
      phoneTimeValue:'',
      familyTimeValue:'',
      feeTimeValue:'',
      economyTimeValue:'',
      phoneList:[],
      familyList:[],
      feeList:[],
      economyList:[],
      phoneLoading:false,
      familyLoading:false,
      feeLoading:false,
      economyLoading:false,

    };
  },
  computed: {

  },
  mounted() {
    this.initDate()
  },
  methods: {
    toggleNav(i){
      this.current = i.value
    },
    async initDate(){
      const data1 = await this.getDate('101')
      const data2 = await this.getDate('102')
      const data3 = await this.getDate('201')
      const data4 = await this.getDate('202')
      this.phoneTimeOptions = this.formatDate(data1)
      this.familyTimeOptions = this.formatDate(data2)
      this.feeTimeOptions = this.formatDate(data3,1)
      this.economyTimeOptions = this.formatDate(data4)
      this.phoneTimeValue = this.phoneTimeOptions[0].value
      this.familyTimeValue = this.familyTimeOptions[0].value
      this.feeTimeValue = this.feeTimeOptions[0].value
      this.economyTimeValue = this.economyTimeOptions[0].value

    },
    async getDate(id){
      const {data} = await getServeData({
        restSerialNo:'m49CE3VM',
        // statType:i ? '2' : this.timeType,
        statType: this.timeType,
        targetId:id
      })
      return data      
    },
    formatDate(data,i){
      const arr = []
      if(this.timeType == 1){
        // 月
        data.forEach(element => {
          const item = {
            value:element.statDate,
            label:element.statDate
          }
          arr.push(item)

        });
      }else if(this.timeType == 3 && i){
        // 月周
        data.forEach((element,index) => {
            const y =  element.statDate.slice(0,4)
            const m =  element.statDate.slice(5,7) > 10 ? element.statDate.slice(5,7) : element.statDate.slice(6,7)
            const w =  element.statDate.slice(9,10) 
            const item = {
              value:element.statDate,
              label:`${y}年${m}月第${w}周`
            }
            arr.push(item)
        });
      }else if(this.timeType == 3){
        // 年周
        data.forEach((element,index) => {
          const y = element.startTime.slice(0,4)
          const date = element.startTime.replace('-','/').replace('-','/') + '-' + element.endTime.replace('-','/').replace('-','/')
          const item = {
            value:element.statDate,
            label:`${y}年第${element.statDate}周 (${date})`
          }
          arr.push(item)
        });
      }
      return arr
    },
    // 获取全部数据
    async getData(date){
      const {data} = await getServeData({
        restSerialNo:'ib24N4YJ',
        statType:this.timeType,
        statDate:date
      })
      return data
    },
    // 下钻
    moreDetail(i){
      console.log(i);
      i.level = '服务分项情况'
      sessionStorage.setItem('menuUrl','/examine-index/serveDetail')
      localStorage.setItem('detailItem',JSON.stringify(i))
      this.$router.push({
        name:'ServeDetail',
        params:i
      })
    }

    
  },
  watch: {
    async phoneTimeValue(){
      this.phoneLoading = true
      const data = await this.getData(this.phoneTimeValue)
      this.phoneList = data[0].childs[0].childs
      this.phoneList.forEach(element => {
        element.parentId = data[0].childs[0].targetId
        // element.timeType = this.timeType
        // element.timeValue = this.phoneTimeValue
        element.currentLevel = 1
        element.firstTitle = "集团考核指标视图"
        if (element.trend.length != 0) {
          element.trend.forEach(item => {
            item.parentId = data[1].childs[0].targetId
            item.timeType = this.timeType
            item.timeValue = this.feeTimeValue
            item.currentLevel = 1
            item.firstTitle = "集团考核指标视图"
          })
        }
      })
      this.phoneLoading = false
      

    },
    async familyTimeValue(){
      this.familyLoading = true
      const data = await this.getData(this.familyTimeValue)
      this.familyList = data[0].childs[1].childs
      this.familyList.forEach(element => {
        element.parentId = data[0].childs[1].targetId
        element.currentLevel = 1
        element.firstTitle = "集团考核指标视图"
        if (element.trend.length != 0) {
          element.trend.forEach(item => {
            item.parentId = data[1].childs[0].targetId
            item.timeType = this.timeType
            item.timeValue = this.feeTimeValue
            item.currentLevel = 1
            item.firstTitle = "集团考核指标视图"
          })
        }
      })
      this.familyLoading = false

    },
    async feeTimeValue(){
      this.feeLoading = true
      const data = await this.getData(this.feeTimeValue)
      this.feeList = data[1].childs[0].childs
      this.feeList.forEach(element => {
        element.parentId = data[1].childs[0].targetId
        element.currentLevel = 1
        element.firstTitle = "集团考核指标视图"
        if (element.trend.length != 0) {
          element.trend.forEach(item => {
            item.parentId = data[1].childs[0].targetId
            item.timeType = this.timeType
            item.timeValue = this.feeTimeValue
            item.currentLevel = 1
            item.firstTitle = "集团考核指标视图"
          })
        }
      })
      this.feeLoading = false

    },
    async economyTimeValue(){
      this.economyLoading = true
      const data = await this.getData(this.economyTimeValue)
      this.economyList = data[1].childs[1].childs
      this.economyList.forEach(element => {
        element.parentId = data[1].childs[1].targetId
        element.currentLevel = 1
        element.firstTitle = "集团考核指标视图"
        if (element.trend.length != 0) {
          element.trend.forEach(item => {
            item.parentId = data[1].childs[0].targetId
            item.timeType = this.timeType
            item.timeValue = this.feeTimeValue
            item.currentLevel = 1
            item.firstTitle = "集团考核指标视图"
          })
        }
      })
      this.economyLoading = false

    },
    timeType(){
      this.initDate()
    }
  },
};
</script>
<style>
.el-select-dropdown__wrap {
  margin-bottom: 0 !important;

}
/* .el-select-dropdown{
  position:relative !important;
  display: block;

} */
</style>

<style lang='less'>
  .month-serve {
    .nav-box{
      display: flex;
      width: 100%;
      height: 552px;
      .SubTitle{
          margin-top: 26px;
          width: auto;
          overflow: hidden;
          height:100%;
          display: flex;
          flex-direction: column;
          padding:0px;
          li {
              display: inline-block;
              width:auto;
              max-width: 200px;
              font-family: SourceHanSansSC-Regular;
              font-size: 20px;
              color: rgba(0,0,0,.7);
              cursor: pointer;
              line-height:40px;
              transition: all .3s linear;
              position:relative;
              transition: all 0.3s linear;
              &::after {
                  content:'';
                  height: 3px;
                  width:0px;
                  background: #ff9900;
                  position:absolute;
                  bottom:0px;
                  left:0px;
                  transition: all 0.3s linear;
              }
          }
          .active{
            font-family: SourceHanSansSC-Medium;
              font-size: 20px;
              color: #262626;
              -border-bottom: 3px solid #ff9900;
              &::after {
                  width:90px;
              }
          }
      }

      .content{
        width: 100%;
        height: 100%;
        padding: 0;
        display: flex;
        justify-content: space-between;
        
        .content-type{
          background-color: white;
          width: 49%;
          height: 100%;
          padding: 20px;
          box-sizing: border-box;
          .content-head{
            width: 100%;
            display: flex;
            // padding: 20px;
            padding-right: 20px;
            justify-content: space-between;
            .content-title{
              font-size: 18px;
              font-weight: 500;
              font-family: SourceHanSansSC-Regular;
              font-size: 20px;
              color: #262626;
            }
          }
        }
      }
    }
  }
  .year-type{
    width:200px !important;
  }

  .week-type{
    width:300px !important;
  }

  
</style>
