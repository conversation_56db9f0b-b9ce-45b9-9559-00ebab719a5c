<template>
  <div class="month-report">
    <div class="report-header">
      <el-select
        v-model="monthValue"
        style="margin-right: 10px"
        placeholder="请选择"
      >
        <el-option
          v-for="item in monthOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-button type="primary" size="small" @click="checkDate">查询</el-button>
      <el-button type="default" size="small" @click="reset">重置</el-button>
    </div>

    <div class="report-content">
      <p style="font-size: 18px">客户满意度月报</p>

      <el-table
        v-loading="loading"
        border
        :span-method="objectSpanMethod"
        :cell-class-name="formatterCellClass"
        :header-cell-class-name="formatterCellClass"
        :data="tableData"
      >
        <el-table-column label="" prop="firstType" align="center" />

        <el-table-column label="" prop="secondType" align="center" />

        <el-table-column label="考核指标" prop="targetName" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.targetName">{{ scope.row.targetName }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="月均" prop="avgScore" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.avgScore != '-'">{{
              Number(scope.row.avgScore).toFixed2(2)
            }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column
          v-for="(item, index) in columns"
          v-if="columns.length"
          :key="item.id"
          :label="item.date.slice(5, 7) + '月'"
          :prop="item.date"
          :formatter="formatCol"
          align="center"
        >
          <template slot-scope="scope">
            <div
              v-if="scope.row[item.date]"
              style="display: inline-block; width: 45px"
            >
              {{ Number(scope.row[item.date]).toFixed2(2) }}
            </div>
            <div v-else width="60px">-</div>
            <span
              v-if="
                (scope.row.scoreType == '0' || scope.row.scoreType == '1') &&
                  index == columns.length - 1 &&
                  index != 0
              "
            >
              <img
                :src="scope.row.scoreType == '0' ? upImg : downImg"
                width="12"
                style="margin-left: 15px"
              >
            </span>
          </template>
        </el-table-column>
        <el-table-column label="趋势" align="center">
          <template slot-scope="scope">
            <div
              :ref="'echarts' + scope.row.num"
              style="height: 55px; width: 100px"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getUniformedWarning } from '../../../../api/teleservice/api'

export default {
  name: 'MonthReport',
  components: {},
  props: {},

  data() {
    return {
      tableData: [
        { secondType: '1', num: 1, targetName: 1, avgScore: 1, firstType: 1 }
      ],
      monthOptions: [],
      monthValue: '',
      columns: [],
      loading: false,
      upImg: require('@/assets/images/up-icon.png'),
      downImg: require('@/assets/images/down-icon.png')
    }
  },
  computed: {},

  watch: {},
  mounted() {
    this.initDate()
  },
  methods: {
    formatterCellClass({ row, column, rowIndex, columnIndex }) {
      if (columnIndex > 2) {
        return 'border-right'
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        if (row.colNum0 || row.colNum1) {
          return {
            rowspan: columnIndex === 0 ? row.colNum0 : row.colNum1,
            colspan: 1
          }
        } else {
          return {
            rowspan: 1,
            colspan: 0
          }
        }
      }
    },
    formatCol(row, column, cellValue, index) {
      if (cellValue == undefined) {
        return '-'
      } else {
        return cellValue
      }
    },
    async initDate() {
      // 获取月份下拉框值
      setTimeout(() => {
        this.tableData.forEach((e) => {
          const myChart = this.$echarts.init(this.$refs['echarts' + e.num])
          myChart.setOption({
            grid: {
              left: 10,
              top: 10,
              right: '0',
              bottom: -10,
              containLabel: true
            },
            xAxis: {
              type: 'category',
              // 不显示x轴线
              show: false
            },
            yAxis: {
              type: 'value',
              show: false
            },
            series: [
              {
                data: [3, 4, 5, 4, 5, 6, 3],
                // 单独修改当前线条的颜色
                lineStyle: {
                  normal: {
                    color: 'rgba(255,153,0,1)',
                    width: 1
                  }
                },
                areaStyle: {
                  color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(255, 153,0,0.8)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 153,0,0.1)'
                    }
                  ])
                },
                type: 'line',
                smooth: true,
                symbol: 'none'
              }
            ]
          })
          window.addEventListener('resize', () => {
            myChart.resize()
          })
        })
      }, 1000)

      const { data } = await getUniformedWarning({
        restSerialNo: 'group-view-allmonthlist'
      })
      const arr = []
      data.data.forEach((item) => {
        const label = item.stat_date.replace('-', '年') + '月'
        const i = {
          label,
          value: item.stat_date
        }
        arr.push(i)
      })
      this.monthOptions = arr
      this.monthValue = arr[0].value
      this.initMonth()
    },
    // 获取月报数据
    async initMonth() {
      this.loading = true
      const { data } = await getUniformedWarning({
        restSerialNo: 'group-view-month',
        opTime: this.monthValue
      })
      const arr = []
      const columns = []
      const dates = []
      data.children.forEach((item1) => {
        let columns0 = 0
        if (item1.children && item1.children.length == 0) {
          columns0 += 1
        }
        item1.children.forEach((child) => {
          columns0 += child.children.length
          if (child.children && child.children.length == 0) {
            columns0 += 1
          }
        })
        item1.children.forEach((item2, idx2) => {
          // 初始化数据，colNum0 != 0 时合并第一列
          let item = {
            colNum0: idx2 == 0 ? columns0 : 0,
            firstType: item1.name,
            secondType: item2.name,
            targetId: item2.code
          }
          if (item2.children.length !== 0) {
            item2.children.forEach((item3, idx3) => {
              const itemDates = []
              // columns为返回的数据列数，根据columns的值渲染几列数据
              item3.trend.forEach((trend) => {
                if (!dates.includes(trend.stat_date)) {
                  const element = {
                    date: trend.stat_date,
                    id: parseInt(Math.random() * 100 + Math.random() * 100)
                  }
                  columns.push(element)
                  dates.push(trend.stat_date)
                }
                itemDates.push(trend.stat_date)
              })
              // 初始化数据，colNum1 != 0 时合并列第二列
              item = {
                colNum0: idx2 == 0 && idx3 == 0 ? columns0 : 0,
                firstType: item1.name,
                secondType: item2.name,
                colNum1: idx3 == 0 ? item2.children.length : 0,
                targetName: item3.target_name,
                targetId: item3.target_id,
                avgScore: item3.avg_score,
                scoreType: item3.score_type
              }
              itemDates.forEach((date, idx) => {
                item[date] = item3.trend[idx].score
              })
              arr.push(item)
            })
          } else {
            item.colNum1 = 1
            arr.push(item)
          }
        })
      })
      this.columns = columns
      this.tableData = arr
      this.loading = false
    },
    // 点击查询
    checkDate() {
      this.initMonth()
    },
    // 重置
    reset() {
      this.monthValue = this.monthOptions[0].value
      this.initMonth()
    }
  }
}
</script>

<style lang="less">
.month-report {
  .report-header {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 20px 0;

    h4 {
      -color: #ffa51f;
      font-size: 16px;
      font-family: SourceHanSansSC-Normal;
      font-weight: normal;
    }
  }

  .report-content {
    width: 100%;
    background: #fff;
    padding: 24px;
    box-sizing: border-box;
  }
}
.border-right {
  border-right: none !important;
}
.el-select-dropdown__item.selected {
  color: rgb(255, 153, 0);
  font-weight: bold;
}
.el-select .el-input.is-focus .el-input__inner {
  border-color: rgb(255, 153, 0);
}
.el-select .el-input__inner:focus {
  border-color: rgb(255, 153, 0);
}
</style>
