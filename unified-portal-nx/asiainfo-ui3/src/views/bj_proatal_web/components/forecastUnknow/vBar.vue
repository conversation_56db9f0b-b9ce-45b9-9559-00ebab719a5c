<template>
	<div class="vBar">
		<!-- <h2 class="title">{{date+title}}</h2> -->
		<h2 class="title">{{title}}</h2>
		<div style="width:180px; position: absolute; right:20px; top:60px; display:flex; justify-content:space-around; color:#999; font-size:12px;">
			<span v-if="flag" style="width:100px;display:block;text-align:center;">预警用户数</span>
			<span v-else style="width:100px;display:block;text-align:center;">投诉量</span>
			<span v-if="flag" style="width:80px;display:block;text-align:center;">占比</span>
		</div>
		<div style="width:100%; flex-grow:2; display:flex; flex-direction:column; justify-content: space-around;">
			<div v-for="(item,index) in datas" style="width:100%; display:flex; align-items:center;">
				<div style="display:block; font-size:14px; overflow: hidden; text-overflow:ellipsis; white-space: nowrap;margin-right:10px;"
				:style="{minWidth: minWidth}">{{item.name  || item.detailName}}</div>

				<div style="flex-grow:2;height:16px; background:rgba(242,242,242,1); display:block; position:relative;z-index: 99;">
					<span :style="{width:flag ? item.percent+'%' : item.num+'%',background:barColor}" class="data-bar">
						<span class="bar-line-top" :style="{background:barColor}"></span>
						<span class="bar-line-bottom"  :style="{background:barColor}"></span>
					</span>
				</div>

				<div style="font-size:14px;margin-left: 24px; display:flex; width: 180px; align-items:center;">
					<span v-if="flag" style="width:100px;display:block;text-align:center;">{{item.num}}个</span>
					<span v-else style="width:100px;display:block;text-align:right;">{{item.num}}</span>
					<span v-if="flag" style="width:10px;display:block;color:#ccc">|</span>
					<span v-if="flag" style="width:80px;display:block;text-align:center;">{{item.percent}}%</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
    import Vue from 'vue';
    import * as echarts from "echarts";

    export default {
        name:"Pie",
        props:{
        	date:String,
        	title:String,
        	legendTop:String,
        	datas:{
        		type: Array
        	},
        	barColor:String,
        	minWidth:String,
			flag:Boolean
        },
        data(){
            return {
                chart:null,
                colors:["#39f","#85c285","#ffc266","#ffa93b","#ff8585"]
            }
        },
        watch: {
        },
        computed:{


        },
        created(){
            
        },
        mounted(){

        },
        methods:{

        }
    };
</script>

<style lang="less" scoped>
	.vBar {
		width:100%; height:100%;
		display: flex;
		flex-direction: column;
		padding: 12px 20px;
		position: relative;

		.title {
			font-weight: normal;
			margin-top: 0px;
			margin-bottom: 0px;
			font-size: 16px;
			font-family: 'SourceHanSansSC-Normal';
			min-height:60px;
			margin:0px;
		}

		.data-bar{
			height:16px; 
			display:inline-block;
			position: relative;
			top:0px;
			left:0px;
			z-index: 99;
		}

		.bar-line-top {
			display: inlin-block;
			height: 2px;
			width: 1px;
			display: block;
			position: absolute;
			right:0px;
			top:-2px;
			z-index: 8;
		}


		.bar-line-bottom {
			display: inlin-block;
			height: 2px;
			width: 1px;
			display: block;
			position: absolute;
			right:0px;
			bottom:-2px;
			z-index: 8;
		}
	}

	


</style>