<template>
	<div class="map">
		<div class="top">
			<!-- <h2 class="title">{{date+title}}</h2> -->
			<h2 class="title">{{title}}</h2>
			<div style="width:100%; display:flex; font-size:14px">
				<div style="margin-right: 70px;">
					<!-- <p style="margin:10px 0; margin-bottom:4px;">预警客户数： <span style="font-size:28px;">123.9&nbsp;</span>万</p> -->
					<p style="margin:10px 0; margin-bottom:4px;">预警客户数： <span style="font-size:28px;">{{ customerData.customerNum}}&nbsp;</span>{{ customerData.unit }}</p>
					<p style="font-size:14px;margin-top:0px; display:flex;align-items:center;">
						<span style="margin-right:30px;">占比：{{ customerData.customerPercent}}%</span>
						<span style="display:flex; align-items:center;">环比：
							<!-- <img :src="require('../../assets/img/triagle.png')" style="margin-right:8px;" />
							<span style="color: rgb(51, 153, 51);">2.33PP</span> -->
                            <!-- <span>-0pp</span> -->
							<span>{{ customerData.customerProportion}}pp</span>
						</span>
					</p>
				</div>

				<div>
					<p style="margin:10px 0;margin-bottom:4px;">预警渠道数： <span style="font-size:28px;">{{ channelData.channelNum}}&nbsp;</span>{{ channelData.unit }}</p>
					<p style="font-size:14px;margin-top:0px; display:flex;align-items:center;">
						<span style="margin-right:30px;">占比：{{ channelData.channelPercent}}%</span>
						<span style="display:flex; align-items:center;">环比：
							<!-- <img :src="require('../../assets/img/triagle.png')" style="margin-right:8px;" />
							<span style="color: rgb(51, 153, 51);">2.33PP</span> -->
                            <span>{{ channelData.channelProportion}}pp</span>
						</span>
					</p>
				</div>

			</div>
			<!-- <p class="desc">本月不知情定制行为预警客户数123.9万，占北京在网用户总数的4.97%，环比下降-0pp，预警客户数top5地市为城二、城一、城三、房山、通州。</p> -->
			<p class="desc">本月不知情定制行为预警客户数{{ customerData.customerNum}}{{ customerData.unit }}，占北京在网用户总数的{{ customerData.customerPercent}}%，
				预警客户数top5地市为{{ topCity }}。</p>
		</div>

		<div class="bottom">
			<div ref="chart" class="chart"  style="width:50%; height:450px;"></div>
			<!-- <div class="legend" style="overflow: hidden; row-gap: 30px;"> -->
			<el-scrollbar class="legend" ref="elscrollbar" >
				<div 
				@mouseover="onMouseover" 
                @mouseout="onMouseout"
				v-for="(item,index) in mapDesData"
				style="width:100%;  height:138px; min-height:138px; padding: 10px; box-sizing:border-box;display:flex; flex-direction:column; justify-content:space-around;color:#666; font-size:14px; color:#262626">
					<div style="font-size: 16px;color:#262626;display:flex; align-items:center;">
						<img style='margin-right:12px; width:16px; height:16px;' :src="require('../../assets/img/fuhao.png')" />{{item.department}}
					</div>
					<div style="display:flex; align-items:center; justify-content:space-between">
						<div>
							<i style='display:inline-block; width:2px; height: 12px; background:#FF9900; margin-left:2px; margin-right:12px;'></i>
							预警客户数： <span style="font-size:24px">{{item.custom.number}}&nbsp;</span>
						</div>
						<div style="display:flex;align-items:center; margin-top:8px;">
							<span style="margin-right:20px;">占比：{{item.custom.percentage}}%</span>
							<span style="display:flex; align-items:center;">环比：
                                <Arrow :num="Number(item.custom.relativePer)"/>
								<!-- <img :src="require('../../assets/img/triagle.png')" style="margin-right:8px;" />
								<span style="color: rgb(51, 153, 51);">{{item.custom.relativePer}}PP</span> -->
							</span>
						</div>
					</div>

					<div style="display:flex; align-items:center; justify-content:space-between">
						<div>
							<i style='display:inline-block; width:2px; height: 12px; background:#FF9900; margin-left:2px; margin-right:12px;'></i>
							预警渠道数： <span style="font-size:24px">{{item.channel.number}}&nbsp;</span>
						</div>
						<div style="display:flex;align-items:center; margin-top:8px;">
							<span style="margin-right:20px;">占比：{{item.channel.percentage}}%</span>
							<span style="display:flex; align-items:center;">环比：
								<!-- <img :src="require('../../assets/img/triagle.png')" style="margin-right:8px;" />
								<span style="color: rgb(51, 153, 51);">{{item.channel.relativePer}}PP</span> -->
								<Arrow :num="Number(item.channel.relativePer)"/>
							</span>
						</div>
					</div>
				</div>
			</el-scrollbar>
			<!-- </div> -->
		</div>
	</div>
</template>

<script>
    import Vue from 'vue';
    import * as echarts from "echarts";
    import Arrow from "../../components/common/arrow";
    const beijingJson = require("../../assets/beijing.json");

    // const beijingSvg = require("../../assets/bj.svg");
    const beijingSvg = require("../../assets/beijing.svg");

    const fuhao = require("../../assets/img/fuhao.png");
    const triagle = require("../../assets/img/triagle.png");

    export default {
        name:"Map",
        components:{
            Arrow
        },
        props:{
        	date:String,
        	title:String,
        	legendTop:String,
        	datas:{
        		type: Array,
        		default:()=>{
        			return [{
                            name:"顺义",
                            value: 5337,
                            customPer:'97.14%',
                            channelNum: 15,
                            channelPer:'93.75%'
                        },{
                            name:"延庆",
                            value: 381,
                            channelNum:7,
                            customPer:'78.72%',
                            channelPer:'100%'
                        },{
                            name:"通州",
                            value: 8105,
                            channelNum: 16,
                            customPer:'100%',
                            channelPer:'97.59%'
                        },{
                            name:"昌平",
                            value: 6546,
                            channelNum: 21,
                            customPer:'100%',
                            channelPer:'92.3%'

                        },{
                            name:"平谷",
                            value:1762,
                            channelNum:7,
                            customPer:'100%',
                            channelPer:'92.3%'

                        },{
                            name:"怀柔",
                            value: 1028,
                            channelNum:5,
                            customPer:'100%',
                            channelPer:'93.45%'
                        },{
                            name:"密云",
                            value: 1290,
                            channelNum:7,
                            customPer:'100%',
                            channelPer:'92.94%'
                        },{
                            name:"房山",
                            value: 11586,
                            channelNum:13,
                            customPer:'100%',
                            channelPer:'97.76%'
                        },{
                            name:"大兴",
                            value: 6495,
                            channelNum: 17,
                            customPer:'100%',
                            channelPer:'97.49%'
                        },{
                            name:"分公司不详",
                            value: 338622,
                            channelNum: 1,
                            customPer:'100%',
                            channelPer:'99.72%'
                        },{
                            name:"电子商务中心",
                            value: 6621,
                            channelNum: 1,
                            customPer:'100%',
                            channelPer:'100%'
                        },{
                            name:"客户服务部",
                            value: 2,
                            channelNum: 1,
                            customPer:'100%',
                            channelPer:'78.72%'
                        },{
                            name:"数字家庭中心",
                            value: 1,
                            channelNum: 1,
                            customPer:'100%',
                            channelPer:'78.72%'
                        },{
                            name:"客户服务中心",
                            value: 10073,
                            channelNum: 1,
                            customPer:'100%',
                            channelPer:'78.72%'
                        },{
                            name:"政企客户中心",
                            value: 1156,
                            channelNum: 1,
                            customPer:'100%',
                            channelPer:'78.72%'
                        },{
                            name:"平台生态部",
                            value: 21521,
                            channelNum: 1,
                            customPer:'100%',
                            channelPer:'78.72%'
                        },{
                            name:"市场经营部",
                            value: 680599,
                            channelNum: 2,
                            customPer:'100%',
                            channelPer:'96.09%'
                        },{
                            name:"信息系统部",
                            value: 113241,
                            channelNum: 2,
                            customPer:'100%',
                            channelPer:'99.52%'
                        },{
                            name:"城一",
                            value: 14959,
                            channelNum: 34,
                            customPer:'100%',
                            channelPer:'96.01%'
                        },{
                            name:"城二",
                            value: 19838,
                            channelNum: 37,
                            customPer:'100%',
                            channelPer:'94.33%'
                        },{
                            name:"城三",
                            value: 14413,
                            channelNum: 47,
                            customPer:'100%',
                            channelPer:'95.51%'
                        }]
	        	}
        	},
			mapData:Array,
			customerData:Object,
			channelData:Object,
			topCity:String,
			topValue:Array,
			mapDesData:Array
        },
        data(){
            return {
            	animationId:null,
                chart:null,
                // colors:["#39f","#85c285","#ffc266","#ffa93b","#ff8585"],
                // mapDesData:[
                // 	{
                // 		department:"电子商务中心",
                // 		custom:{
                // 			number:6621,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		},
                // 		channel:{
                // 			number:1,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		}

                // 	},{
                // 		department:"客户服务部",
                // 		custom:{
                // 			number:2,	
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		},
                // 		channel:{
                // 			number:1,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		}

                // 	},{
                // 		department:"数字家庭中心",
                // 		custom:{
                // 			number:1,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		},
                // 		channel:{
                // 			number:1,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		}

                // 	},{
                // 		department:"客户服务中心",
                // 		custom:{
                // 			number:10073,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		},
                // 		channel:{
                // 			number:1,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		}

                // 	},{
                // 		department:"政企客户中心",
                // 		custom:{
                // 			number:1156,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		},
                // 		channel:{
                // 			number:1,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		}

                // 	},{
                // 		department:"平台生态部",
                // 		custom:{
                // 			number:21521,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		},
                // 		channel:{
                // 			number:1,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		}

                // 	},{
                // 		department:"市场经营部",
                // 		custom:{
                // 			number:680599,
	            //     		percentage:96.09,
	            //     		relativePer: 0,
                // 		},
                // 		channel:{
                // 			number:2,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		}

                // 	},{
                // 		department:"信息系统部",
                // 		custom:{
                // 			number:113241,
	            //     		percentage:99.52,
	            //     		relativePer: 0,
                // 		},
                // 		channel:{
                // 			number:2,
	            //     		percentage:100,
	            //     		relativePer: 0,
                // 		}

                // 	}
                // ]
            }
        },
        watch: {
			mapData(){
				this.initChart()
			}
        },
        computed:{


        },
        created(){
            
        },
        mounted(){
			setTimeout(() => {
				this.initChart();
			}, 2000);
            

            this.animationId = requestAnimationFrame(this.autoScroll);

			/*var arr2 = arr.map(item=>{
				return item.toString();
			})

			var set = new Set(arr2);

			var newArr = []

			set.forEach(item=>{
				var arr = item.split(',');
				arr[0] = Number(arr[0]);
				arr[1] = Number(arr[1]);
				newArr.push(arr);
				
			})

			console.log(newArr)*/
        },
        methods:{
        	initChart(){
				if(this.chart !== null && this.chart !== '' && this.chart !== undefined){
					this.chart.dispose()
				}
        		var _this = this;

	            $.get(beijingSvg,  function(svg) {
	            	svg.getElementsByTagName('svg')[0].setAttribute("width", _this.$refs.chart.clientWidth+'px');
	            	svg.getElementsByTagName('svg')[0].setAttribute("height", _this.$refs.chart.clientHeight+20+'px');
	            	svg.getElementsByTagName('svg')[0].setAttribute("viewBox", "890 410 490 460");

	            	setTimeout(()=>{
	            		echarts.registerMap('beijing', {svg: svg});
					    // let svg = document.getElementsByTagName('svg');
					    _this.chart = echarts.init(_this.$refs.chart, null, {
			                render: "svg"
			            });
			        	_this.updateOption();
			            window.addEventListener("resize", ()=>{
                            svg.getElementsByTagName('svg')[0].setAttribute("width", _this.$refs.chart.clientWidth+'px');
                            svg.getElementsByTagName('svg')[0].setAttribute("height", _this.$refs.chart.clientHeight+20+'px');
                            svg.getElementsByTagName('svg')[0].setAttribute("viewBox", "890 410 490 460");
                            echarts.registerMap('beijing', {svg: svg});
			                _this.chart.resize();
			            });

	            	},1000)
				    
				});

				/*echarts.registerMap('beijing', beijingJson);

			    _this.chart = echarts.init(_this.$refs.chart, null, {
	                render: "svg"
	            });

	            _this.updateOption();

	            window.addEventListener("resize", ()=>{
	                _this.chart.resize();
	            });*/
	            
	        },

	        updateOption(){
	        	let _this = this;

				let piecesColor = []

				if(this.topValue[4]===this.topValue[0]){
					piecesColor = [{
		                label: '极少',
		                color: '#CCE4FD'
		            }]
					
				}else{
					piecesColor = [
		                    {
		                        lt: this.topValue[4],
		                        label: '极少',
		                        color: '#CCE4FD'
		                    },{
		                        gte: this.topValue[4],
		                        lt: this.topValue[3],
		                        label: '很少',
		                        color: '#FFEBCC'
		                    },{
		                        gte: this.topValue[3],
		                        lt: this.topValue[2],
		                        label: '少',
		                        color: '#FFD699'
		                    },{
								gte: this.topValue[2],
		                        lt: this.topValue[1],
		                        label: '中等',
		                        color: '#FFC266'
							},{
								gte: this.topValue[1],
		                        lt: this.topValue[0],
		                        label: '多',
		                        color: '#FFAD33'
							},{
								gte: this.topValue[0],
		                        label: '很多',
		                        color: '#FF9900'
							}
		                ]

				}

	        	let option = {
	        		grid: [
			            {
			               left: '0',
			                right: 0,
			                top: 10,
			            }
			        ],
	        		tooltip: {
						show:true,
				        padding: 0,
				        enterable: true,
				        transitionDuration: 1,
				        textStyle: {
				            color: '#000',
				            decoration: 'none'
				        },
				        borderColor:"rgba(255,255,255,0)",
				        backgroundColor:'rgba(255,255,255,0.3)',
				        formatter: function(params) {
				        	if(params.componentSubType=="map"){
				        		var tipHtml = '';
					            tipHtml = `<div style="height:auto;width:416px;border-radius:4px;background:rgba(255,255,255,0.9);box-shadow:0 4px 11px 1px rgba(99,99,99,0.19); padding:10px 14px 0px 14px;  box-sizing:border-box; display:flex; flex-direction:column; justify-content: space-around;font-family: SourceHanSansSC-Regular;">

					            		<div style='margin:0px; font-size:16px; display:flex; align-items:center;'>
					            			<img style='margin-right:12px;' src="${fuhao}" />${params.name}
					            		</div>
					            		<div style='display:flex; align-items:center; justify-content:space-between; font-size:14px;'>

					            			<div style=' display:flex; align-items:center;'>
					            				<p style='position:relative'>
						            				<i style='display:inline-block; width:2px; height: 12px; background:#FF9900; position:absolute; top:9px;'></i>
						            				<span style='margin-left:16px'>预警客户数：</span>
						            				<span style='font-size:24px;'>${params.value}</span>&nbsp;&nbsp;个
					            				</p>
			            					</div>

			            					<div style=" display:flex;align-items:center;">
												<span style="margin-right:10px;">占比：${params.data.customPer}%</span>
												<span style="display:flex; align-items:center;">环比：
													
													<span>${params.data.customerProportion}pp</span>
												</span>
											</div>
				            			</div>

				            			<div style='display:flex; align-items:center; justify-content:space-between; font-size:14px;'>

					            			<div style=' display:flex; align-items:center;'>
					            				<p style='position:relative'>
						            				<i style='display:inline-block; width:2px; height: 12px; background:#FF9900; position:absolute; top:9px;'></i>
						            				<span style='margin-left:16px'>预警渠道数：</span>
						            				<span style='font-size:24px;'>${params.data.channelNum}</span>&nbsp;&nbsp;个
					            				</p>
			            					</div>

			            					<div style=" display:flex;align-items:center;">
												<span style="margin-right:10px;">占比：${params.data.channelPer}%</span>
												<span style="display:flex; align-items:center;">环比：
												
													<span>${params.data.channelProportion}pp</span>
												</span>
											</div>
				            			</div>
					                </div>`;
					            return tipHtml;
				        	}
				        }
				    },


				    visualMap: {
		                type: 'piecewise',
		                show: false,
		                pieces: piecesColor,
		                color: '#fff',
		                textStyle: {
		                    color: '#000',
		                },
		                visibility: 'off'
		            },

		            

        		
					geo: [{
						map: "beijing",
						name:"beijing",
						zoom: 1,
						roam: false,
						silent: false,
                        selectedMode: false,
                        select:{
                            label:{
                                show: false
                            }
                        },
						itemStyle: {
							normal: {
								areaColor: '#CCE4FD',
								borderColor: '#FFFFFF',
								borderWidth: 1
							}
						},
						zlevel:8,
						label: {
							show: true,
							textStyle: {
								color: "#333",
								fontSize:10
							},
                            formatter:'{a}',
							// position: [10, 10],
							emphasis:{
								show: true
							}
						}			
					}],
					series: [
						{
							type: "map",
							nameProperty:"beijing",
							geoIndex:0,
                            selectedMode: false,
							label: {
								show: true,
								textStyle: {
									color: "#1DE9B6",
								},
							},
							itemStyle: {
								borderColor: "#8dc0e8",
								borderWidth: 1,
								areaColor: "#97ddfa"
							},
							zoom: 1,
							map: "beijing", //使用
							roam: false,
							silent: false,
							zlevel: 1,
							data: _this.mapData,
						}
					]
	            }
	        	this.chart.setOption(option);
	        },

	        autoScroll(){
		      var el = this.$refs['elscrollbar'].$refs['wrap'];
		      if(el.scrollTop==0&&(el.scrollHeight-el.offsetHeight)-el.scrollTop<50){
		        return;
		      }
		      if(el.scrollTop < el.scrollHeight-el.offsetHeight){
		        el.scrollTop = el.scrollTop+1;
		        this.animationId = requestAnimationFrame(this.autoScroll);
		      } else {
		      	cancelAnimationFrame(this.animationId)
		      	el.scrollTop = 0;
		      	this.animationId = requestAnimationFrame(this.autoScroll);
		      }
		    },

		    onMouseover(e){
		      cancelAnimationFrame(this.animationId)
		    },

		    onMouseout(){
		      this.animationId = requestAnimationFrame(this.autoScroll);
		    },
        }
    };
</script>

<style lang="less" scope>
	.map {
		width:100%; 
		height:100%;
		display: flex; 
		flex-direction: column;
		padding-bottom: 20px;
		box-sizing: border-box;
		font-family: SourceHanSansSC-Regular;
		
		.top {
			width: 100%;
			padding: 0 20px;
			box-sizing: border-box;
		}


		.title {
			font-weight: normal;
			margin-top: 12px;
			margin-bottom: 0px;
			font-size: 16px;
			font-family: SourceHanSansSC-Regular;
		}

		.desc {
			font-size: 14px;
			color: rgba(140,140,140,1);
			margin-top:4px;
		}

		.bottom {
			width: 100%;
			flex-grow: 2;
			display: flex;
			position: relative;
		}

		.chart {
			width:50%; 
			height:100%;
			position: relative;

			&::after {
				content: "";
				display: block;
				width: 1px;
				height:100%;
				background-color: #ccc;
				position: absolute;
				right:0px;
				top:0px;
			};
		}

		.legend {
			width:50%; 
			height:100%;			
			box-sizing: border-box;
			padding-top:0px !important;
			
		}

	}

</style>

<style lang="less">
  .map .el-scrollbar__view {
    height:100%;
    row-gap: 30px;
    padding: 40px 30px;
    box-sizing: border-box;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
  }

  .map .el-scrollbar__wrap{
    overflow-X: hidden;
  }
</style>