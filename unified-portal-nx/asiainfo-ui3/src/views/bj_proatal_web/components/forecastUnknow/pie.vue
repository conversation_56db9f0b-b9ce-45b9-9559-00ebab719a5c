<template>
  <div class="pie">
    <div ref="chart" class="chart" />
    <div class="legend">
      <p v-for="(item,index) in datas" class="legend-item">
        <span style="display:block; width:140px; text-align:left; padding-left: 20px; position:relative"><span class="point" :style="{background:colors[index]}" />{{ item.name }}</span>
        <span style="display:block; width:100px; text-align:left;">{{ item.value }}</span>
        <span style="display:block; width:70px; text-align:left;">{{ item.percent }}</span>
      </p>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'Pie',
  props: {
        	date: String,
        	title: String,
        	legendTop: String,
        	datas: {
        		type: Array
        	}
  },
  data() {
    return {
      chart: null,
      colors: ['#F98781', '#FFA93C', '#FFE36F', '#84C285', '#3454FF', '#3499FF', '#C299FF', '#F25DCD']
    }
  },
  computed: {

  },
  watch: {

        	// datas(val){
        	// 	let total = 0;
        	// 	val.forEach((item)=>{
        	// 		total += parseInt(item.value);
        	// 	})

        	// 	val.forEach((item)=>{
        	// 		item.percentage = (item/total).toFixed2(2);
        	// 	})
        	// }
    datas() {
      this.initChart()
    }
  },
  created() {

  },
  mounted() {
    setTimeout(() => {
      this.initChart()
    }, 1000)
  },
  methods: {
        	initChart() {
      if (this.chart !== null && this.chart !== '' && this.chart !== undefined) {
        this.chart.dispose()
      }
	            this.chart = echarts.init(this.$refs.chart, null, {
	                render: 'svg'
	            })

	            this.updateOption()

	            window.addEventListener('resize', () => {
	                this.chart.resize()
	            })
	        },

	        updateOption() {
	        	let total = 0
        		this.datas.forEach((item) => {
        			total += parseInt(item.value)
        		})

        		this.datas.forEach((item) => {
        			item.percent = ((parseInt(item.value) / total).toFixed(4) * 100).toFixed2(2) + '%'
        		})

	        	const _this = this
	        	const option = {
	                title: {
	                    // text: _this.date+_this.title,
          text: _this.title,
	                    show: true,
	                    textStyle: {
	                        fontSize: 16,
	                        fontWeight: 'normal',
	                        lineHeight: 40,
	                        fontFamily: 'SourceHanSansSC-Regular'
	                    },
	                    left: 10,
	                    top: 0,
	                    subtext: '单位：个',
	                    subtextStyle: {
	                    	color: 'rgb(140,140,140)',
	                    	fontSize: 14,
	                    	lineHeight: 22,
	                    	fontFamily: 'SourceHanSansSC-Normal'
	                    }
	                },
	                tooltip: {
	                    show: true,
	                    trigger: 'item'
	                },
	                legend: {
	                    show: false
	                },
	                color: ['#F98781', '#FFA93C', '#FFE36F', '#84C285', '#3454FF', '#3499FF', '#C299FF', '#F25DCD'],
	                series: [
	                    {
	                        name: '',
	                        type: 'pie',
	                        selectedMode: 'single',
	                        radius: ['38%', '50%'],
	                        center: ['130', '62%'],
	                        label: {
	                            show: false,
	                            position: 'inner',
	                            formatter: '{c}'
	                        },
	                        labelLine: {
	                            show: false
	                        },
	                        data: _this.datas
	                    }
	                ]
	            }
	        	this.chart.setOption(option)
	        }
  }
}
</script>

<style lang="less" scope>
	.pie {
		width:100%; height:100%; position:relative;
	}

	.chart {
		width:100%; height:100%;
	}

	.legend {
		width:50%;
		height:100%;
		position:absolute;
		right:0px;
		top:0px;
		display:flex;
		flex-direction:column;
		justify-content:center;
		padding-top:80px;
		box-sizing: border-box;
		font-size: 12px;
		color: rgb(89,89,89);

		.legend-item {
			display: flex;
			align-items: center;
			margin: 5px 0;
			display: flex;
			justify-content: space-around;
			align-items: center;
		}

		.point {
			width:8px;
			height:8px;
			border-radius:10px;
			display: inline-block;
			margin-right: 6px;
			position: absolute;
			left:0px;
			top: 4px;
		}
	}

</style>
