<template>
	<div class="bar" ref="chart">
	</div>
</template>

<script>
    import Vue from 'vue';
    import * as echarts from "echarts";
    import chartsTitleVue from '../common/chartsTitle.vue';

    export default {
        name:"Pie",
        props:{
			time:String,
        	date:String,
        	title:String,
        	legendTop:String,
        	datas:Array,
        	barColor:{
        		type:Array,
        		default:()=>{
        			return ['#83bff6','#188df0','#188df0']
        		}
        	},
        	minWidth:String,
        	unit:{
        		type:String,
        		default:()=>{
        			return '单位：个'
        		}
        	},
        	names:Array,
        	isNameShow: Boolean
        },
        data(){
            return {
                chart:null
            }
        },
        watch: {
			datas:{
				handler(){
				// 	if(this.time !== ''){
						this.initChart()
				// 	}else{
				// 		return
				// 	}
				},
				deep:true
				
			}
        },
        computed:{
        	xLabel(){
        		let arr = this.datas[0].map(item=>{
        			return item.name
        		})
        		return arr;
        	}
        },
        created(){
            
        },
        mounted(){
			setTimeout(() => {
				this.initChart();
			}, 3500);
        },
        methods:{
        	insert_flg(str, flg, sn) {
		      let newstr = '';
		      let len = 0;
		    len = str.length;
		      for (let i = 0; i < len; i += sn) {
		        const tmp = str.substring(i, i + sn);
		        newstr += tmp + flg;
		      }
		      return newstr;
		    },

        	initChart(){
				if(this.chart !== null && this.chart !== '' && this.chart !== undefined){
					this.chart.clear()
					this.chart.dispose()
				}
	            this.chart = echarts.init(this.$refs.chart, null, {
	                render: "svg"
	            });
	            this.updateOption();

	            window.addEventListener("resize", ()=>{
	                this.chart.resize();
	            });
	        },

	        updateOption(){
	        	let _this = this;
	        	let series = [];
	        	let yAxis = [];

				series.push({
					name: _this.names[0],
					type: 'bar',
					stack: 'Ad',
					barWidth: '16',
					itemStyle: {
				        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					      { offset: 0, color:'#83bff6' },
					    //   { offset: 0.5, color: _this.barColor[1] },
					      { offset: 1, color:'#188df0' }
					    ])
				  	},
					// label: {
    				//   position:'top',
    				//   show: true,
					//   color:'#8c8c8c'
    				// },
					data: _this.datas[0]
				})
				series.push({
					name: _this.names[1],
					type: 'bar',
					stack: 'Ad',
					barWidth: '16',
					itemStyle: {
				        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					      { offset: 0, color: _this.barColor[0] },
					    //   { offset: 0.5, color: _this.barColor[1] },
					      { offset: 1, color: _this.barColor[2] }
					    ])
				  	},
					// label: {
    				//   position:'top',
    				//   show: true,
					//   color:'#8c8c8c'
    				// },
					data: _this.datas[1]
				})
				series.push({
					name: _this.names[2],
					type: 'bar',
					stack: 'Ad',
					barWidth: '16',
					itemStyle: {
				        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					      { offset: 0, color: _this.barColor[1] },
					    //   { offset: 0.5, color: _this.barColor[1] },
					      { offset: 1, color: _this.barColor[3] }
					    ])
				  	},
					// label: {
    				//   position:'top',
    				//   show: true,
					//   color:'#8c8c8c'
    				// },
					data: _this.datas[2]
				})

				const option = {
                    title: {
						text: _this.title,
	                    show: true,
	                    textStyle:{
	                        fontSize:16,
	                        fontWeight:'normal',
	                        lineHeight: 40,
	                        fontFamily: 'SourceHanSansSC-Regular'
	                    },
	                    left:10,
	                    top:0,
	                    subtext:_this.unit,
	                    subtextStyle:{
	                    	fontWeight:'normal',
	                    	color:'rgb(140,140,140)',
	                    	fontSize:14,
	                    	lineHeight:30,
	                    	fontFamily: 'SourceHanSansSC-Normal'
	                    }
	                },
                    tooltip: {
                      trigger: 'axis',
                      axisPointer: {
                        type: 'shadow'
                      }
                    },
                    legend: {
						left:'center',
						top:78,
                    },
                    grid: {
                      top:'35%',
                      left: '3%',
                      right: '4%',
                      bottom: '3%',
                      containLabel: true
                    },
                    xAxis: [
                      {
                        type: 'category',
                        data: _this.xLabel,
						axisTick: {
								show:false,
								alignWithLabel: true
							},
							axisLine:{
								show: true,
								lineStyle:{
									color: '#ccc'
								}
								
							},
							axisLabel: {
								interval: 0,
								color: '#666',
								fontSize:12,
								lineHeight:16,						
								formatter: (value) => _this.insert_flg(value, '\n', 4),
							}							
                      }
                    ],
                    yAxis: [
					  {
                        type: 'value'
                      },
                    ],
                    series:series
                };
				this.chart.clear()
	        	this.chart.setOption(option,true);
	        }
        }
    };
</script>

<style lang="less" scoped>
	.bar {
		width:100%; 
		height:100%;
	}

</style>