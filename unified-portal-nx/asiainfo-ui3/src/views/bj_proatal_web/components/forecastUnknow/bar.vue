<template>
	<div class="bar" ref="chart">
	</div>
</template>

<script>
    import Vue from 'vue';
    import * as echarts from "echarts";
	import chartsTitleVue from '../common/chartsTitle.vue';

    export default {
        name:"Pie",
        props:{
			time:String,
        	date:String,
        	title:String,
        	legendTop:String,
        	datas:Array,
        	barColor:{
        		type:Array,
        		default:()=>{
        			return ['#83bff6','#188df0','#188df0']
        		}
        	},
        	minWidth:String,
        	unit:{
        		type:String,
        		default:()=>{
        			return '单位：个'
        		}
        	},
        	names:Array,
        	isNameShow: Boolean
        },
        data(){
            return {
                chart:null
            }
        },
        watch: {
			time:{
				handler(){
						this.initChart()
				},
				
			}
        },
        computed:{
        	xLabel(){
        		let arr = this.datas[0].map(item=>{
        			return item.name
        		})
        		return arr;
        	}
        },
        created(){
            
        },
        mounted(){
			setTimeout(() => {
				if(this.datas!==undefined){
					this.initChart();
				}
				
			}, 3500);
        },
        methods:{
        	insert_flg(str, flg, sn) {
		      let newstr = '';
		      let len = 0;
		    //   if(str.length>8){
		    //   	len = 8
		    //   } else {
		    //   	len = str.length;
		    //   }
			  len = str.length;
		      for (let i = 0; i < len; i += sn) {
		        const tmp = str.substring(i, i + sn);
		        newstr += tmp + flg;
		      }

		    //   if(str.length>8){
		    //   	newstr += '...'
		    //   }
			
		      return newstr;
		    },

        	initChart(){
				if(this.chart !== null && this.chart !== '' && this.chart !== undefined){
					this.chart.dispose()
				}
	            this.chart = echarts.init(this.$refs.chart, null, {
	                render: "svg"
	            });

	            this.updateOption();

	            window.addEventListener("resize", ()=>{
	                this.chart.resize();
	            });
	        },

	        updateOption(){
	        	let _this = this;
	        	let series = [];
	        	let yAxis = [];
				// 单柱状图时排序
				if (this.datas.length === 1) {
					this.datas[0].sort((a,b)=>{
	        			return b.value-a.value;
	        		})	
				}
	        	if(this.datas.length>=1){
					yAxis.push({
						type: 'value',
						position: 'left',
						name: _this.names[2] ? _this.names[0] + '：个' : _this.names[0],
						nameTextStyle:{
							color:"#999"
						},
						axisTick:{
							show:false
						},
						axisLine:{
							show: false
						},
						splitLine:{
							show: true,
							lineStyle:{
								type: 'dashed',
								color:'#dcdcdc',
								width:0.5
							}
						},
						splitNumber: 6
					})

					series.push({
						name: _this.names[0],
						type: 'bar',
						barWidth: _this.barColor[3]===undefined?'20':'15',
						itemStyle: {
					        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					          { offset: 0, color: _this.datas.length==1?_this.barColor[0]:'#83bff6' },
					          // { offset: 0.5, color: _this.barColor[1] },
					          { offset: 1, color: _this.datas.length==1?_this.barColor[2]:'#188df0' }
					        ]),
				      	},
						label: {
    					  position:'top',
    					  show: true,
						  color:'#8c8c8c'
    					},
						data: _this.datas[0]
					})
	        	} 
	        	if(this.datas.length>=2){

	        		yAxis.push({
						type: 'value',
						position: _this.barColor[3]===undefined?'right':'left',
						name: _this.names[2] ? _this.names[1] + '：人' : _this.names[1],
						nameTextStyle:{
							color:"#999"
						},
					    axisTick:{
							show:false
						},
					    axisLine:{
							show: false
						},
					    splitLine:{
							show: false,
							lineStyle:{
								type: 'dashed',
								color:'#dcdcdc',
								width:0.5
							}
						},
						splitNumber: 6
					})

	        		series.push({
						name: _this.names[1],
						type: 'bar',
						barWidth: _this.barColor[3]===undefined?'20':'15',
						itemStyle: {
					        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					          { offset: 0, color: _this.barColor[0] },
					          // { offset: 0.5, color: _this.barColor[1] },
					          { offset: 1, color: _this.barColor[2] }
					        ]),
				      	},
						label: {
    					  position:'top',
    					  show: true,
						  color:'#8c8c8c'
    					},
				      	yAxisIndex: 1,
						data: _this.datas[1]
					})

					
	        	} 

	        	if(this.datas.length>=3){
					if(_this.barColor[3]===undefined){
							yAxis.push({
							type: 'value',
							position: 'right',
							// name:_this.names[2],
							show:false,
							
						})
						series.push({
							name: _this.names[2],
							type:  'line',
							showSymbol:false,
							itemStyle:{
								color:_this.barColor[2],
							},
							label: {
    						  position:'top',
    						  show: true,
							  color:'#8c8c8c'
    						},
							lineStyle: {
						        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						          { offset: 0, color:_this.barColor[0]},
						          // { offset: 0.5, color: _this.barColor[1] },
						          { offset: 1, color:_this.barColor[2]}
						        ])
				    	  	},
				    	  	yAxisIndex: 2,
							data: _this.datas[2]
						})
					}else{
						yAxis.push({
							type: 'value',
							position: 'right',
							name: '',
							nameTextStyle:{
								color:"#999"
							},
						    axisTick:{
								show:false
							},
						    axisLine:{
								show: false
							},
						    splitLine:{
								show: false,
								lineStyle:{
									type: 'dashed',
									color:'#dcdcdc',
									width:0.5
								}
							},
							splitNumber: 6
						})
						series.push({
							name: _this.names[2],
							type:  _this.barColor[3]===undefined?'line':'bar',
							showSymbol:false,
							barWidth: _this.barColor[3]===undefined?'':'15',
							itemStyle:{
								color: _this.barColor[3]===undefined ? _this.barColor[2] : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						          { offset: 0, color:_this.barColor[1]},
						          { offset: 1, color:_this.barColor[3] }
						        ])
							},
							label: {
    						  position:'top',
    						  show: true,
							  color:'#8c8c8c'
    						},
							lineStyle: {
						        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						          { offset: 0, color:_this.barColor[0]},
						          // { offset: 0.5, color: _this.barColor[1] },
						          { offset: 1, color:_this.barColor[3]}
						        ])
				    	  	},
				    	  	yAxisIndex: 2,
							data: _this.datas[2]
						})

					}
	        		// yAxis.push({
					// 	type: 'value',
					// 	position: _this.barColor[3]===undefined?'right':'left',
					// 	name:_this.barColor[3]===undefined?_this.names[2]:'',
					// 	show:false
					// })

					// console.log('dbhjzbguobhibuguig',this.names,this.barColor[3]);
					
	        	}


	        	let option = {
	        		title: {
	                    // text: _this.date+_this.title,
						text: _this.title,
	                    show: true,
	                    textStyle:{
	                        fontSize:16,
	                        fontWeight:'normal',
	                        lineHeight: 40,
	                        fontFamily: 'SourceHanSansSC-Regular'
	                    },
	                    left:10,
	                    top:0,
	                    subtext:_this.names[2]?'':this.unit,
	                    subtextStyle:{
	                    	fontWeight:'normal',
	                    	color:'rgb(140,140,140)',
	                    	fontSize:14,
	                    	lineHeight:30,
	                    	fontFamily: 'SourceHanSansSC-Normal'
	                    }
	                },
					tooltip: {
						show:false
						// trigger: 'axis',
						// axisPointer: {
						// 	type: 'shadow'
						// },
						// backgroundColor:'rgba(255,255,255,0.8)',
						// textStyle:{
						// 	color:"#666",
						// 	fontSize:14,
						// 	lineHeight:22
						// },
						// borderColor:'#eee',
						// borderWidth:1
					},
					legend:{
						show: _this.isNameShow,
						left:'center',
						top:78,
						/*itemWidth:10,
						itemHeight:10,
						textStyle:{
							color:"#999",
							fontSize:12
						},
						lineStyle:{
							width: 2,

						},*/
						data:_this.names
					},
					grid: {
						top:'35%',
						left: '80',
						right: '5%',
						bottom: 70,
						containLabel: false
					},
					xAxis: [
						{
							type: 'category',
							data: _this.xLabel,
							axisTick: {
								show:false,
								alignWithLabel: true
							},
							axisLine:{
								show: true,
								lineStyle:{
									color: '#ccc'
								}
							},
							axisLabel: {
								interval: 0,
								color: '#666',
								fontSize:_this.datas.length>=8 ? 10 : 12,
								lineHeight:12,								
								formatter: (value) => _this.datas.length==1?_this.insert_flg(value, '\n', 5):value,
							},
						}
					],
					yAxis: yAxis,
					series: series
				};
				this.chart.clear()
	        	this.chart.setOption(option,true);
	        }
        }
    };
</script>

<style lang="less" scoped>
	.bar {
		width:100%; 
		height:100%;
	}

</style>